'use client';

import React, { useRef, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { ChevronDownIcon, PlayIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui';
import { useGSAP } from '@gsap/react';
import { animations } from '@/lib/animations/gsap';
import { fadeInUp, staggerContainer, staggerItem } from '@/lib/animations/framer';

const Hero: React.FC = () => {
  const heroRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    if (titleRef.current) {
      animations.heroTitle(titleRef.current);
    }
    if (subtitleRef.current) {
      animations.heroSubtitle(subtitleRef.current, 0.6);
    }
    if (ctaRef.current) {
      animations.heroSubtitle(ctaRef.current, 1.0);
    }
  }, { scope: heroRef });

  const scrollToNextSection = () => {
    const nextSection = document.getElementById('about');
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section 
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-900"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 hero-pattern"></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary-500/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent-500/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 right-1/3 w-48 h-48 bg-secondary-500/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '4s' }}></div>
      </div>

      <div className="container-custom relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Content */}
          <motion.div
            variants={staggerContainer}
            initial="initial"
            animate="animate"
            className="text-center lg:text-left"
          >
            <motion.div variants={staggerItem}>
              <span className="inline-block px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-white/90 text-sm font-medium mb-6">
                🚀 Professional Web Solutions
              </span>
            </motion.div>

            <motion.h1 
              ref={titleRef}
              variants={staggerItem}
              className="text-5xl lg:text-7xl font-bold text-white mb-6 leading-tight"
            >
              Let's{' '}
              <span className="text-gradient bg-gradient-to-r from-accent-400 to-accent-300 bg-clip-text text-transparent">
                Grow
              </span>{' '}
              Together
            </motion.h1>

            <motion.p 
              ref={subtitleRef}
              variants={staggerItem}
              className="text-xl lg:text-2xl text-white/80 mb-8 leading-relaxed max-w-2xl"
            >
              Professional website development and Meta advertising services that drive results. 
              Transform your digital presence with custom solutions built for growth.
            </motion.p>

            <motion.div 
              ref={ctaRef}
              variants={staggerItem}
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
            >
              <Button 
                href="/contact" 
                variant="primary"
                size="lg"
                className="bg-accent-600 hover:bg-accent-700 text-white px-8 py-4 text-lg"
              >
                Start Your Project
              </Button>
              
              <Button 
                href="/portfolio" 
                variant="outline"
                size="lg"
                className="border-white/30 text-white hover:bg-white/10 px-8 py-4 text-lg backdrop-blur-sm"
              >
                <PlayIcon className="w-5 h-5 mr-2" />
                View Our Work
              </Button>
            </motion.div>

            {/* Stats */}
            <motion.div 
              variants={staggerItem}
              className="grid grid-cols-3 gap-6 mt-12 pt-8 border-t border-white/20"
            >
              <div className="text-center lg:text-left">
                <div className="text-3xl lg:text-4xl font-bold text-white mb-1">50+</div>
                <div className="text-white/70 text-sm">Projects Completed</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-3xl lg:text-4xl font-bold text-white mb-1">4.8x</div>
                <div className="text-white/70 text-sm">Average ROAS</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-3xl lg:text-4xl font-bold text-white mb-1">98%</div>
                <div className="text-white/70 text-sm">Client Satisfaction</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Visual Element */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.5, ease: "easeOut" }}
            className="relative"
          >
            <div className="relative w-full max-w-lg mx-auto">
              {/* Main Device Mockup */}
              <div className="relative bg-white rounded-2xl shadow-2xl p-4 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                <div className="bg-gray-100 rounded-lg aspect-video overflow-hidden">
                  <div className="h-full bg-gradient-to-br from-primary-100 to-accent-100 flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-primary-600 rounded-lg mx-auto mb-4 flex items-center justify-center">
                        <span className="text-white text-2xl">🌐</span>
                      </div>
                      <div className="text-gray-700 font-semibold">Professional Website</div>
                      <div className="text-gray-500 text-sm">Built for Success</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Cards */}
              <motion.div
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                className="absolute -top-4 -left-4 bg-white rounded-lg shadow-lg p-3 border border-gray-200"
              >
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-xs font-medium text-gray-700">Live Website</span>
                </div>
              </motion.div>

              <motion.div
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 1 }}
                className="absolute -bottom-4 -right-4 bg-white rounded-lg shadow-lg p-3 border border-gray-200"
              >
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-xs font-medium text-gray-700">Meta Ads</span>
                </div>
              </motion.div>

              <motion.div
                animate={{ x: [0, 5, 0] }}
                transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 2 }}
                className="absolute top-1/2 -right-8 bg-white rounded-lg shadow-lg p-3 border border-gray-200"
              >
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">+150%</div>
                  <div className="text-xs text-gray-600">Growth</div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.button
        type="button"
        onClick={scrollToNextSection}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/70 hover:text-white transition-colors"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
        aria-label="Scroll to next section"
      >
        <ChevronDownIcon className="w-8 h-8" />
      </motion.button>
    </section>
  );
};

export default Hero;
