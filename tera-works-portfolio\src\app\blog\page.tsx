import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Blog',
  description: 'Latest insights, tips, and trends in web development and digital marketing.',
};

const blogPosts = [
  {
    id: 1,
    title: '10 Essential Features Every Modern Website Needs',
    excerpt: 'Discover the must-have features that make websites successful in 2024, from responsive design to advanced analytics.',
    date: '2024-03-15',
    category: 'Web Development',
    readTime: '5 min read',
    image: '/images/blog/modern-website-features.jpg'
  },
  {
    id: 2,
    title: 'Maximizing ROI with Meta Advertising: A Complete Guide',
    excerpt: 'Learn proven strategies to optimize your Facebook and Instagram ad campaigns for maximum return on investment.',
    date: '2024-03-10',
    category: 'Meta Advertising',
    readTime: '8 min read',
    image: '/images/blog/meta-advertising-roi.jpg'
  },
  {
    id: 3,
    title: 'The Future of E-commerce: Trends to Watch in 2024',
    excerpt: 'Explore the latest e-commerce trends that are shaping online retail and how to implement them in your business.',
    date: '2024-03-05',
    category: 'E-commerce',
    readTime: '6 min read',
    image: '/images/blog/ecommerce-trends-2024.jpg'
  }
];

export default function BlogPage() {
  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="section-padding-sm bg-gradient-to-br from-primary-900 to-secondary-900 text-white">
        <div className="container-custom">
          <div className="text-center">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              Insights & Resources
            </h1>
            <p className="text-xl text-primary-100 max-w-2xl mx-auto">
              Stay updated with the latest trends, tips, and strategies in web development and digital marketing.
            </p>
          </div>
        </div>
      </section>

      {/* Blog Posts */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post) => (
              <article key={post.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="h-48 bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">Blog Image {post.id}</span>
                </div>
                
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm text-primary-600 font-medium">
                      {post.category}
                    </span>
                    <span className="text-sm text-gray-500">
                      {post.readTime}
                    </span>
                  </div>
                  
                  <h2 className="text-xl font-bold text-gray-900 mb-3 hover:text-primary-600 transition-colors">
                    <Link href={`/blog/${post.id}`}>
                      {post.title}
                    </Link>
                  </h2>
                  
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {new Date(post.date).toLocaleDateString()}
                    </span>
                    <Link 
                      href={`/blog/${post.id}`}
                      className="text-primary-600 hover:text-primary-700 font-medium text-sm"
                    >
                      Read More →
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>
          
          {/* Coming Soon Message */}
          <div className="text-center mt-16">
            <div className="bg-white rounded-xl shadow-lg p-8 max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                More Content Coming Soon!
              </h3>
              <p className="text-gray-600 mb-6">
                We're working on creating valuable content to help you succeed in your digital marketing journey. 
                Subscribe to our newsletter to be notified when new articles are published.
              </p>
              <Link 
                href="/contact"
                className="btn-primary"
              >
                Subscribe to Updates
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
