import gsap from "gsap";
import ScrollTrigger from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";

// Register plugins
if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger, useGSAP);
}

// Global GSAP configuration
gsap.config({
  autoSleep: 60,
  force3D: false,
  nullTargetWarn: false,
});

// Animation utilities
export const animations = {
  // Hero section animations
  heroTitle: (element: HTMLElement) => {
    return gsap.fromTo(
      element,
      {
        y: 100,
        opacity: 0,
      },
      {
        y: 0,
        opacity: 1,
        duration: 1.2,
        ease: "power3.out",
      }
    );
  },

  heroSubtitle: (element: HTMLElement, delay = 0.3) => {
    return gsap.fromTo(
      element,
      {
        y: 50,
        opacity: 0,
      },
      {
        y: 0,
        opacity: 1,
        duration: 0.8,
        delay,
        ease: "power2.out",
      }
    );
  },

  // Scroll reveal animations
  scrollReveal: (elements: HTMLElement[] | HTMLElement, options = {}) => {
    const defaultOptions = {
      y: 80,
      opacity: 0,
      duration: 0.8,
      stagger: 0.2,
      ease: "power2.out",
      scrollTrigger: {
        trigger: Array.isArray(elements) ? elements[0] : elements,
        start: "top 80%",
        toggleActions: "play none none reverse",
      },
    };

    return gsap.fromTo(
      elements,
      {
        y: defaultOptions.y,
        opacity: 0,
      },
      {
        ...defaultOptions,
        ...options,
        y: 0,
        opacity: 1,
      }
    );
  },

  // Service cards animation
  serviceCards: (elements: HTMLElement[]) => {
    return gsap.fromTo(
      elements,
      {
        y: 60,
        opacity: 0,
        scale: 0.9,
      },
      {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 0.8,
        stagger: 0.15,
        ease: "power2.out",
        scrollTrigger: {
          trigger: elements[0],
          start: "top 75%",
        },
      }
    );
  },

  // Portfolio items animation
  portfolioItems: (elements: HTMLElement[]) => {
    return gsap.fromTo(
      elements,
      {
        scale: 0.8,
        opacity: 0,
      },
      {
        scale: 1,
        opacity: 1,
        duration: 0.6,
        stagger: 0.1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: elements[0],
          start: "top 70%",
        },
      }
    );
  },

  // Text reveal animation
  textReveal: (element: HTMLElement) => {
    const chars = element.textContent?.split("") || [];
    element.innerHTML = chars
      .map((char) => `<span style="display: inline-block;">${char === " " ? "&nbsp;" : char}</span>`)
      .join("");

    const spans = element.querySelectorAll("span");
    
    return gsap.fromTo(
      spans,
      {
        y: 100,
        opacity: 0,
      },
      {
        y: 0,
        opacity: 1,
        duration: 0.05,
        stagger: 0.02,
        ease: "power2.out",
      }
    );
  },

  // Counter animation
  counter: (element: HTMLElement, endValue: number, duration = 2) => {
    const obj = { value: 0 };
    return gsap.to(obj, {
      value: endValue,
      duration,
      ease: "power2.out",
      onUpdate: () => {
        element.textContent = Math.round(obj.value).toString();
      },
      scrollTrigger: {
        trigger: element,
        start: "top 80%",
        once: true,
      },
    });
  },

  // Hover effects
  cardHover: {
    enter: (element: HTMLElement) => {
      return gsap.to(element, {
        y: -8,
        scale: 1.02,
        duration: 0.3,
        ease: "power2.out",
      });
    },
    leave: (element: HTMLElement) => {
      return gsap.to(element, {
        y: 0,
        scale: 1,
        duration: 0.3,
        ease: "power2.out",
      });
    },
  },

  // Button hover effects
  buttonHover: {
    enter: (element: HTMLElement) => {
      return gsap.to(element, {
        scale: 1.05,
        duration: 0.2,
        ease: "power2.out",
      });
    },
    leave: (element: HTMLElement) => {
      return gsap.to(element, {
        scale: 1,
        duration: 0.2,
        ease: "power2.out",
      });
    },
  },

  // Page transition
  pageTransition: {
    enter: (element: HTMLElement) => {
      return gsap.fromTo(
        element,
        {
          opacity: 0,
          y: 20,
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.5,
          ease: "power2.out",
        }
      );
    },
    exit: (element: HTMLElement) => {
      return gsap.to(element, {
        opacity: 0,
        y: -20,
        duration: 0.3,
        ease: "power2.in",
      });
    },
  },
};

// Utility functions
export const gsapUtils = {
  // Refresh ScrollTrigger (useful for dynamic content)
  refresh: () => {
    if (typeof window !== "undefined") {
      ScrollTrigger.refresh();
    }
  },

  // Kill all animations
  killAll: () => {
    gsap.killTweensOf("*");
  },

  // Set up responsive animations
  matchMedia: (breakpoint: string, animation: () => void) => {
    if (typeof window !== "undefined") {
      gsap.matchMedia().add(breakpoint, animation);
    }
  },

  // Batch scroll animations
  batch: (selector: string, options = {}) => {
    if (typeof window !== "undefined") {
      ScrollTrigger.batch(selector, {
        onEnter: (elements) => animations.scrollReveal(elements as HTMLElement[]),
        ...options,
      });
    }
  },
};

export { gsap, ScrollTrigger, useGSAP };
