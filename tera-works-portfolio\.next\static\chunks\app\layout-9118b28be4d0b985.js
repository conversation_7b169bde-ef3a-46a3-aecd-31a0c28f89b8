(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},2548:(e,s,a)=>{"use strict";a.d(s,{default:()=>m});var r=a(5155);a(2115);var t=a(6874),l=a.n(t),i=a(6766),c=a(6408),n=a(8593),o=a(4633),d=a(7208),h=a(2932),x=a(4910);let m=()=>{let e=new Date().getFullYear(),s=e=>{let{platform:s,className:a="w-5 h-5"}=e;switch(s.toLowerCase()){case"linkedin":return(0,r.jsx)("svg",{className:a,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})});case"twitter":return(0,r.jsx)("svg",{className:a,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})});case"facebook":return(0,r.jsx)("svg",{className:a,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})});case"instagram":return(0,r.jsx)("svg",{className:a,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.596-3.205-1.533l1.714-1.714c.39.586 1.07.977 1.85.977.98 0 1.776-.796 1.776-1.776 0-.98-.796-1.776-1.776-1.776-.78 0-1.46.391-1.85.977L4.244 10.43c.757-.937 1.908-1.533 3.205-1.533 2.269 0 4.106 1.837 4.106 4.106s-1.837 4.106-4.106 4.106zm7.441 0c-2.269 0-4.106-1.837-4.106-4.106s1.837-4.106 4.106-4.106c1.297 0 2.448.596 3.205 1.533l-1.714 1.714c-.39-.586-1.07-.977-1.85-.977-.98 0-1.776.796-1.776 1.776 0 .98.796 1.776 1.776 1.776.78 0 1.46-.391 1.85-.977l1.714 1.714c-.757.937-1.908 1.533-3.205 1.533z"})});case"github":return(0,r.jsx)("svg",{className:a,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})});default:return(0,r.jsx)(n.A,{className:a})}};return(0,r.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,r.jsxs)("div",{className:"container-custom",children:[(0,r.jsx)(c.P.div,{variants:x.bK,initial:"initial",whileInView:"animate",viewport:{once:!0},className:"py-16 lg:py-20",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12",children:[(0,r.jsxs)(c.P.div,{variants:x.Rf,className:"lg:col-span-1",children:[(0,r.jsxs)(l(),{href:"/",className:"flex items-center space-x-2 mb-6",children:[(0,r.jsx)("div",{className:"relative w-10 h-10",children:(0,r.jsx)(i.default,{src:"/logo.png",alt:"Tera Works Logo",fill:!0,className:"object-contain"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-xl font-bold",children:"Tera Works"}),(0,r.jsx)("span",{className:"text-sm text-gray-400 -mt-1",children:"Let's Grow Together"})]})]}),(0,r.jsx)("p",{className:"text-gray-300 mb-6 text-sm leading-relaxed",children:"Professional web development and Meta advertising services to help your business grow and succeed online."}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 text-primary-400 flex-shrink-0"}),(0,r.jsx)("a",{href:"mailto:".concat(h.r_.email),className:"text-gray-300 hover:text-white transition-colors",children:h.r_.email})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 text-primary-400 flex-shrink-0"}),(0,r.jsx)("a",{href:"tel:".concat(h.r_.phone),className:"text-gray-300 hover:text-white transition-colors",children:h.r_.phone})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3 text-sm",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 text-primary-400 flex-shrink-0 mt-0.5"}),(0,r.jsxs)("div",{className:"text-gray-300",children:[(0,r.jsx)("div",{children:h.r_.address.street}),(0,r.jsxs)("div",{children:[h.r_.address.city,", ",h.r_.address.state," ",h.r_.address.zip]})]})]})]})]}),(0,r.jsxs)(c.P.div,{variants:x.Rf,children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Services"}),(0,r.jsx)("ul",{className:"space-y-3",children:h.ii.services.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:e.href,className:"text-gray-300 hover:text-white transition-colors text-sm",children:e.label})},e.href))})]}),(0,r.jsxs)(c.P.div,{variants:x.Rf,children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Company"}),(0,r.jsx)("ul",{className:"space-y-3",children:h.ii.company.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:e.href,className:"text-gray-300 hover:text-white transition-colors text-sm",children:e.label})},e.href))})]}),(0,r.jsxs)(c.P.div,{variants:x.Rf,children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Resources"}),(0,r.jsx)("ul",{className:"space-y-3 mb-6",children:h.ii.resources.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:e.href,className:"text-gray-300 hover:text-white transition-colors text-sm",children:e.label})},e.href))}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Follow Us"}),(0,r.jsx)("div",{className:"flex space-x-4",children:h.lj.slice(0,4).map(e=>(0,r.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white transition-colors","aria-label":e.label,children:(0,r.jsx)(s,{platform:e.platform})},e.platform))})]})]})]})}),(0,r.jsx)(c.P.div,{variants:x.tE,initial:"initial",whileInView:"animate",viewport:{once:!0},className:"border-t border-gray-800 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-400",children:["\xa9 ",e," Tera Works. All rights reserved."]}),(0,r.jsx)("div",{className:"flex space-x-6",children:h.ii.legal.map(e=>(0,r.jsx)(l(),{href:e.href,className:"text-sm text-gray-400 hover:text-white transition-colors",children:e.label},e.href))})]})})]})})}},5098:(e,s,a)=>{"use strict";a.d(s,{default:()=>p});var r=a(5155),t=a(2115),l=a(6874),i=a.n(l),c=a(6766),n=a(8999),o=a(760),d=a(6408);let h=t.forwardRef(function(e,s){let{title:a,titleId:r,...l}=e;return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},l),a?t.createElement("title",{id:r},a):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}),x=t.forwardRef(function(e,s){let{title:a,titleId:r,...l}=e;return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},l),a?t.createElement("title",{id:r},a):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))});var m=a(9434),v=a(2932),f=a(8055),j=a(4910);let p=()=>{let[e,s]=(0,t.useState)(!1),[a,l]=(0,t.useState)(!1),p=(0,n.usePathname)();(0,t.useEffect)(()=>{let e=()=>{s(window.scrollY>10)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,t.useEffect)(()=>{l(!1)},[p]);let g=e=>"/"===e?"/"===p:p.startsWith(e);return(0,r.jsx)("header",{className:(0,m.cn)("fixed top-0 left-0 right-0 z-50 transition-all duration-300",e?"bg-white/95 backdrop-blur-sm shadow-lg":"bg-transparent"),children:(0,r.jsxs)("div",{className:"container-custom",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-16 lg:h-20",children:[(0,r.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"relative w-10 h-10 lg:w-12 lg:h-12",children:(0,r.jsx)(c.default,{src:"/logo.png",alt:"Tera Works Logo",fill:!0,className:"object-contain",priority:!0})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-xl lg:text-2xl font-bold text-gray-900",children:"Tera Works"}),(0,r.jsx)("span",{className:"text-xs lg:text-sm text-gray-600 -mt-1",children:"Let's Grow Together"})]})]}),(0,r.jsx)("nav",{className:"hidden lg:flex items-center space-x-8",children:v.ss.map(s=>(0,r.jsx)(i(),{href:s.href,className:(0,m.cn)("text-sm font-medium transition-colors hover:text-primary-600",g(s.href)?"text-primary-600":e?"text-gray-900":"text-white"),children:s.label},s.href))}),(0,r.jsx)("div",{className:"hidden lg:flex items-center space-x-4",children:(0,r.jsx)(f.$n,{href:"/contact",variant:"primary",children:"Get Started"})}),(0,r.jsx)("button",{type:"button",onClick:()=>l(!a),className:(0,m.cn)("lg:hidden p-2 rounded-md transition-colors",e?"text-gray-900 hover:bg-gray-100":"text-white hover:bg-white/10"),"aria-label":"Toggle mobile menu",children:a?(0,r.jsx)(h,{className:"w-6 h-6"}):(0,r.jsx)(x,{className:"w-6 h-6"})})]}),(0,r.jsx)(o.N,{children:a&&(0,r.jsx)(d.P.div,{variants:j.Ym,initial:"closed",animate:"open",exit:"closed",className:"lg:hidden bg-white border-t border-gray-200 shadow-lg",children:(0,r.jsxs)("nav",{className:"py-4 space-y-2",children:[v.ss.map(e=>(0,r.jsx)(d.P.div,{variants:j.Tz,children:(0,r.jsx)(i(),{href:e.href,className:(0,m.cn)("block px-4 py-2 text-base font-medium transition-colors hover:bg-gray-50",g(e.href)?"text-primary-600 bg-primary-50":"text-gray-900"),children:e.label})},e.href)),(0,r.jsx)(d.P.div,{variants:j.Tz,className:"px-4 pt-4 border-t border-gray-200",children:(0,r.jsx)(f.$n,{href:"/contact",variant:"primary",className:"w-full",children:"Get Started"})})]})})})]})})}},5452:e=>{e.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback'",fontStyle:"normal"},className:"__className_d5a796",variable:"__variable_d5a796"}},5984:(e,s,a)=>{Promise.resolve().then(a.t.bind(a,6707,23)),Promise.resolve().then(a.t.bind(a,5452,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,2548)),Promise.resolve().then(a.bind(a,5098))},6707:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}}},e=>{var s=s=>e(e.s=s);e.O(0,[153,989,738,441,684,358],()=>s(5984)),_N_E=e.O()}]);