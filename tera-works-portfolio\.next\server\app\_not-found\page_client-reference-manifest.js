globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"894":{"*":{"id":"6346","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"7173","name":"*","chunks":[],"async":false}},"2548":{"*":{"id":"2830","name":"*","chunks":[],"async":false}},"2719":{"*":{"id":"7071","name":"*","chunks":[],"async":false}},"3934":{"*":{"id":"7589","name":"*","chunks":[],"async":false}},"4302":{"*":{"id":"2059","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"8827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"7924","name":"*","chunks":[],"async":false}},"5098":{"*":{"id":"5736","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"5656","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"99","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"8243","name":"*","chunks":[],"async":false}},"8431":{"*":{"id":"5944","name":"*","chunks":[],"async":false}},"8977":{"*":{"id":"6351","name":"*","chunks":[],"async":false}},"9551":{"*":{"id":"4865","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"2763","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}":{"id":6707,"name":"*","chunks":["989","static/chunks/989-b347b5eddeb5fe0e.js","738","static/chunks/738-5d11a6f0e6b73125.js","177","static/chunks/app/layout-9118b28be4d0b985.js"],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"}":{"id":5452,"name":"*","chunks":["989","static/chunks/989-b347b5eddeb5fe0e.js","738","static/chunks/738-5d11a6f0e6b73125.js","177","static/chunks/app/layout-9118b28be4d0b985.js"],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\globals.css":{"id":347,"name":"*","chunks":["989","static/chunks/989-b347b5eddeb5fe0e.js","738","static/chunks/738-5d11a6f0e6b73125.js","177","static/chunks/app/layout-9118b28be4d0b985.js"],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Footer.tsx":{"id":2548,"name":"*","chunks":["989","static/chunks/989-b347b5eddeb5fe0e.js","738","static/chunks/738-5d11a6f0e6b73125.js","177","static/chunks/app/layout-9118b28be4d0b985.js"],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Header.tsx":{"id":5098,"name":"*","chunks":["989","static/chunks/989-b347b5eddeb5fe0e.js","738","static/chunks/738-5d11a6f0e6b73125.js","177","static/chunks/app/layout-9118b28be4d0b985.js"],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\About.tsx":{"id":3934,"name":"*","chunks":["592","static/chunks/c15bf2b0-7f3f4bd25724833d.js","989","static/chunks/989-b347b5eddeb5fe0e.js","825","static/chunks/825-3ea400f4b63fcacd.js","738","static/chunks/738-5d11a6f0e6b73125.js","974","static/chunks/app/page-0753bc7cb8ab860b.js"],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Contact.tsx":{"id":8977,"name":"*","chunks":["592","static/chunks/c15bf2b0-7f3f4bd25724833d.js","989","static/chunks/989-b347b5eddeb5fe0e.js","825","static/chunks/825-3ea400f4b63fcacd.js","738","static/chunks/738-5d11a6f0e6b73125.js","974","static/chunks/app/page-0753bc7cb8ab860b.js"],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Hero.tsx":{"id":8431,"name":"*","chunks":["592","static/chunks/c15bf2b0-7f3f4bd25724833d.js","989","static/chunks/989-b347b5eddeb5fe0e.js","825","static/chunks/825-3ea400f4b63fcacd.js","738","static/chunks/738-5d11a6f0e6b73125.js","974","static/chunks/app/page-0753bc7cb8ab860b.js"],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Portfolio.tsx":{"id":4302,"name":"*","chunks":["592","static/chunks/c15bf2b0-7f3f4bd25724833d.js","989","static/chunks/989-b347b5eddeb5fe0e.js","825","static/chunks/825-3ea400f4b63fcacd.js","738","static/chunks/738-5d11a6f0e6b73125.js","974","static/chunks/app/page-0753bc7cb8ab860b.js"],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Services.tsx":{"id":9551,"name":"*","chunks":["592","static/chunks/c15bf2b0-7f3f4bd25724833d.js","989","static/chunks/989-b347b5eddeb5fe0e.js","825","static/chunks/825-3ea400f4b63fcacd.js","738","static/chunks/738-5d11a6f0e6b73125.js","974","static/chunks/app/page-0753bc7cb8ab860b.js"],"async":false},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Testimonials.tsx":{"id":2719,"name":"*","chunks":["592","static/chunks/c15bf2b0-7f3f4bd25724833d.js","989","static/chunks/989-b347b5eddeb5fe0e.js","825","static/chunks/825-3ea400f4b63fcacd.js","738","static/chunks/738-5d11a6f0e6b73125.js","974","static/chunks/app/page-0753bc7cb8ab860b.js"],"async":false}},"entryCSSFiles":{"E:\\Augment Code Testing\\tera-works-portfolio\\src\\":[],"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\layout":[{"inlined":false,"path":"static/css/fe3acb13291c7c34.css"}],"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\page":[],"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\_not-found\\page":[]},"rscModuleMapping":{"347":{"*":{"id":"1135","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"6444","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"1307","name":"*","chunks":[],"async":false}},"2548":{"*":{"id":"4712","name":"*","chunks":[],"async":false}},"2719":{"*":{"id":"743","name":"*","chunks":[],"async":false}},"3934":{"*":{"id":"1136","name":"*","chunks":[],"async":false}},"4302":{"*":{"id":"5947","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"2089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"6042","name":"*","chunks":[],"async":false}},"5098":{"*":{"id":"8926","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"8170","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"9477","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"9345","name":"*","chunks":[],"async":false}},"8431":{"*":{"id":"8595","name":"*","chunks":[],"async":false}},"8977":{"*":{"id":"4759","name":"*","chunks":[],"async":false}},"9551":{"*":{"id":"8395","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"6577","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}