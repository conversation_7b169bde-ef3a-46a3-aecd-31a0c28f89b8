import { NavigationItem, SocialLink } from '@/types';

export const mainNavigation: NavigationItem[] = [
  {
    label: 'Home',
    href: '/'
  },
  {
    label: 'About',
    href: '/about'
  },
  {
    label: 'Services',
    href: '/services',
    children: [
      {
        label: 'Web Development',
        href: '/services/web-development'
      },
      {
        label: 'Meta Advertising',
        href: '/services/meta-advertising'
      },
      {
        label: 'Booking Systems',
        href: '/services/booking-systems'
      }
    ]
  },
  {
    label: 'Portfolio',
    href: '/portfolio'
  },
  {
    label: 'Blog',
    href: '/blog'
  },
  {
    label: 'Contact',
    href: '/contact'
  }
];

export const footerNavigation = {
  services: [
    {
      label: 'Custom Website Development',
      href: '/services/web-development'
    },
    {
      label: 'E-commerce Solutions',
      href: '/services/ecommerce'
    },
    {
      label: 'Booking Systems',
      href: '/services/booking-systems'
    },
    {
      label: 'Meta Advertising',
      href: '/services/meta-advertising'
    },
    {
      label: 'Website Maintenance',
      href: '/services/maintenance'
    }
  ],
  company: [
    {
      label: 'About Us',
      href: '/about'
    },
    {
      label: 'Our Process',
      href: '/process'
    },
    {
      label: 'Case Studies',
      href: '/portfolio'
    },
    {
      label: 'Testimonials',
      href: '/testimonials'
    },
    {
      label: 'Careers',
      href: '/careers'
    }
  ],
  resources: [
    {
      label: 'Blog',
      href: '/blog'
    },
    {
      label: 'FAQ',
      href: '/faq'
    },
    {
      label: 'Support',
      href: '/support'
    },
    {
      label: 'Documentation',
      href: '/docs'
    },
    {
      label: 'Contact',
      href: '/contact'
    }
  ],
  legal: [
    {
      label: 'Privacy Policy',
      href: '/privacy'
    },
    {
      label: 'Terms of Service',
      href: '/terms'
    },
    {
      label: 'Cookie Policy',
      href: '/cookies'
    },
    {
      label: 'GDPR',
      href: '/gdpr'
    }
  ]
};

export const socialLinks: SocialLink[] = [
  {
    platform: 'LinkedIn',
    url: 'https://linkedin.com/company/tera-works',
    icon: 'linkedin',
    label: 'Follow us on LinkedIn'
  },
  {
    platform: 'Twitter',
    url: 'https://twitter.com/teraworks',
    icon: 'twitter',
    label: 'Follow us on Twitter'
  },
  {
    platform: 'Facebook',
    url: 'https://facebook.com/teraworks',
    icon: 'facebook',
    label: 'Like us on Facebook'
  },
  {
    platform: 'Instagram',
    url: 'https://instagram.com/teraworks',
    icon: 'instagram',
    label: 'Follow us on Instagram'
  },
  {
    platform: 'GitHub',
    url: 'https://github.com/teraworks',
    icon: 'github',
    label: 'View our code on GitHub'
  },
  {
    platform: 'Email',
    url: 'mailto:<EMAIL>',
    icon: 'email',
    label: 'Send us an email'
  }
];

export const contactInfo = {
  email: '<EMAIL>',
  phone: '+****************',
  address: {
    street: '123 Business Ave',
    city: 'Tech City',
    state: 'TC',
    zip: '12345',
    country: 'United States'
  },
  hours: {
    weekdays: '9:00 AM - 6:00 PM',
    weekends: '10:00 AM - 4:00 PM',
    timezone: 'EST'
  }
};

export const quickLinks = [
  {
    label: 'Get Started',
    href: '/contact',
    description: 'Start your project today'
  },
  {
    label: 'View Portfolio',
    href: '/portfolio',
    description: 'See our latest work'
  },
  {
    label: 'Read Blog',
    href: '/blog',
    description: 'Latest insights and tips'
  },
  {
    label: 'Book Consultation',
    href: '/consultation',
    description: 'Free 30-minute consultation'
  }
];
