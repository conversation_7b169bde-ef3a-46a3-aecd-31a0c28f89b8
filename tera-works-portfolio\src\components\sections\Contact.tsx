'use client';

import React, { useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { 
  EnvelopeIcon, 
  PhoneIcon, 
  MapPinIcon, 
  ClockIcon,
  CheckCircleIcon 
} from '@heroicons/react/24/outline';
import { Button, Input, Textarea, Select } from '@/components/ui';
import { useGSAP } from '@gsap/react';
import { animations } from '@/lib/animations/gsap';
import { contactInfo } from '@/data/navigation';
import { services } from '@/data/services';
import { staggerContainer, staggerItem } from '@/lib/animations/framer';

const Contact: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    service: '',
    budget: '',
    message: ''
  });

  useGSAP(() => {
    if (sectionRef.current) {
      const elements = sectionRef.current.querySelectorAll('.contact-content');
      animations.scrollReveal(Array.from(elements) as HTMLElement[], {
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 80%',
        }
      });
    }
  }, { scope: sectionRef });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setIsSubmitted(true);
        // Reset form after 3 seconds
        setTimeout(() => {
          setIsSubmitted(false);
          setFormData({
            name: '',
            email: '',
            phone: '',
            company: '',
            service: '',
            budget: '',
            message: ''
          });
        }, 3000);
      } else {
        console.error('Form submission failed');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const serviceOptions = [
    { value: '', label: 'Select a service...' },
    ...services.map(service => ({
      value: service.id,
      label: service.title
    }))
  ];

  const budgetOptions = [
    { value: '', label: 'Select budget range...' },
    { value: 'under-5k', label: 'Under $5,000' },
    { value: '5k-10k', label: '$5,000 - $10,000' },
    { value: '10k-25k', label: '$10,000 - $25,000' },
    { value: '25k-50k', label: '$25,000 - $50,000' },
    { value: 'over-50k', label: 'Over $50,000' },
    { value: 'discuss', label: 'Let\'s discuss' }
  ];

  return (
    <section 
      id="contact"
      ref={sectionRef}
      className="section-padding bg-gray-50"
    >
      <div className="container-custom">
        {/* Header */}
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div variants={staggerItem}>
            <span className="inline-block px-4 py-2 bg-primary-100 text-primary-800 rounded-full text-sm font-medium mb-6">
              Get In Touch
            </span>
          </motion.div>

          <motion.h2 
            variants={staggerItem}
            className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6"
          >
            Let's Start Your Project
          </motion.h2>

          <motion.p 
            variants={staggerItem}
            className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed"
          >
            Ready to transform your digital presence? Get in touch with us today for a free consultation 
            and let's discuss how we can help your business grow.
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
          {/* Contact Form */}
          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="contact-content"
          >
            <motion.div variants={staggerItem}>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                Send us a message
              </h3>
            </motion.div>

            {isSubmitted ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-green-50 border border-green-200 rounded-lg p-8 text-center"
              >
                <CheckCircleIcon className="w-16 h-16 text-green-600 mx-auto mb-4" />
                <h4 className="text-xl font-semibold text-green-900 mb-2">
                  Message Sent Successfully!
                </h4>
                <p className="text-green-700">
                  Thank you for reaching out. We'll get back to you within 24 hours.
                </p>
              </motion.div>
            ) : (
              <motion.form
                ref={formRef}
                onSubmit={handleSubmit}
                variants={staggerItem}
                className="space-y-6"
              >
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <Input
                    label="Full Name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    placeholder="John Doe"
                  />
                  <Input
                    label="Email Address"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <Input
                    label="Phone Number"
                    name="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="+****************"
                  />
                  <Input
                    label="Company Name"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    placeholder="Your Company"
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <Select
                    label="Service Interested In"
                    name="service"
                    value={formData.service}
                    onChange={handleInputChange}
                    options={serviceOptions}
                    required
                  />
                  <Select
                    label="Project Budget"
                    name="budget"
                    value={formData.budget}
                    onChange={handleInputChange}
                    options={budgetOptions}
                  />
                </div>

                <Textarea
                  label="Project Details"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  placeholder="Tell us about your project, goals, and any specific requirements..."
                  className="min-h-[120px]"
                />

                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  loading={isSubmitting}
                  className="w-full"
                >
                  {isSubmitting ? 'Sending Message...' : 'Send Message'}
                </Button>
              </motion.form>
            )}
          </motion.div>

          {/* Contact Information */}
          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="contact-content"
          >
            <motion.div variants={staggerItem}>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                Get in touch
              </h3>
              <p className="text-gray-600 mb-8 leading-relaxed">
                Prefer to reach out directly? Here are all the ways you can contact us. 
                We're always happy to discuss your project and answer any questions.
              </p>
            </motion.div>

            <motion.div variants={staggerItem} className="space-y-6 mb-8">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <EnvelopeIcon className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">Email Us</h4>
                  <p className="text-gray-600 mb-2">Send us an email anytime</p>
                  <a 
                    href={`mailto:${contactInfo.email}`}
                    className="text-primary-600 hover:text-primary-700 font-medium"
                  >
                    {contactInfo.email}
                  </a>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <PhoneIcon className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">Call Us</h4>
                  <p className="text-gray-600 mb-2">Speak with our team directly</p>
                  <a 
                    href={`tel:${contactInfo.phone}`}
                    className="text-primary-600 hover:text-primary-700 font-medium"
                  >
                    {contactInfo.phone}
                  </a>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <MapPinIcon className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">Visit Us</h4>
                  <p className="text-gray-600 mb-2">Come say hello at our office</p>
                  <div className="text-gray-700">
                    <div>{contactInfo.address.street}</div>
                    <div>
                      {contactInfo.address.city}, {contactInfo.address.state} {contactInfo.address.zip}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <ClockIcon className="w-6 h-6 text-primary-600" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">Business Hours</h4>
                  <p className="text-gray-600 mb-2">When we're available</p>
                  <div className="text-gray-700">
                    <div>Weekdays: {contactInfo.hours.weekdays}</div>
                    <div>Weekends: {contactInfo.hours.weekends}</div>
                    <div className="text-sm text-gray-500 mt-1">
                      {contactInfo.hours.timezone}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Quick Actions */}
            <motion.div variants={staggerItem} className="space-y-4">
              <h4 className="font-semibold text-gray-900">Quick Actions</h4>
              <div className="space-y-3">
                <Button 
                  href="/portfolio" 
                  variant="outline" 
                  className="w-full justify-start"
                >
                  View Our Portfolio
                </Button>
                <Button 
                  href="/services" 
                  variant="outline" 
                  className="w-full justify-start"
                >
                  Explore Our Services
                </Button>
                <Button 
                  href="/about" 
                  variant="outline" 
                  className="w-full justify-start"
                >
                  Learn About Us
                </Button>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
