"use strict";exports.id=381,exports.ids=[381],exports.modules={2059:(e,t,i)=>{i.d(t,{default:()=>f});var a=i(687),r=i(3210),o=i(474),s=i(6001),n=i(8920);let l=r.forwardRef(function({title:e,titleId:t,...i},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},i),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}),c=r.forwardRef(function({title:e,titleId:t,...i},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},i),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"}))});var d=i(3437),m=i(6156),g=i(2438);let p=[{id:"1",title:"Spice and Herb Restaurant",description:"Modern restaurant website with online ordering and reservation system",longDescription:"A comprehensive restaurant website featuring an elegant design, online menu, reservation system, and integrated ordering platform. Built with modern web technologies to provide an exceptional user experience.",category:"web-development",technologies:["Next.js","React","TypeScript","Tailwind CSS","Stripe","Sanity CMS"],images:{thumbnail:"/images/portfolio/spice-herb-thumb.jpg",gallery:["/images/portfolio/spice-herb-1.jpg","/images/portfolio/spice-herb-2.jpg","/images/portfolio/spice-herb-3.jpg"]},url:"https://spiceandherbrestaurant.com",featured:!0,completedAt:new Date("2024-03-15"),client:"Spice & Herb Restaurant",results:[{metric:"Online Orders",value:"+150%",description:"Increase in online orders within 3 months"},{metric:"Page Load Speed",value:"1.2s",description:"Average page load time"},{metric:"Mobile Traffic",value:"78%",description:"Mobile users engagement rate"}]},{id:"2",title:"Zeynthra Fashion",description:"E-commerce platform for luxury fashion brand with custom design",longDescription:"A sophisticated e-commerce website for a luxury fashion brand, featuring custom product galleries, size guides, wishlist functionality, and seamless checkout experience.",category:"web-development",technologies:["Shopify","Liquid","JavaScript","SCSS","Shopify Plus"],images:{thumbnail:"/images/portfolio/zeynthra-thumb.jpg",gallery:["/images/portfolio/zeynthra-1.jpg","/images/portfolio/zeynthra-2.jpg","/images/portfolio/zeynthra-3.jpg"]},url:"https://zeynthra.com",featured:!0,completedAt:new Date("2024-01-20"),client:"Zeynthra Fashion",results:[{metric:"Conversion Rate",value:"+85%",description:"Improvement in conversion rate"},{metric:"Average Order Value",value:"+42%",description:"Increase in average order value"},{metric:"Customer Retention",value:"68%",description:"Returning customer rate"}]},{id:"3",title:"Chalakadulanga Photography",description:"Professional photography portfolio with booking system integration",longDescription:"A stunning photography portfolio website showcasing professional work with an integrated booking system for client appointments and session management.",category:"web-development",technologies:["React","Node.js","MongoDB","Express","Cloudinary","Stripe"],images:{thumbnail:"/images/portfolio/chalakadulanga-thumb.jpg",gallery:["/images/portfolio/chalakadulanga-1.jpg","/images/portfolio/chalakadulanga-2.jpg","/images/portfolio/chalakadulanga-3.jpg"]},url:"https://chalakadulangaphotography.com",featured:!0,completedAt:new Date("2023-11-10"),client:"Chalakadulanga Photography",results:[{metric:"Booking Inquiries",value:"+200%",description:"Increase in booking inquiries"},{metric:"Portfolio Views",value:"15K+",description:"Monthly portfolio views"},{metric:"Client Satisfaction",value:"4.9/5",description:"Average client rating"}]},{id:"4",title:"Photography Booking System",description:"Custom booking platform for photography sessions and events",longDescription:"A comprehensive booking system specifically designed for photography services, featuring calendar integration, payment processing, client management, and automated email notifications.",category:"booking-system",technologies:["Next.js","PostgreSQL","Prisma","NextAuth","Stripe","SendGrid"],images:{thumbnail:"/images/portfolio/booking-system-thumb.jpg",gallery:["/images/portfolio/booking-system-1.jpg","/images/portfolio/booking-system-2.jpg","/images/portfolio/booking-system-3.jpg"]},url:"https://booking.chalakadulangaphotography.com",featured:!0,completedAt:new Date("2023-12-05"),client:"Chalakadulanga Photography",results:[{metric:"Booking Efficiency",value:"+300%",description:"Faster booking process"},{metric:"No-shows Reduced",value:"-75%",description:"Reduction in missed appointments"},{metric:"Admin Time Saved",value:"20hrs/week",description:"Time saved on booking management"}]},{id:"5",title:"Local Restaurant Meta Ads Campaign",description:"Comprehensive Meta advertising campaign for local restaurant chain",longDescription:"A strategic Meta advertising campaign targeting local customers for a restaurant chain, focusing on increasing foot traffic, online orders, and brand awareness through targeted Facebook and Instagram ads.",category:"meta-advertising",technologies:["Meta Ads Manager","Facebook Pixel","Google Analytics","Looker Studio"],images:{thumbnail:"/images/portfolio/meta-ads-restaurant-thumb.jpg",gallery:["/images/portfolio/meta-ads-restaurant-1.jpg","/images/portfolio/meta-ads-restaurant-2.jpg","/images/portfolio/meta-ads-restaurant-3.jpg"]},featured:!0,completedAt:new Date("2024-02-28"),client:"Local Restaurant Chain",results:[{metric:"ROAS",value:"4.2x",description:"Return on ad spend"},{metric:"Foot Traffic",value:"+65%",description:"Increase in restaurant visits"},{metric:"Online Orders",value:"+120%",description:"Growth in online orders"}]},{id:"6",title:"E-commerce Fashion Brand Campaign",description:"Meta advertising strategy for fashion e-commerce brand expansion",longDescription:"A comprehensive Meta advertising strategy for a fashion e-commerce brand, focusing on customer acquisition, retargeting, and brand awareness across Facebook and Instagram platforms.",category:"meta-advertising",technologies:["Meta Ads Manager","Facebook Pixel","Shopify Analytics","Klaviyo"],images:{thumbnail:"/images/portfolio/meta-ads-fashion-thumb.jpg",gallery:["/images/portfolio/meta-ads-fashion-1.jpg","/images/portfolio/meta-ads-fashion-2.jpg","/images/portfolio/meta-ads-fashion-3.jpg"]},featured:!1,completedAt:new Date("2024-01-15"),client:"Fashion E-commerce Brand",results:[{metric:"ROAS",value:"5.8x",description:"Return on ad spend"},{metric:"Customer Acquisition",value:"+180%",description:"New customer growth"},{metric:"Brand Awareness",value:"+95%",description:"Increase in brand recognition"}]}],h=[{id:"all",label:"All Projects",count:p.length},{id:"web-development",label:"Web Development",count:p.filter(e=>"web-development"===e.category).length},{id:"meta-advertising",label:"Meta Advertising",count:p.filter(e=>"meta-advertising"===e.category).length},{id:"booking-system",label:"Booking Systems",count:p.filter(e=>"booking-system"===e.category).length}];p.filter(e=>e.featured);var u=i(4020);let f=()=>{let e=(0,r.useRef)(null),t=(0,r.useRef)(null),[i,f]=(0,r.useState)("all");(0,m.L)(()=>{if(t.current){let e=t.current.querySelectorAll(".portfolio-item");g.WT.portfolioItems(Array.from(e))}},{scope:e,dependencies:[i]});let x="all"===i?p:p.filter(e=>e.category===i),v=e=>{f(e)};return(0,a.jsx)("section",{id:"portfolio",ref:e,className:"section-padding bg-gray-50",children:(0,a.jsxs)("div",{className:"container-custom",children:[(0,a.jsxs)(s.P.div,{variants:u.bK,initial:"initial",whileInView:"animate",viewport:{once:!0},className:"text-center mb-16",children:[(0,a.jsx)(s.P.div,{variants:u.Rf,children:(0,a.jsx)("span",{className:"inline-block px-4 py-2 bg-primary-100 text-primary-800 rounded-full text-sm font-medium mb-6",children:"Our Portfolio"})}),(0,a.jsx)(s.P.h2,{variants:u.Rf,className:"text-4xl lg:text-5xl font-bold text-gray-900 mb-6",children:"Success Stories & Case Studies"}),(0,a.jsx)(s.P.p,{variants:u.Rf,className:"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Explore our portfolio of successful projects that have helped businesses grow, increase conversions, and achieve their digital marketing goals."})]}),(0,a.jsx)(s.P.div,{variants:u.bK,initial:"initial",whileInView:"animate",viewport:{once:!0},className:"flex flex-wrap justify-center gap-4 mb-12",children:h.map(e=>(0,a.jsxs)(s.P.button,{variants:u.Rf,onClick:()=>v(e.id),className:`px-6 py-3 rounded-full font-medium transition-all duration-300 ${i===e.id?"bg-primary-600 text-white shadow-lg":"bg-white text-gray-600 hover:bg-gray-50 border border-gray-200"}`,whileHover:{scale:1.05},whileTap:{scale:.95},children:[e.label,(0,a.jsxs)("span",{className:"ml-2 text-sm opacity-75",children:["(",e.count,")"]})]},e.id))}),(0,a.jsx)(s.P.div,{ref:t,variants:u.VM,initial:"hidden",animate:"show",className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16",children:(0,a.jsx)(n.N,{mode:"wait",children:x.map((e,t)=>(0,a.jsx)(s.P.div,{variants:u.CV,initial:"hidden",animate:"show",exit:"hidden",layout:!0,className:"portfolio-item",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 group",children:[(0,a.jsxs)("div",{className:"relative h-64 overflow-hidden",children:[(0,a.jsx)(o.default,{src:e.images.thumbnail,alt:e.title,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)(d.$n,{href:`/portfolio/${e.id}`,variant:"secondary",size:"sm",className:"bg-white/90 text-gray-900 hover:bg-white",children:[(0,a.jsx)(l,{className:"w-4 h-4 mr-2"}),"View Details"]}),e.url&&(0,a.jsxs)(d.$n,{href:e.url,external:!0,variant:"outline",size:"sm",className:"bg-white/90 border-white/90 text-gray-900 hover:bg-white",children:[(0,a.jsx)(c,{className:"w-4 h-4 mr-2"}),"Live Site"]})]})}),e.featured&&(0,a.jsx)("div",{className:"absolute top-4 left-4",children:(0,a.jsx)("span",{className:"bg-accent-600 text-white px-3 py-1 rounded-full text-xs font-medium",children:"Featured"})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("span",{className:"text-sm text-primary-600 font-medium capitalize",children:e.category.replace("-"," ")}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:e.completedAt.toLocaleDateString()})]}),(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.technologies.slice(0,3).map((e,t)=>(0,a.jsx)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md",children:e},t)),e.technologies.length>3&&(0,a.jsxs)("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md",children:["+",e.technologies.length-3," more"]})]}),e.results&&e.results.length>0&&(0,a.jsx)("div",{className:"border-t border-gray-100 pt-4",children:(0,a.jsx)("div",{className:"grid grid-cols-2 gap-4",children:e.results.slice(0,2).map((e,t)=>(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-primary-600",children:e.value}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:e.metric})]},t))})})]})]})},`${e.id}-${i}`))})}),(0,a.jsxs)(s.P.div,{variants:u.bK,initial:"initial",whileInView:"animate",viewport:{once:!0},className:"text-center",children:[(0,a.jsx)(s.P.h3,{variants:u.Rf,className:"text-3xl font-bold text-gray-900 mb-4",children:"Ready to Start Your Success Story?"}),(0,a.jsx)(s.P.p,{variants:u.Rf,className:"text-lg text-gray-600 mb-8 max-w-2xl mx-auto",children:"Join our growing list of satisfied clients and let us help you achieve similar results for your business."}),(0,a.jsxs)(s.P.div,{variants:u.Rf,className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(d.$n,{href:"/contact",variant:"primary",size:"lg",children:"Start Your Project"}),(0,a.jsx)(d.$n,{href:"/portfolio",variant:"outline",size:"lg",children:"View All Projects"})]})]})]})})}},5947:(e,t,i)=>{i.d(t,{default:()=>a});let a=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\sections\\\\Portfolio.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Portfolio.tsx","default")}};