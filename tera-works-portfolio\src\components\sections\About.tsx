'use client';

import React, { useRef } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { 
  CheckCircleIcon, 
  TrophyIcon, 
  UsersIcon, 
  ChartBarIcon 
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui';
import { useGSAP } from '@gsap/react';
import { animations } from '@/lib/animations/gsap';
import { statistics } from '@/data/testimonials';
import { fadeInUp, staggerContainer, staggerItem } from '@/lib/animations/framer';

const About: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    // Animate statistics counters
    if (statsRef.current) {
      const statElements = statsRef.current.querySelectorAll('[data-stat-value]');
      statElements.forEach((element) => {
        const value = parseInt(element.getAttribute('data-stat-value') || '0');
        animations.counter(element as HTMLElement, value, 2);
      });
    }

    // Animate section elements
    if (sectionRef.current) {
      const elements = sectionRef.current.querySelectorAll('.about-content');
      animations.scrollReveal(Array.from(elements) as HTMLElement[], {
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 80%',
        }
      });
    }
  }, { scope: sectionRef });

  const features = [
    {
      icon: <CheckCircleIcon className="w-6 h-6" />,
      title: 'Proven Results',
      description: 'Track record of delivering successful projects that drive real business growth.'
    },
    {
      icon: <TrophyIcon className="w-6 h-6" />,
      title: 'Award-Winning Design',
      description: 'Creating beautiful, functional websites that stand out from the competition.'
    },
    {
      icon: <UsersIcon className="w-6 h-6" />,
      title: 'Client-Focused',
      description: 'Dedicated to understanding your business needs and exceeding expectations.'
    },
    {
      icon: <ChartBarIcon className="w-6 h-6" />,
      title: 'Data-Driven',
      description: 'Using analytics and insights to optimize performance and maximize ROI.'
    }
  ];

  return (
    <section 
      id="about"
      ref={sectionRef}
      className="section-padding bg-gray-50"
    >
      <div className="container-custom">
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center"
        >
          {/* Content */}
          <motion.div variants={staggerItem} className="about-content">
            <motion.div variants={staggerItem}>
              <span className="inline-block px-4 py-2 bg-primary-100 text-primary-800 rounded-full text-sm font-medium mb-6">
                About Tera Works
              </span>
            </motion.div>

            <motion.h2 
              variants={staggerItem}
              className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6"
            >
              Building Digital Success Stories
            </motion.h2>

            <motion.p 
              variants={staggerItem}
              className="text-lg text-gray-600 mb-8 leading-relaxed"
            >
              At Tera Works, we specialize in creating powerful digital experiences that drive business growth. 
              From custom website development to strategic Meta advertising campaigns, we combine creativity 
              with data-driven insights to deliver exceptional results.
            </motion.p>

            <motion.p 
              variants={staggerItem}
              className="text-lg text-gray-600 mb-8 leading-relaxed"
            >
              Our portfolio includes successful projects like spiceandherbrestaurant.com, zeynthra.com, 
              and chalakadulangaphotography.com, along with custom booking systems that streamline business operations.
            </motion.p>

            {/* Features Grid */}
            <motion.div 
              variants={staggerItem}
              className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8"
            >
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  variants={staggerItem}
                  className="flex items-start space-x-3"
                >
                  <div className="flex-shrink-0 w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600">
                    {feature.icon}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">{feature.title}</h3>
                    <p className="text-gray-600 text-sm">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>

            <motion.div variants={staggerItem}>
              <Button href="/about" variant="primary" size="lg">
                Learn More About Us
              </Button>
            </motion.div>
          </motion.div>

          {/* Visual Content */}
          <motion.div 
            variants={staggerItem}
            className="relative"
          >
            {/* Main Image */}
            <div className="relative">
              <div className="relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl">
                <Image
                  src="/images/about/team-working.jpg"
                  alt="Tera Works team working on projects"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>

              {/* Floating Stats Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.6 }}
                viewport={{ once: true }}
                className="absolute -bottom-6 -left-6 bg-white rounded-xl shadow-xl p-6 border border-gray-100"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <TrophyIcon className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900">5+ Years</div>
                    <div className="text-gray-600 text-sm">Experience</div>
                  </div>
                </div>
              </motion.div>

              {/* Floating Success Card */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 0.6 }}
                viewport={{ once: true }}
                className="absolute -top-6 -right-6 bg-white rounded-xl shadow-xl p-6 border border-gray-100"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <ChartBarIcon className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900">98%</div>
                    <div className="text-gray-600 text-sm">Success Rate</div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>

        {/* Statistics Section */}
        <motion.div
          ref={statsRef}
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="mt-20 pt-16 border-t border-gray-200"
        >
          <motion.div variants={staggerItem} className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Our Impact in Numbers
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              These numbers represent real results for real businesses. 
              Let us help you achieve similar success.
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
            {statistics.map((stat, index) => (
              <motion.div
                key={stat.id}
                variants={staggerItem}
                className="text-center"
              >
                <div className="text-4xl mb-2">{stat.icon}</div>
                <div 
                  className="text-3xl lg:text-4xl font-bold text-gray-900 mb-1"
                  data-stat-value={stat.value}
                >
                  0{stat.suffix}
                </div>
                <div className="text-gray-600 text-sm font-medium">
                  {stat.label}
                </div>
                <div className="text-gray-500 text-xs mt-1">
                  {stat.description}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
