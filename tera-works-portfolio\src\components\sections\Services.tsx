'use client';

import React, { useRef } from 'react';
import { motion } from 'framer-motion';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import { ServiceCard, Button } from '@/components/ui';
import { useGSAP } from '@gsap/react';
import { animations } from '@/lib/animations/gsap';
import { services } from '@/data/services';
import { staggerContainer, staggerItem } from '@/lib/animations/framer';

const Services: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const cardsRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    if (cardsRef.current) {
      const cards = cardsRef.current.querySelectorAll('.service-card');
      animations.serviceCards(Array.from(cards) as HTMLElement[]);
    }
  }, { scope: sectionRef });

  // Get featured services (first 6)
  const featuredServices = services.slice(0, 6);

  return (
    <section 
      id="services"
      ref={sectionRef}
      className="section-padding bg-white"
    >
      <div className="container-custom">
        {/* Header */}
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div variants={staggerItem}>
            <span className="inline-block px-4 py-2 bg-accent-100 text-accent-800 rounded-full text-sm font-medium mb-6">
              Our Services
            </span>
          </motion.div>

          <motion.h2 
            variants={staggerItem}
            className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6"
          >
            Comprehensive Digital Solutions
          </motion.h2>

          <motion.p 
            variants={staggerItem}
            className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed"
          >
            From custom website development to strategic Meta advertising campaigns, 
            we provide end-to-end digital solutions that drive growth and deliver measurable results.
          </motion.p>
        </motion.div>

        {/* Services Grid */}
        <div 
          ref={cardsRef}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
        >
          {featuredServices.map((service, index) => (
            <div key={service.id} className="service-card">
              <ServiceCard
                title={service.title}
                description={service.description}
                icon={service.icon}
                features={service.features}
                href={`/services/${service.id}`}
                className="h-full hover:shadow-xl transition-shadow duration-300"
              />
            </div>
          ))}
        </div>

        {/* Service Categories */}
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16"
        >
          {/* Web Development */}
          <motion.div 
            variants={staggerItem}
            className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-8 text-center"
          >
            <div className="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <span className="text-white text-2xl">💻</span>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Web Development</h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Custom websites, e-commerce platforms, and web applications built with modern technologies 
              for optimal performance and user experience.
            </p>
            <ul className="text-left space-y-2 mb-6">
              <li className="flex items-center text-gray-700">
                <ArrowRightIcon className="w-4 h-4 text-blue-600 mr-2 flex-shrink-0" />
                Custom Website Development
              </li>
              <li className="flex items-center text-gray-700">
                <ArrowRightIcon className="w-4 h-4 text-blue-600 mr-2 flex-shrink-0" />
                E-commerce Solutions
              </li>
              <li className="flex items-center text-gray-700">
                <ArrowRightIcon className="w-4 h-4 text-blue-600 mr-2 flex-shrink-0" />
                Booking Systems
              </li>
              <li className="flex items-center text-gray-700">
                <ArrowRightIcon className="w-4 h-4 text-blue-600 mr-2 flex-shrink-0" />
                Website Maintenance
              </li>
            </ul>
            <Button href="/services/web-development" variant="outline" className="w-full">
              Learn More
            </Button>
          </motion.div>

          {/* Meta Advertising */}
          <motion.div 
            variants={staggerItem}
            className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-8 text-center"
          >
            <div className="w-16 h-16 bg-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <span className="text-white text-2xl">📱</span>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Meta Advertising</h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Strategic Facebook and Instagram advertising campaigns that reach your target audience 
              and drive conversions with proven ROI.
            </p>
            <ul className="text-left space-y-2 mb-6">
              <li className="flex items-center text-gray-700">
                <ArrowRightIcon className="w-4 h-4 text-purple-600 mr-2 flex-shrink-0" />
                Campaign Strategy & Setup
              </li>
              <li className="flex items-center text-gray-700">
                <ArrowRightIcon className="w-4 h-4 text-purple-600 mr-2 flex-shrink-0" />
                Audience Targeting
              </li>
              <li className="flex items-center text-gray-700">
                <ArrowRightIcon className="w-4 h-4 text-purple-600 mr-2 flex-shrink-0" />
                Ad Creative Design
              </li>
              <li className="flex items-center text-gray-700">
                <ArrowRightIcon className="w-4 h-4 text-purple-600 mr-2 flex-shrink-0" />
                Performance Optimization
              </li>
            </ul>
            <Button href="/services/meta-advertising" variant="outline" className="w-full">
              Learn More
            </Button>
          </motion.div>

          {/* Consulting */}
          <motion.div 
            variants={staggerItem}
            className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-8 text-center"
          >
            <div className="w-16 h-16 bg-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <span className="text-white text-2xl">💡</span>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Digital Consulting</h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Strategic guidance and expert advice to help you make informed decisions 
              about your digital marketing and web development initiatives.
            </p>
            <ul className="text-left space-y-2 mb-6">
              <li className="flex items-center text-gray-700">
                <ArrowRightIcon className="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                Digital Strategy Planning
              </li>
              <li className="flex items-center text-gray-700">
                <ArrowRightIcon className="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                Technology Recommendations
              </li>
              <li className="flex items-center text-gray-700">
                <ArrowRightIcon className="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                Performance Audits
              </li>
              <li className="flex items-center text-gray-700">
                <ArrowRightIcon className="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                Growth Optimization
              </li>
            </ul>
            <Button href="/services/consulting" variant="outline" className="w-full">
              Learn More
            </Button>
          </motion.div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 lg:p-12 text-center text-white"
        >
          <motion.h3 
            variants={staggerItem}
            className="text-3xl lg:text-4xl font-bold mb-4"
          >
            Ready to Transform Your Digital Presence?
          </motion.h3>
          
          <motion.p 
            variants={staggerItem}
            className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto"
          >
            Let's discuss your project and create a custom solution that drives real results for your business.
          </motion.p>

          <motion.div 
            variants={staggerItem}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Button 
              href="/contact" 
              variant="secondary"
              size="lg"
              className="bg-white text-primary-600 hover:bg-gray-50"
            >
              Start Your Project
            </Button>
            
            <Button 
              href="/portfolio" 
              variant="outline"
              size="lg"
              className="border-white text-white hover:bg-white/10"
            >
              View Our Work
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Services;
