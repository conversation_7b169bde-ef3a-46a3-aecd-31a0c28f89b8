// Simple script to create placeholder images using data URLs
const fs = require('fs');
const path = require('path');

// Create a simple SVG placeholder
function createPlaceholder(width, height, text, bgColor = '#f3f4f6', textColor = '#6b7280') {
  return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="${bgColor}"/>
    <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="${textColor}" text-anchor="middle" dy=".3em">${text}</text>
  </svg>`;
}

// Create directories if they don't exist
const dirs = [
  'public/images/about',
  'public/images/portfolio',
  'public/images/testimonials',
  'public/images/services'
];

dirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Create placeholder images
const placeholders = [
  // About images
  { path: 'public/images/about/team-working.jpg', width: 600, height: 400, text: 'Team Working' },
  
  // Portfolio images
  { path: 'public/images/portfolio/spice-herb-thumb.jpg', width: 400, height: 300, text: 'Spice & Herb' },
  { path: 'public/images/portfolio/spice-herb-1.jpg', width: 800, height: 600, text: 'Spice & Herb Detail' },
  { path: 'public/images/portfolio/spice-herb-2.jpg', width: 800, height: 600, text: 'Spice & Herb Menu' },
  { path: 'public/images/portfolio/spice-herb-3.jpg', width: 800, height: 600, text: 'Spice & Herb Mobile' },
  
  { path: 'public/images/portfolio/zeynthra-thumb.jpg', width: 400, height: 300, text: 'Zeynthra Fashion' },
  { path: 'public/images/portfolio/zeynthra-1.jpg', width: 800, height: 600, text: 'Zeynthra Homepage' },
  { path: 'public/images/portfolio/zeynthra-2.jpg', width: 800, height: 600, text: 'Zeynthra Products' },
  { path: 'public/images/portfolio/zeynthra-3.jpg', width: 800, height: 600, text: 'Zeynthra Checkout' },
  
  { path: 'public/images/portfolio/chalakadulanga-thumb.jpg', width: 400, height: 300, text: 'Photography' },
  { path: 'public/images/portfolio/chalakadulanga-1.jpg', width: 800, height: 600, text: 'Photo Gallery' },
  { path: 'public/images/portfolio/chalakadulanga-2.jpg', width: 800, height: 600, text: 'Photo Booking' },
  { path: 'public/images/portfolio/chalakadulanga-3.jpg', width: 800, height: 600, text: 'Photo Portfolio' },
  
  { path: 'public/images/portfolio/booking-system-thumb.jpg', width: 400, height: 300, text: 'Booking System' },
  { path: 'public/images/portfolio/booking-system-1.jpg', width: 800, height: 600, text: 'Booking Calendar' },
  { path: 'public/images/portfolio/booking-system-2.jpg', width: 800, height: 600, text: 'Booking Form' },
  { path: 'public/images/portfolio/booking-system-3.jpg', width: 800, height: 600, text: 'Booking Admin' },
  
  { path: 'public/images/portfolio/meta-ads-restaurant-thumb.jpg', width: 400, height: 300, text: 'Meta Ads' },
  { path: 'public/images/portfolio/meta-ads-restaurant-1.jpg', width: 800, height: 600, text: 'Ad Campaign' },
  { path: 'public/images/portfolio/meta-ads-restaurant-2.jpg', width: 800, height: 600, text: 'Ad Results' },
  { path: 'public/images/portfolio/meta-ads-restaurant-3.jpg', width: 800, height: 600, text: 'Ad Analytics' },
  
  { path: 'public/images/portfolio/meta-ads-fashion-thumb.jpg', width: 400, height: 300, text: 'Fashion Ads' },
  { path: 'public/images/portfolio/meta-ads-fashion-1.jpg', width: 800, height: 600, text: 'Fashion Campaign' },
  { path: 'public/images/portfolio/meta-ads-fashion-2.jpg', width: 800, height: 600, text: 'Fashion Results' },
  { path: 'public/images/portfolio/meta-ads-fashion-3.jpg', width: 800, height: 600, text: 'Fashion ROI' },
  
  // Testimonial avatars
  { path: 'public/images/testimonials/sarah-johnson.jpg', width: 100, height: 100, text: 'SJ' },
  { path: 'public/images/testimonials/michael-chen.jpg', width: 100, height: 100, text: 'MC' },
  { path: 'public/images/testimonials/priya-patel.jpg', width: 100, height: 100, text: 'PP' },
  { path: 'public/images/testimonials/david-rodriguez.jpg', width: 100, height: 100, text: 'DR' },
  { path: 'public/images/testimonials/emma-thompson.jpg', width: 100, height: 100, text: 'ET' },
  { path: 'public/images/testimonials/james-wilson.jpg', width: 100, height: 100, text: 'JW' },
];

// Create OG image
const ogImage = createPlaceholder(1200, 630, 'Tera Works - Let\'s Grow Together', '#2563eb', '#ffffff');
fs.writeFileSync('public/og-image.jpg', ogImage);

// Create all placeholder images
placeholders.forEach(({ path: filePath, width, height, text }) => {
  const svg = createPlaceholder(width, height, text);
  fs.writeFileSync(filePath, svg);
  console.log(`Created: ${filePath}`);
});

console.log('All placeholder images created successfully!');
