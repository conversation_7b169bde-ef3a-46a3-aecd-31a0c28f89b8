import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700', '800'],
  variable: '--font-poppins',
  display: 'swap',
});

export const metadata: Metadata = {
  title: {
    default: 'Tera Works - Professional Web Development & Meta Advertising',
    template: '%s | Tera Works',
  },
  description: 'Professional website development and Meta advertising services. Let\'s grow together with custom web solutions and targeted social media campaigns.',
  keywords: ['web development', 'meta advertising', 'website design', 'social media marketing', 'facebook ads', 'instagram ads', 'booking systems'],
  authors: [{ name: 'Tera Works' }],
  creator: 'Tera Works',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://teraworks.com',
    siteName: 'Tera Works',
    title: 'Tera Works - Professional Web Development & Meta Advertising',
    description: 'Professional website development and Meta advertising services. Let\'s grow together.',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Tera Works - Let\'s Grow Together',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Tera Works - Professional Web Development & Meta Advertising',
    description: 'Professional website development and Meta advertising services.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable}`}>
      <body className="font-sans antialiased">
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
