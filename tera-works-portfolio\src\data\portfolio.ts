import { PortfolioItem } from '@/types';

export const portfolioItems: PortfolioItem[] = [
  {
    id: '1',
    title: 'Spice and Herb Restaurant',
    description: 'Modern restaurant website with online ordering and reservation system',
    longDescription: 'A comprehensive restaurant website featuring an elegant design, online menu, reservation system, and integrated ordering platform. Built with modern web technologies to provide an exceptional user experience.',
    category: 'web-development',
    technologies: ['Next.js', 'React', 'TypeScript', 'Tailwind CSS', 'Stripe', 'Sanity CMS'],
    images: {
      thumbnail: '/images/portfolio/spice-herb-thumb.jpg',
      gallery: [
        '/images/portfolio/spice-herb-1.jpg',
        '/images/portfolio/spice-herb-2.jpg',
        '/images/portfolio/spice-herb-3.jpg'
      ]
    },
    url: 'https://spiceandherbrestaurant.com',
    featured: true,
    completedAt: new Date('2024-03-15'),
    client: 'Spice & Herb Restaurant',
    results: [
      {
        metric: 'Online Orders',
        value: '+150%',
        description: 'Increase in online orders within 3 months'
      },
      {
        metric: 'Page Load Speed',
        value: '1.2s',
        description: 'Average page load time'
      },
      {
        metric: 'Mobile Traffic',
        value: '78%',
        description: 'Mobile users engagement rate'
      }
    ]
  },
  {
    id: '2',
    title: 'Zeynthra Fashion',
    description: 'E-commerce platform for luxury fashion brand with custom design',
    longDescription: 'A sophisticated e-commerce website for a luxury fashion brand, featuring custom product galleries, size guides, wishlist functionality, and seamless checkout experience.',
    category: 'web-development',
    technologies: ['Shopify', 'Liquid', 'JavaScript', 'SCSS', 'Shopify Plus'],
    images: {
      thumbnail: '/images/portfolio/zeynthra-thumb.jpg',
      gallery: [
        '/images/portfolio/zeynthra-1.jpg',
        '/images/portfolio/zeynthra-2.jpg',
        '/images/portfolio/zeynthra-3.jpg'
      ]
    },
    url: 'https://zeynthra.com',
    featured: true,
    completedAt: new Date('2024-01-20'),
    client: 'Zeynthra Fashion',
    results: [
      {
        metric: 'Conversion Rate',
        value: '+85%',
        description: 'Improvement in conversion rate'
      },
      {
        metric: 'Average Order Value',
        value: '+42%',
        description: 'Increase in average order value'
      },
      {
        metric: 'Customer Retention',
        value: '68%',
        description: 'Returning customer rate'
      }
    ]
  },
  {
    id: '3',
    title: 'Chalakadulanga Photography',
    description: 'Professional photography portfolio with booking system integration',
    longDescription: 'A stunning photography portfolio website showcasing professional work with an integrated booking system for client appointments and session management.',
    category: 'web-development',
    technologies: ['React', 'Node.js', 'MongoDB', 'Express', 'Cloudinary', 'Stripe'],
    images: {
      thumbnail: '/images/portfolio/chalakadulanga-thumb.jpg',
      gallery: [
        '/images/portfolio/chalakadulanga-1.jpg',
        '/images/portfolio/chalakadulanga-2.jpg',
        '/images/portfolio/chalakadulanga-3.jpg'
      ]
    },
    url: 'https://chalakadulangaphotography.com',
    featured: true,
    completedAt: new Date('2023-11-10'),
    client: 'Chalakadulanga Photography',
    results: [
      {
        metric: 'Booking Inquiries',
        value: '+200%',
        description: 'Increase in booking inquiries'
      },
      {
        metric: 'Portfolio Views',
        value: '15K+',
        description: 'Monthly portfolio views'
      },
      {
        metric: 'Client Satisfaction',
        value: '4.9/5',
        description: 'Average client rating'
      }
    ]
  },
  {
    id: '4',
    title: 'Photography Booking System',
    description: 'Custom booking platform for photography sessions and events',
    longDescription: 'A comprehensive booking system specifically designed for photography services, featuring calendar integration, payment processing, client management, and automated email notifications.',
    category: 'booking-system',
    technologies: ['Next.js', 'PostgreSQL', 'Prisma', 'NextAuth', 'Stripe', 'SendGrid'],
    images: {
      thumbnail: '/images/portfolio/booking-system-thumb.jpg',
      gallery: [
        '/images/portfolio/booking-system-1.jpg',
        '/images/portfolio/booking-system-2.jpg',
        '/images/portfolio/booking-system-3.jpg'
      ]
    },
    url: 'https://booking.chalakadulangaphotography.com',
    featured: true,
    completedAt: new Date('2023-12-05'),
    client: 'Chalakadulanga Photography',
    results: [
      {
        metric: 'Booking Efficiency',
        value: '+300%',
        description: 'Faster booking process'
      },
      {
        metric: 'No-shows Reduced',
        value: '-75%',
        description: 'Reduction in missed appointments'
      },
      {
        metric: 'Admin Time Saved',
        value: '20hrs/week',
        description: 'Time saved on booking management'
      }
    ]
  },
  {
    id: '5',
    title: 'Local Restaurant Meta Ads Campaign',
    description: 'Comprehensive Meta advertising campaign for local restaurant chain',
    longDescription: 'A strategic Meta advertising campaign targeting local customers for a restaurant chain, focusing on increasing foot traffic, online orders, and brand awareness through targeted Facebook and Instagram ads.',
    category: 'meta-advertising',
    technologies: ['Meta Ads Manager', 'Facebook Pixel', 'Google Analytics', 'Looker Studio'],
    images: {
      thumbnail: '/images/portfolio/meta-ads-restaurant-thumb.jpg',
      gallery: [
        '/images/portfolio/meta-ads-restaurant-1.jpg',
        '/images/portfolio/meta-ads-restaurant-2.jpg',
        '/images/portfolio/meta-ads-restaurant-3.jpg'
      ]
    },
    featured: true,
    completedAt: new Date('2024-02-28'),
    client: 'Local Restaurant Chain',
    results: [
      {
        metric: 'ROAS',
        value: '4.2x',
        description: 'Return on ad spend'
      },
      {
        metric: 'Foot Traffic',
        value: '+65%',
        description: 'Increase in restaurant visits'
      },
      {
        metric: 'Online Orders',
        value: '+120%',
        description: 'Growth in online orders'
      }
    ]
  },
  {
    id: '6',
    title: 'E-commerce Fashion Brand Campaign',
    description: 'Meta advertising strategy for fashion e-commerce brand expansion',
    longDescription: 'A comprehensive Meta advertising strategy for a fashion e-commerce brand, focusing on customer acquisition, retargeting, and brand awareness across Facebook and Instagram platforms.',
    category: 'meta-advertising',
    technologies: ['Meta Ads Manager', 'Facebook Pixel', 'Shopify Analytics', 'Klaviyo'],
    images: {
      thumbnail: '/images/portfolio/meta-ads-fashion-thumb.jpg',
      gallery: [
        '/images/portfolio/meta-ads-fashion-1.jpg',
        '/images/portfolio/meta-ads-fashion-2.jpg',
        '/images/portfolio/meta-ads-fashion-3.jpg'
      ]
    },
    featured: false,
    completedAt: new Date('2024-01-15'),
    client: 'Fashion E-commerce Brand',
    results: [
      {
        metric: 'ROAS',
        value: '5.8x',
        description: 'Return on ad spend'
      },
      {
        metric: 'Customer Acquisition',
        value: '+180%',
        description: 'New customer growth'
      },
      {
        metric: 'Brand Awareness',
        value: '+95%',
        description: 'Increase in brand recognition'
      }
    ]
  }
];

export const portfolioCategories = [
  { id: 'all', label: 'All Projects', count: portfolioItems.length },
  { 
    id: 'web-development', 
    label: 'Web Development', 
    count: portfolioItems.filter(item => item.category === 'web-development').length 
  },
  { 
    id: 'meta-advertising', 
    label: 'Meta Advertising', 
    count: portfolioItems.filter(item => item.category === 'meta-advertising').length 
  },
  { 
    id: 'booking-system', 
    label: 'Booking Systems', 
    count: portfolioItems.filter(item => item.category === 'booking-system').length 
  }
];

export const featuredProjects = portfolioItems.filter(item => item.featured);

export const getProjectById = (id: string): PortfolioItem | undefined => {
  return portfolioItems.find(item => item.id === id);
};

export const getProjectsByCategory = (category: string): PortfolioItem[] => {
  if (category === 'all') return portfolioItems;
  return portfolioItems.filter(item => item.category === category);
};
