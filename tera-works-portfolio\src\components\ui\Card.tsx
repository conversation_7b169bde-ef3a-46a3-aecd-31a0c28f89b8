'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { CardProps } from '@/types';
import { cardVariants } from '@/lib/animations/framer';

const Card: React.FC<CardProps> = ({
  children,
  className,
  title,
  description,
  image,
  href,
  hover = true,
  ...props
}) => {
  const baseClasses = 'bg-white rounded-lg shadow-md overflow-hidden transition-shadow duration-300';
  
  const classes = cn(baseClasses, className);

  const cardContent = (
    <motion.div
      className={classes}
      variants={hover ? cardVariants : undefined}
      initial="idle"
      whileHover={hover ? "hover" : "idle"}
      {...props}
    >
      {image && (
        <div className="relative h-48 w-full">
          <Image
            src={image}
            alt={title || 'Card image'}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
      )}
      
      <div className="p-6">
        {title && (
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            {title}
          </h3>
        )}
        
        {description && (
          <p className="text-gray-600 mb-4">
            {description}
          </p>
        )}
        
        {children}
      </div>
    </motion.div>
  );

  if (href) {
    return (
      <Link href={href} className="block">
        {cardContent}
      </Link>
    );
  }

  return cardContent;
};

// Service Card Component
export const ServiceCard: React.FC<{
  title: string;
  description: string;
  icon: string;
  features: string[];
  href?: string;
  className?: string;
}> = ({ title, description, icon, features, href, className }) => {
  return (
    <Card
      title={title}
      description={description}
      href={href}
      className={cn('h-full', className)}
    >
      <div className="flex items-center mb-4">
        <span className="text-4xl mr-3">{icon}</span>
        <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
      </div>
      
      <p className="text-gray-600 mb-4">{description}</p>
      
      <ul className="space-y-2">
        {features.slice(0, 4).map((feature, index) => (
          <li key={index} className="flex items-center text-sm text-gray-600">
            <svg
              className="w-4 h-4 text-green-500 mr-2 flex-shrink-0"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
            {feature}
          </li>
        ))}
      </ul>
    </Card>
  );
};

// Portfolio Card Component
export const PortfolioCard: React.FC<{
  title: string;
  description: string;
  image: string;
  technologies: string[];
  href?: string;
  className?: string;
}> = ({ title, description, image, technologies, href, className }) => {
  return (
    <Card
      title={title}
      description={description}
      image={image}
      href={href}
      className={cn('h-full', className)}
    >
      <div className="flex flex-wrap gap-2 mt-4">
        {technologies.slice(0, 3).map((tech, index) => (
          <span
            key={index}
            className="px-2 py-1 bg-primary-100 text-primary-800 text-xs rounded-full"
          >
            {tech}
          </span>
        ))}
        {technologies.length > 3 && (
          <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
            +{technologies.length - 3} more
          </span>
        )}
      </div>
    </Card>
  );
};

// Testimonial Card Component
export const TestimonialCard: React.FC<{
  name: string;
  position: string;
  company: string;
  content: string;
  avatar?: string;
  rating: number;
  className?: string;
}> = ({ name, position, company, content, avatar, rating, className }) => {
  return (
    <Card className={cn('h-full', className)} hover={false}>
      <div className="flex items-center mb-4">
        {[...Array(5)].map((_, i) => (
          <svg
            key={i}
            className={cn(
              'w-5 h-5',
              i < rating ? 'text-yellow-400' : 'text-gray-300'
            )}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
      
      <blockquote className="text-gray-600 mb-4 italic">
        "{content}"
      </blockquote>
      
      <div className="flex items-center">
        {avatar && (
          <div className="relative w-12 h-12 mr-4">
            <Image
              src={avatar}
              alt={name}
              fill
              className="rounded-full object-cover"
            />
          </div>
        )}
        <div>
          <div className="font-semibold text-gray-900">{name}</div>
          <div className="text-sm text-gray-600">
            {position} at {company}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default Card;
