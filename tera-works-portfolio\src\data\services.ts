import { Service } from '@/types';

export const services: Service[] = [
  {
    id: '1',
    title: 'Custom Website Development',
    description: 'Professional websites built with modern technologies for optimal performance and user experience.',
    longDescription: 'We create custom websites tailored to your business needs using the latest web technologies. From simple business websites to complex web applications, we ensure your online presence stands out with exceptional design and functionality.',
    icon: '🌐',
    features: [
      'Responsive Design for All Devices',
      'SEO Optimization',
      'Fast Loading Speed',
      'Content Management System',
      'Security Implementation',
      'Analytics Integration',
      'Social Media Integration',
      'Contact Forms & Lead Generation'
    ],
    pricing: {
      type: 'project',
      startingPrice: 2500,
      currency: 'USD'
    },
    deliverables: [
      'Fully Responsive Website',
      'Content Management System',
      'SEO Setup & Optimization',
      'Google Analytics Integration',
      'Contact Forms',
      '3 Months Free Support',
      'Training Documentation',
      'Source Code & Assets'
    ],
    timeline: '4-8 weeks',
    category: 'web-development'
  },
  {
    id: '2',
    title: 'E-commerce Solutions',
    description: 'Complete online stores with payment processing, inventory management, and customer analytics.',
    longDescription: 'Build a powerful online store that drives sales and grows your business. Our e-commerce solutions include everything you need to sell online, from product catalogs to secure payment processing and order management.',
    icon: '🛒',
    features: [
      'Product Catalog Management',
      'Secure Payment Processing',
      'Inventory Management',
      'Order Tracking System',
      'Customer Account Portal',
      'Shopping Cart & Wishlist',
      'Discount & Coupon System',
      'Multi-currency Support'
    ],
    pricing: {
      type: 'project',
      startingPrice: 4500,
      currency: 'USD'
    },
    deliverables: [
      'Complete E-commerce Website',
      'Payment Gateway Integration',
      'Admin Dashboard',
      'Product Management System',
      'Order Management System',
      'Customer Support Tools',
      '6 Months Free Support',
      'Staff Training'
    ],
    timeline: '6-12 weeks',
    category: 'web-development'
  },
  {
    id: '3',
    title: 'Booking System Development',
    description: 'Custom booking platforms for appointments, events, and service reservations.',
    longDescription: 'Streamline your booking process with a custom booking system designed for your specific business needs. Perfect for service providers, consultants, restaurants, and event organizers.',
    icon: '📅',
    features: [
      'Calendar Integration',
      'Automated Email Notifications',
      'Payment Processing',
      'Customer Management',
      'Availability Management',
      'Booking Confirmations',
      'Cancellation Handling',
      'Reporting & Analytics'
    ],
    pricing: {
      type: 'project',
      startingPrice: 3500,
      currency: 'USD'
    },
    deliverables: [
      'Custom Booking Platform',
      'Calendar Integration',
      'Payment System',
      'Email Automation',
      'Admin Dashboard',
      'Customer Portal',
      '4 Months Free Support',
      'User Training'
    ],
    timeline: '5-10 weeks',
    category: 'web-development'
  },
  {
    id: '4',
    title: 'Meta Advertising Campaigns',
    description: 'Strategic Facebook and Instagram advertising to reach your target audience and drive conversions.',
    longDescription: 'Maximize your reach and ROI with expertly crafted Meta advertising campaigns. We create, manage, and optimize Facebook and Instagram ads to help you achieve your business goals.',
    icon: '📱',
    features: [
      'Campaign Strategy Development',
      'Audience Research & Targeting',
      'Ad Creative Design',
      'A/B Testing',
      'Performance Monitoring',
      'ROI Optimization',
      'Detailed Reporting',
      'Ongoing Campaign Management'
    ],
    pricing: {
      type: 'project',
      startingPrice: 1500,
      currency: 'USD'
    },
    deliverables: [
      'Campaign Strategy Document',
      'Ad Creative Assets',
      'Audience Targeting Setup',
      'Campaign Launch',
      'Weekly Performance Reports',
      'Monthly Optimization',
      '3 Months Campaign Management',
      'Final Results Analysis'
    ],
    timeline: '2-4 weeks setup + ongoing',
    category: 'meta-advertising'
  },
  {
    id: '5',
    title: 'Social Media Marketing',
    description: 'Comprehensive social media strategy and management across multiple platforms.',
    longDescription: 'Build your brand presence and engage with your audience through strategic social media marketing. We handle content creation, posting schedules, and community management.',
    icon: '📊',
    features: [
      'Content Strategy Development',
      'Content Creation & Design',
      'Posting Schedule Management',
      'Community Engagement',
      'Hashtag Research',
      'Performance Analytics',
      'Competitor Analysis',
      'Brand Voice Development'
    ],
    pricing: {
      type: 'project',
      startingPrice: 1200,
      currency: 'USD'
    },
    deliverables: [
      'Social Media Strategy',
      'Content Calendar',
      'Custom Graphics & Posts',
      'Account Setup & Optimization',
      'Monthly Analytics Reports',
      'Community Management',
      '3 Months Content Creation',
      'Performance Review'
    ],
    timeline: '1-2 weeks setup + ongoing',
    category: 'meta-advertising'
  },
  {
    id: '6',
    title: 'Website Maintenance & Support',
    description: 'Ongoing website maintenance, updates, and technical support to keep your site running smoothly.',
    longDescription: 'Keep your website secure, updated, and performing at its best with our comprehensive maintenance and support services. We handle all the technical aspects so you can focus on your business.',
    icon: '🔧',
    features: [
      'Regular Security Updates',
      'Performance Monitoring',
      'Backup Management',
      'Content Updates',
      'Bug Fixes & Troubleshooting',
      'SEO Monitoring',
      'Analytics Reporting',
      '24/7 Technical Support'
    ],
    pricing: {
      type: 'project',
      startingPrice: 200,
      currency: 'USD'
    },
    deliverables: [
      'Monthly Security Updates',
      'Performance Reports',
      'Backup Services',
      'Content Updates (up to 5 hours)',
      'Bug Fixes',
      'SEO Health Checks',
      'Analytics Reports',
      'Priority Support'
    ],
    timeline: 'Ongoing monthly service',
    category: 'web-development'
  }
];

export const serviceCategories = [
  {
    id: 'web-development',
    label: 'Web Development',
    description: 'Custom websites and web applications',
    icon: '💻',
    services: services.filter(service => service.category === 'web-development')
  },
  {
    id: 'meta-advertising',
    label: 'Meta Advertising',
    description: 'Facebook and Instagram marketing',
    icon: '📱',
    services: services.filter(service => service.category === 'meta-advertising')
  },
  {
    id: 'consulting',
    label: 'Consulting',
    description: 'Strategic digital marketing advice',
    icon: '💡',
    services: services.filter(service => service.category === 'consulting')
  }
];

export const getServiceById = (id: string): Service | undefined => {
  return services.find(service => service.id === id);
};

export const getServicesByCategory = (category: string): Service[] => {
  return services.filter(service => service.category === category);
};
