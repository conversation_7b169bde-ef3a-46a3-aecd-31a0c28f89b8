'use client';

import React, { useRef, useState } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowTopRightOnSquareIcon, EyeIcon } from '@heroicons/react/24/outline';
import { PortfolioCard, Button } from '@/components/ui';
import { useGSAP } from '@gsap/react';
import { animations } from '@/lib/animations/gsap';
import { portfolioItems, portfolioCategories } from '@/data/portfolio';
import { staggerContainer, staggerItem, portfolioGridVariants, portfolioItemVariants } from '@/lib/animations/framer';

const Portfolio: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);
  const [activeFilter, setActiveFilter] = useState('all');

  useGSAP(() => {
    if (gridRef.current) {
      const items = gridRef.current.querySelectorAll('.portfolio-item');
      animations.portfolioItems(Array.from(items) as HTMLElement[]);
    }
  }, { scope: sectionRef, dependencies: [activeFilter] });

  const filteredProjects = activeFilter === 'all' 
    ? portfolioItems 
    : portfolioItems.filter(item => item.category === activeFilter);

  const handleFilterChange = (filter: string) => {
    setActiveFilter(filter);
  };

  return (
    <section 
      id="portfolio"
      ref={sectionRef}
      className="section-padding bg-gray-50"
    >
      <div className="container-custom">
        {/* Header */}
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div variants={staggerItem}>
            <span className="inline-block px-4 py-2 bg-primary-100 text-primary-800 rounded-full text-sm font-medium mb-6">
              Our Portfolio
            </span>
          </motion.div>

          <motion.h2 
            variants={staggerItem}
            className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6"
          >
            Success Stories & Case Studies
          </motion.h2>

          <motion.p 
            variants={staggerItem}
            className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed"
          >
            Explore our portfolio of successful projects that have helped businesses grow, 
            increase conversions, and achieve their digital marketing goals.
          </motion.p>
        </motion.div>

        {/* Filter Tabs */}
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {portfolioCategories.map((category) => (
            <motion.button
              key={category.id}
              variants={staggerItem}
              onClick={() => handleFilterChange(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeFilter === category.id
                  ? 'bg-primary-600 text-white shadow-lg'
                  : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category.label}
              <span className="ml-2 text-sm opacity-75">({category.count})</span>
            </motion.button>
          ))}
        </motion.div>

        {/* Portfolio Grid */}
        <motion.div
          ref={gridRef}
          variants={portfolioGridVariants}
          initial="hidden"
          animate="show"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
        >
          <AnimatePresence mode="wait">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={`${project.id}-${activeFilter}`}
                variants={portfolioItemVariants}
                initial="hidden"
                animate="show"
                exit="hidden"
                layout
                className="portfolio-item"
              >
                <div className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 group">
                  {/* Project Image */}
                  <div className="relative h-64 overflow-hidden">
                    <Image
                      src={project.images.thumbnail}
                      alt={project.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
                    
                    {/* Overlay Actions */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="flex space-x-3">
                        <Button
                          href={`/portfolio/${project.id}`}
                          variant="secondary"
                          size="sm"
                          className="bg-white/90 text-gray-900 hover:bg-white"
                        >
                          <EyeIcon className="w-4 h-4 mr-2" />
                          View Details
                        </Button>
                        {project.url && (
                          <Button
                            href={project.url}
                            external
                            variant="outline"
                            size="sm"
                            className="bg-white/90 border-white/90 text-gray-900 hover:bg-white"
                          >
                            <ArrowTopRightOnSquareIcon className="w-4 h-4 mr-2" />
                            Live Site
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Featured Badge */}
                    {project.featured && (
                      <div className="absolute top-4 left-4">
                        <span className="bg-accent-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                          Featured
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Project Info */}
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm text-primary-600 font-medium capitalize">
                        {project.category.replace('-', ' ')}
                      </span>
                      <span className="text-sm text-gray-500">
                        {project.completedAt.toLocaleDateString()}
                      </span>
                    </div>

                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors">
                      {project.title}
                    </h3>

                    <p className="text-gray-600 mb-4 line-clamp-2">
                      {project.description}
                    </p>

                    {/* Technologies */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.technologies.slice(0, 3).map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md"
                        >
                          {tech}
                        </span>
                      ))}
                      {project.technologies.length > 3 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md">
                          +{project.technologies.length - 3} more
                        </span>
                      )}
                    </div>

                    {/* Results */}
                    {project.results && project.results.length > 0 && (
                      <div className="border-t border-gray-100 pt-4">
                        <div className="grid grid-cols-2 gap-4">
                          {project.results.slice(0, 2).map((result, resultIndex) => (
                            <div key={resultIndex} className="text-center">
                              <div className="text-lg font-bold text-primary-600">
                                {result.value}
                              </div>
                              <div className="text-xs text-gray-600">
                                {result.metric}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="text-center"
        >
          <motion.h3 
            variants={staggerItem}
            className="text-3xl font-bold text-gray-900 mb-4"
          >
            Ready to Start Your Success Story?
          </motion.h3>
          
          <motion.p 
            variants={staggerItem}
            className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto"
          >
            Join our growing list of satisfied clients and let us help you achieve 
            similar results for your business.
          </motion.p>

          <motion.div 
            variants={staggerItem}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Button href="/contact" variant="primary" size="lg">
              Start Your Project
            </Button>
            
            <Button href="/portfolio" variant="outline" size="lg">
              View All Projects
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Portfolio;
