/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.tsx */ \"(rsc)/./src/app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\"));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\layout.tsx\"],\n'error': [module1, \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\"],\n'loading': [module2, \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(rsc)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(rsc)/./src/components/layout/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNBdWdtZW50JTIwQ29kZSUyMFRlc3RpbmclNUMlNUN0ZXJhLXdvcmtzLXBvcnRmb2xpbyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXdHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxBdWdtZW50IENvZGUgVGVzdGluZ1xcXFx0ZXJhLXdvcmtzLXBvcnRmb2xpb1xcXFxzcmNcXFxcYXBwXFxcXGVycm9yLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCard.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22ServiceCard%22%2C%22PortfolioCard%22%2C%22TestimonialCard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CInput.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22Textarea%22%2C%22Select%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CModal.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCard.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22ServiceCard%22%2C%22PortfolioCard%22%2C%22TestimonialCard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CInput.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22Textarea%22%2C%22Select%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CModal.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/Button.tsx */ \"(rsc)/./src/components/ui/Button.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/Card.tsx */ \"(rsc)/./src/components/ui/Card.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/Input.tsx */ \"(rsc)/./src/components/ui/Input.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/Modal.tsx */ \"(rsc)/./src/components/ui/Modal.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCard.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22ServiceCard%22%2C%22PortfolioCard%22%2C%22TestimonialCard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CInput.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22Textarea%22%2C%22Select%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CModal.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkU6XFxBdWdtZW50IENvZGUgVGVzdGluZ1xcdGVyYS13b3Jrcy1wb3J0Zm9saW9cXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"be1474b548a0\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxcQXVnbWVudCBDb2RlIFRlc3RpbmdcXHRlcmEtd29ya3MtcG9ydGZvbGlvXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJiZTE0NzRiNTQ4YTBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\",\"800\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\"],\\\"variable\\\":\\\"--font-poppins\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: 'Tera Works - Professional Web Development & Meta Advertising',\n        template: '%s | Tera Works'\n    },\n    description: 'Professional website development and Meta advertising services. Let\\'s grow together with custom web solutions and targeted social media campaigns.',\n    keywords: [\n        'web development',\n        'meta advertising',\n        'website design',\n        'social media marketing',\n        'facebook ads',\n        'instagram ads',\n        'booking systems'\n    ],\n    authors: [\n        {\n            name: 'Tera Works'\n        }\n    ],\n    creator: 'Tera Works',\n    openGraph: {\n        type: 'website',\n        locale: 'en_US',\n        url: 'https://teraworks.com',\n        siteName: 'Tera Works',\n        title: 'Tera Works - Professional Web Development & Meta Advertising',\n        description: 'Professional website development and Meta advertising services. Let\\'s grow together.',\n        images: [\n            {\n                url: '/og-image.jpg',\n                width: 1200,\n                height: 630,\n                alt: 'Tera Works - Let\\'s Grow Together'\n            }\n        ]\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'Tera Works - Professional Web Development & Meta Advertising',\n        description: 'Professional website development and Meta advertising services.',\n        images: [\n            '/og-image.jpg'\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_400_500_600_700_800_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_5___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans antialiased\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"min-h-screen\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.tsx":
/*!*****************************!*\
  !*** ./src/app/loading.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\loading.tsx\",\n                        lineNumber: 6,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Please wait while we prepare your content\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\loading.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7OEJBRWpCLDhEQUFDQztvQkFBR0QsV0FBVTs4QkFBMkM7Ozs7Ozs4QkFDekQsOERBQUNFO29CQUFFRixXQUFVOzhCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJckMiLCJzb3VyY2VzIjpbIkU6XFxBdWdtZW50IENvZGUgVGVzdGluZ1xcdGVyYS13b3Jrcy1wb3J0Zm9saW9cXHNyY1xcYXBwXFxsb2FkaW5nLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLWdyYXktNTBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJvcmRlci00IGJvcmRlci1wcmltYXJ5LTIwMCBib3JkZXItdC1wcmltYXJ5LTYwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1zcGluIG14LWF1dG8gbWItNFwiPjwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5Mb2FkaW5nLi4uPC9oMj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlBsZWFzZSB3YWl0IHdoaWxlIHdlIHByZXBhcmUgeW91ciBjb250ZW50PC9wPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui */ \"(rsc)/./src/components/ui/index.ts\");\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center max-w-md mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl font-bold text-primary-600 mb-4\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"Page Not Found\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            href: \"/\",\n                            variant: \"primary\",\n                            className: \"w-full\",\n                            children: \"Go Home\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            href: \"/contact\",\n                            variant: \"outline\",\n                            className: \"w-full\",\n                            children: \"Contact Us\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\not-found.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\not-found.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\not-found.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                    children: \"Tera Works Portfolio\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg text-gray-600\",\n                    children: \"Website is loading successfully!\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFHRCxXQUFVOzhCQUF3Qzs7Ozs7OzhCQUd0RCw4REFBQ0U7b0JBQUVGLFdBQVU7OEJBQXdCOzs7Ozs7Ozs7Ozs7Ozs7OztBQU03QyIsInNvdXJjZXMiOlsiRTpcXEF1Z21lbnQgQ29kZSBUZXN0aW5nXFx0ZXJhLXdvcmtzLXBvcnRmb2xpb1xcc3JjXFxhcHBcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTRcIj5cbiAgICAgICAgICBUZXJhIFdvcmtzIFBvcnRmb2xpb1xuICAgICAgICA8L2gxPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICBXZWJzaXRlIGlzIGxvYWRpbmcgc3VjY2Vzc2Z1bGx5IVxuICAgICAgICA8L3A+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJIb21lIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Footer.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Header.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Button.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Button.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PortfolioCard: () => (/* binding */ PortfolioCard),
/* harmony export */   ServiceCard: () => (/* binding */ ServiceCard),
/* harmony export */   TestimonialCard: () => (/* binding */ TestimonialCard),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ServiceCard = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ServiceCard() from the server but ServiceCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Card.tsx",
"ServiceCard",
);const PortfolioCard = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call PortfolioCard() from the server but PortfolioCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Card.tsx",
"PortfolioCard",
);const TestimonialCard = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call TestimonialCard() from the server but TestimonialCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Card.tsx",
"TestimonialCard",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Card.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/Input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Select: () => (/* binding */ Select),
/* harmony export */   Textarea: () => (/* binding */ Textarea),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Textarea = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Textarea() from the server but Textarea is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Input.tsx",
"Textarea",
);const Select = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Select() from the server but Select is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Input.tsx",
"Select",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Input.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/Modal.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Modal.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Modal.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Modal.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/index.ts":
/*!************************************!*\
  !*** ./src/components/ui/index.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* reexport safe */ _Button__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Card: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Input: () => (/* reexport safe */ _Input__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Modal: () => (/* reexport safe */ _Modal__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   PortfolioCard: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__.PortfolioCard),\n/* harmony export */   Select: () => (/* reexport safe */ _Input__WEBPACK_IMPORTED_MODULE_2__.Select),\n/* harmony export */   ServiceCard: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__.ServiceCard),\n/* harmony export */   TestimonialCard: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__.TestimonialCard),\n/* harmony export */   Textarea: () => (/* reexport safe */ _Input__WEBPACK_IMPORTED_MODULE_2__.Textarea)\n/* harmony export */ });\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Button */ \"(rsc)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Card */ \"(rsc)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _Input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Input */ \"(rsc)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _Modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Modal */ \"(rsc)/./src/components/ui/Modal.tsx\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy91aS9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQTZDO0FBQ3lDO0FBQ3pCO0FBQ2xCIiwic291cmNlcyI6WyJFOlxcQXVnbWVudCBDb2RlIFRlc3RpbmdcXHRlcmEtd29ya3MtcG9ydGZvbGlvXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBkZWZhdWx0IGFzIEJ1dHRvbiB9IGZyb20gJy4vQnV0dG9uJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2FyZCwgU2VydmljZUNhcmQsIFBvcnRmb2xpb0NhcmQsIFRlc3RpbW9uaWFsQ2FyZCB9IGZyb20gJy4vQ2FyZCc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIElucHV0LCBUZXh0YXJlYSwgU2VsZWN0IH0gZnJvbSAnLi9JbnB1dCc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIE1vZGFsIH0gZnJvbSAnLi9Nb2RhbCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsIkJ1dHRvbiIsIkNhcmQiLCJTZXJ2aWNlQ2FyZCIsIlBvcnRmb2xpb0NhcmQiLCJUZXN0aW1vbmlhbENhcmQiLCJJbnB1dCIsIlRleHRhcmVhIiwiU2VsZWN0IiwiTW9kYWwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/index.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(ssr)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNBdWdtZW50JTIwQ29kZSUyMFRlc3RpbmclNUMlNUN0ZXJhLXdvcmtzLXBvcnRmb2xpbyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQXdHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxBdWdtZW50IENvZGUgVGVzdGluZ1xcXFx0ZXJhLXdvcmtzLXBvcnRmb2xpb1xcXFxzcmNcXFxcYXBwXFxcXGVycm9yLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCard.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22ServiceCard%22%2C%22PortfolioCard%22%2C%22TestimonialCard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CInput.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22Textarea%22%2C%22Select%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CModal.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCard.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22ServiceCard%22%2C%22PortfolioCard%22%2C%22TestimonialCard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CInput.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22Textarea%22%2C%22Select%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CModal.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/Button.tsx */ \"(ssr)/./src/components/ui/Button.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/Card.tsx */ \"(ssr)/./src/components/ui/Card.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/Input.tsx */ \"(ssr)/./src/components/ui/Input.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/Modal.tsx */ \"(ssr)/./src/components/ui/Modal.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCard.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22ServiceCard%22%2C%22PortfolioCard%22%2C%22TestimonialCard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CInput.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22Textarea%22%2C%22Select%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CModal.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            console.error(error);\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center max-w-md mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"Something went wrong!\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"We apologize for the inconvenience. An error occurred while loading this page.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: reset,\n                            variant: \"primary\",\n                            className: \"w-full\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            href: \"/\",\n                            variant: \"outline\",\n                            className: \"w-full\",\n                            children: \"Go Home\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _data_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/navigation */ \"(ssr)/./src/data/navigation.ts\");\n/* harmony import */ var _lib_animations_framer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/animations/framer */ \"(ssr)/./src/lib/animations/framer.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const SocialIcon = ({ platform, className = \"w-5 h-5\" })=>{\n        switch(platform.toLowerCase()){\n            case 'linkedin':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: className,\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, undefined);\n            case 'twitter':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: className,\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, undefined);\n            case 'facebook':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: className,\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, undefined);\n            case 'instagram':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: className,\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.596-3.205-1.533l1.714-1.714c.39.586 1.07.977 1.85.977.98 0 1.776-.796 1.776-1.776 0-.98-.796-1.776-1.776-1.776-.78 0-1.46.391-1.85.977L4.244 10.43c.757-.937 1.908-1.533 3.205-1.533 2.269 0 4.106 1.837 4.106 4.106s-1.837 4.106-4.106 4.106zm7.441 0c-2.269 0-4.106-1.837-4.106-4.106s1.837-4.106 4.106-4.106c1.297 0 2.448.596 3.205 1.533l-1.714 1.714c-.39-.586-1.07-.977-1.85-.977-.98 0-1.776.796-1.776 1.776 0 .98.796 1.776 1.776 1.776.78 0 1.46-.391 1.85-.977l1.714 1.714c-.757.937-1.908 1.533-3.205 1.533z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, undefined);\n            case 'github':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: className,\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: className\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_5__.staggerContainer,\n                    initial: \"initial\",\n                    whileInView: \"animate\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"py-16 lg:py-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_5__.staggerItem,\n                                className: \"lg:col-span-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center space-x-2 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-10 h-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: \"/logo.png\",\n                                                    alt: \"Tera Works Logo\",\n                                                    fill: true,\n                                                    className: \"object-contain\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: \"Tera Works\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-400 -mt-1\",\n                                                        children: \"Let's Grow Together\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mb-6 text-sm leading-relaxed\",\n                                        children: \"Professional web development and Meta advertising services to help your business grow and succeed online.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4 text-primary-400 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: `mailto:${_data_navigation__WEBPACK_IMPORTED_MODULE_4__.contactInfo.email}`,\n                                                        className: \"text-gray-300 hover:text-white transition-colors\",\n                                                        children: _data_navigation__WEBPACK_IMPORTED_MODULE_4__.contactInfo.email\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4 text-primary-400 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: `tel:${_data_navigation__WEBPACK_IMPORTED_MODULE_4__.contactInfo.phone}`,\n                                                        className: \"text-gray-300 hover:text-white transition-colors\",\n                                                        children: _data_navigation__WEBPACK_IMPORTED_MODULE_4__.contactInfo.phone\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4 text-primary-400 flex-shrink-0 mt-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: _data_navigation__WEBPACK_IMPORTED_MODULE_4__.contactInfo.address.street\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                                lineNumber: 115,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    _data_navigation__WEBPACK_IMPORTED_MODULE_4__.contactInfo.address.city,\n                                                                    \", \",\n                                                                    _data_navigation__WEBPACK_IMPORTED_MODULE_4__.contactInfo.address.state,\n                                                                    \" \",\n                                                                    _data_navigation__WEBPACK_IMPORTED_MODULE_4__.contactInfo.address.zip\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_5__.staggerItem,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6\",\n                                        children: \"Services\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: _data_navigation__WEBPACK_IMPORTED_MODULE_4__.footerNavigation.services.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors text-sm\",\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, item.href, false, {\n                                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_5__.staggerItem,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6\",\n                                        children: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: _data_navigation__WEBPACK_IMPORTED_MODULE_4__.footerNavigation.company.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors text-sm\",\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, item.href, false, {\n                                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_5__.staggerItem,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-6\",\n                                        children: \"Resources\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3 mb-6\",\n                                        children: _data_navigation__WEBPACK_IMPORTED_MODULE_4__.footerNavigation.resources.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    className: \"text-gray-300 hover:text-white transition-colors text-sm\",\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, item.href, false, {\n                                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium mb-3\",\n                                                children: \"Follow Us\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: _data_navigation__WEBPACK_IMPORTED_MODULE_4__.socialLinks.slice(0, 4).map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: social.url,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-gray-400 hover:text-white transition-colors\",\n                                                        \"aria-label\": social.label,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocialIcon, {\n                                                            platform: social.platform\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, social.platform, false, {\n                                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_5__.fadeInUp,\n                    initial: \"initial\",\n                    whileInView: \"animate\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"border-t border-gray-800 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" Tera Works. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6\",\n                                children: _data_navigation__WEBPACK_IMPORTED_MODULE_4__.footerNavigation.legal.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"text-sm text-gray-400 hover:text-white transition-colors\",\n                                        children: item.label\n                                    }, item.href, false, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFMEI7QUFDRztBQUNFO0FBQ1E7QUFLRjtBQUMwQztBQUNHO0FBRWxGLE1BQU1hLFNBQW1CO0lBQ3ZCLE1BQU1DLGNBQWMsSUFBSUMsT0FBT0MsV0FBVztJQUUxQyxNQUFNQyxhQUFpRSxDQUFDLEVBQ3RFQyxRQUFRLEVBQ1JDLFlBQVksU0FBUyxFQUN0QjtRQUNDLE9BQVFELFNBQVNFLFdBQVc7WUFDMUIsS0FBSztnQkFDSCxxQkFDRSw4REFBQ0M7b0JBQUlGLFdBQVdBO29CQUFXRyxNQUFLO29CQUFlQyxTQUFROzhCQUNyRCw0RUFBQ0M7d0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7O1lBR2QsS0FBSztnQkFDSCxxQkFDRSw4REFBQ0o7b0JBQUlGLFdBQVdBO29CQUFXRyxNQUFLO29CQUFlQyxTQUFROzhCQUNyRCw0RUFBQ0M7d0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7O1lBR2QsS0FBSztnQkFDSCxxQkFDRSw4REFBQ0o7b0JBQUlGLFdBQVdBO29CQUFXRyxNQUFLO29CQUFlQyxTQUFROzhCQUNyRCw0RUFBQ0M7d0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7O1lBR2QsS0FBSztnQkFDSCxxQkFDRSw4REFBQ0o7b0JBQUlGLFdBQVdBO29CQUFXRyxNQUFLO29CQUFlQyxTQUFROzhCQUNyRCw0RUFBQ0M7d0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7O1lBR2QsS0FBSztnQkFDSCxxQkFDRSw4REFBQ0o7b0JBQUlGLFdBQVdBO29CQUFXRyxNQUFLO29CQUFlQyxTQUFROzhCQUNyRCw0RUFBQ0M7d0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7O1lBR2Q7Z0JBQ0UscUJBQU8sOERBQUNyQiwySEFBWUE7b0JBQUNlLFdBQVdBOzs7Ozs7UUFDcEM7SUFDRjtJQUVBLHFCQUNFLDhEQUFDTztRQUFPUCxXQUFVO2tCQUNoQiw0RUFBQ1E7WUFBSVIsV0FBVTs7OEJBQ2IsOERBQUNoQixpREFBTUEsQ0FBQ3dCLEdBQUc7b0JBQ1RDLFVBQVVqQixvRUFBZ0JBO29CQUMxQmtCLFNBQVE7b0JBQ1JDLGFBQVk7b0JBQ1pDLFVBQVU7d0JBQUVDLE1BQU07b0JBQUs7b0JBQ3ZCYixXQUFVOzhCQUVWLDRFQUFDUTt3QkFBSVIsV0FBVTs7MENBRWIsOERBQUNoQixpREFBTUEsQ0FBQ3dCLEdBQUc7Z0NBQUNDLFVBQVVoQiwrREFBV0E7Z0NBQUVPLFdBQVU7O2tEQUMzQyw4REFBQ2xCLGtEQUFJQTt3Q0FBQ2dDLE1BQUs7d0NBQUlkLFdBQVU7OzBEQUN2Qiw4REFBQ1E7Z0RBQUlSLFdBQVU7MERBQ2IsNEVBQUNqQixrREFBS0E7b0RBQ0pnQyxLQUFJO29EQUNKQyxLQUFJO29EQUNKYixJQUFJO29EQUNKSCxXQUFVOzs7Ozs7Ozs7OzswREFHZCw4REFBQ1E7Z0RBQUlSLFdBQVU7O2tFQUNiLDhEQUFDaUI7d0RBQUtqQixXQUFVO2tFQUFvQjs7Ozs7O2tFQUNwQyw4REFBQ2lCO3dEQUFLakIsV0FBVTtrRUFBOEI7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFNbEQsOERBQUNrQjt3Q0FBRWxCLFdBQVU7a0RBQTZDOzs7Ozs7a0RBSzFELDhEQUFDUTt3Q0FBSVIsV0FBVTs7MERBQ2IsOERBQUNRO2dEQUFJUixXQUFVOztrRUFDYiw4REFBQ2YsMkhBQVlBO3dEQUFDZSxXQUFVOzs7Ozs7a0VBQ3hCLDhEQUFDbUI7d0RBQ0NMLE1BQU0sQ0FBQyxPQUFPLEVBQUV4Qix5REFBV0EsQ0FBQzhCLEtBQUssRUFBRTt3REFDbkNwQixXQUFVO2tFQUVUVix5REFBV0EsQ0FBQzhCLEtBQUs7Ozs7Ozs7Ozs7OzswREFHdEIsOERBQUNaO2dEQUFJUixXQUFVOztrRUFDYiw4REFBQ2QsMkhBQVNBO3dEQUFDYyxXQUFVOzs7Ozs7a0VBQ3JCLDhEQUFDbUI7d0RBQ0NMLE1BQU0sQ0FBQyxJQUFJLEVBQUV4Qix5REFBV0EsQ0FBQytCLEtBQUssRUFBRTt3REFDaENyQixXQUFVO2tFQUVUVix5REFBV0EsQ0FBQytCLEtBQUs7Ozs7Ozs7Ozs7OzswREFHdEIsOERBQUNiO2dEQUFJUixXQUFVOztrRUFDYiw4REFBQ2IsMkhBQVVBO3dEQUFDYSxXQUFVOzs7Ozs7a0VBQ3RCLDhEQUFDUTt3REFBSVIsV0FBVTs7MEVBQ2IsOERBQUNROzBFQUFLbEIseURBQVdBLENBQUNnQyxPQUFPLENBQUNDLE1BQU07Ozs7OzswRUFDaEMsOERBQUNmOztvRUFDRWxCLHlEQUFXQSxDQUFDZ0MsT0FBTyxDQUFDRSxJQUFJO29FQUFDO29FQUFHbEMseURBQVdBLENBQUNnQyxPQUFPLENBQUNHLEtBQUs7b0VBQUM7b0VBQUVuQyx5REFBV0EsQ0FBQ2dDLE9BQU8sQ0FBQ0ksR0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FRMUYsOERBQUMxQyxpREFBTUEsQ0FBQ3dCLEdBQUc7Z0NBQUNDLFVBQVVoQiwrREFBV0E7O2tEQUMvQiw4REFBQ2tDO3dDQUFHM0IsV0FBVTtrREFBNkI7Ozs7OztrREFDM0MsOERBQUM0Qjt3Q0FBRzVCLFdBQVU7a0RBQ1haLDhEQUFnQkEsQ0FBQ3lDLFFBQVEsQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLHFCQUM5Qiw4REFBQ0M7MERBQ0MsNEVBQUNsRCxrREFBSUE7b0RBQ0hnQyxNQUFNaUIsS0FBS2pCLElBQUk7b0RBQ2ZkLFdBQVU7OERBRVQrQixLQUFLRSxLQUFLOzs7Ozs7K0NBTE5GLEtBQUtqQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OzBDQWF4Qiw4REFBQzlCLGlEQUFNQSxDQUFDd0IsR0FBRztnQ0FBQ0MsVUFBVWhCLCtEQUFXQTs7a0RBQy9CLDhEQUFDa0M7d0NBQUczQixXQUFVO2tEQUE2Qjs7Ozs7O2tEQUMzQyw4REFBQzRCO3dDQUFHNUIsV0FBVTtrREFDWFosOERBQWdCQSxDQUFDOEMsT0FBTyxDQUFDSixHQUFHLENBQUMsQ0FBQ0MscUJBQzdCLDhEQUFDQzswREFDQyw0RUFBQ2xELGtEQUFJQTtvREFDSGdDLE1BQU1pQixLQUFLakIsSUFBSTtvREFDZmQsV0FBVTs4REFFVCtCLEtBQUtFLEtBQUs7Ozs7OzsrQ0FMTkYsS0FBS2pCLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7MENBYXhCLDhEQUFDOUIsaURBQU1BLENBQUN3QixHQUFHO2dDQUFDQyxVQUFVaEIsK0RBQVdBOztrREFDL0IsOERBQUNrQzt3Q0FBRzNCLFdBQVU7a0RBQTZCOzs7Ozs7a0RBQzNDLDhEQUFDNEI7d0NBQUc1QixXQUFVO2tEQUNYWiw4REFBZ0JBLENBQUMrQyxTQUFTLENBQUNMLEdBQUcsQ0FBQyxDQUFDQyxxQkFDL0IsOERBQUNDOzBEQUNDLDRFQUFDbEQsa0RBQUlBO29EQUNIZ0MsTUFBTWlCLEtBQUtqQixJQUFJO29EQUNmZCxXQUFVOzhEQUVUK0IsS0FBS0UsS0FBSzs7Ozs7OytDQUxORixLQUFLakIsSUFBSTs7Ozs7Ozs7OztrREFZdEIsOERBQUNOOzswREFDQyw4REFBQzRCO2dEQUFHcEMsV0FBVTswREFBMkI7Ozs7OzswREFDekMsOERBQUNRO2dEQUFJUixXQUFVOzBEQUNaWCx5REFBV0EsQ0FBQ2dELEtBQUssQ0FBQyxHQUFHLEdBQUdQLEdBQUcsQ0FBQyxDQUFDUSx1QkFDNUIsOERBQUNuQjt3REFFQ0wsTUFBTXdCLE9BQU9DLEdBQUc7d0RBQ2hCQyxRQUFPO3dEQUNQQyxLQUFJO3dEQUNKekMsV0FBVTt3REFDVjBDLGNBQVlKLE9BQU9MLEtBQUs7a0VBRXhCLDRFQUFDbkM7NERBQVdDLFVBQVV1QyxPQUFPdkMsUUFBUTs7Ozs7O3VEQVBoQ3VDLE9BQU92QyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBaUJsQyw4REFBQ2YsaURBQU1BLENBQUN3QixHQUFHO29CQUNUQyxVQUFVbEIsNERBQVFBO29CQUNsQm1CLFNBQVE7b0JBQ1JDLGFBQVk7b0JBQ1pDLFVBQVU7d0JBQUVDLE1BQU07b0JBQUs7b0JBQ3ZCYixXQUFVOzhCQUVWLDRFQUFDUTt3QkFBSVIsV0FBVTs7MENBQ2IsOERBQUNRO2dDQUFJUixXQUFVOztvQ0FBd0I7b0NBQ2xDTDtvQ0FBWTs7Ozs7OzswQ0FHakIsOERBQUNhO2dDQUFJUixXQUFVOzBDQUNaWiw4REFBZ0JBLENBQUN1RCxLQUFLLENBQUNiLEdBQUcsQ0FBQyxDQUFDQyxxQkFDM0IsOERBQUNqRCxrREFBSUE7d0NBRUhnQyxNQUFNaUIsS0FBS2pCLElBQUk7d0NBQ2ZkLFdBQVU7a0RBRVQrQixLQUFLRSxLQUFLO3VDQUpORixLQUFLakIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFhaEM7QUFFQSxpRUFBZXBCLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIkU6XFxBdWdtZW50IENvZGUgVGVzdGluZ1xcdGVyYS13b3Jrcy1wb3J0Zm9saW9cXHNyY1xcY29tcG9uZW50c1xcbGF5b3V0XFxGb290ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSc7XG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJztcbmltcG9ydCB7IFxuICBFbnZlbG9wZUljb24sIFxuICBQaG9uZUljb24sIFxuICBNYXBQaW5JY29uIFxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuaW1wb3J0IHsgZm9vdGVyTmF2aWdhdGlvbiwgc29jaWFsTGlua3MsIGNvbnRhY3RJbmZvIH0gZnJvbSAnQC9kYXRhL25hdmlnYXRpb24nO1xuaW1wb3J0IHsgZmFkZUluVXAsIHN0YWdnZXJDb250YWluZXIsIHN0YWdnZXJJdGVtIH0gZnJvbSAnQC9saWIvYW5pbWF0aW9ucy9mcmFtZXInO1xuXG5jb25zdCBGb290ZXI6IFJlYWN0LkZDID0gKCkgPT4ge1xuICBjb25zdCBjdXJyZW50WWVhciA9IG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKTtcblxuICBjb25zdCBTb2NpYWxJY29uOiBSZWFjdC5GQzx7IHBsYXRmb3JtOiBzdHJpbmc7IGNsYXNzTmFtZT86IHN0cmluZyB9PiA9ICh7IFxuICAgIHBsYXRmb3JtLCBcbiAgICBjbGFzc05hbWUgPSBcInctNSBoLTVcIiBcbiAgfSkgPT4ge1xuICAgIHN3aXRjaCAocGxhdGZvcm0udG9Mb3dlckNhc2UoKSkge1xuICAgICAgY2FzZSAnbGlua2VkaW4nOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPXtjbGFzc05hbWV9IGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICA8cGF0aCBkPVwiTTIwLjQ0NyAyMC40NTJoLTMuNTU0di01LjU2OWMwLTEuMzI4LS4wMjctMy4wMzctMS44NTItMy4wMzctMS44NTMgMC0yLjEzNiAxLjQ0NS0yLjEzNiAyLjkzOXY1LjY2N0g5LjM1MVY5aDMuNDE0djEuNTYxaC4wNDZjLjQ3Ny0uOSAxLjYzNy0xLjg1IDMuMzctMS44NSAzLjYwMSAwIDQuMjY3IDIuMzcgNC4yNjcgNS40NTV2Ni4yODZ6TTUuMzM3IDcuNDMzYy0xLjE0NCAwLTIuMDYzLS45MjYtMi4wNjMtMi4wNjUgMC0xLjEzOC45Mi0yLjA2MyAyLjA2My0yLjA2MyAxLjE0IDAgMi4wNjQuOTI1IDIuMDY0IDIuMDYzIDAgMS4xMzktLjkyNSAyLjA2NS0yLjA2NCAyLjA2NXptMS43ODIgMTMuMDE5SDMuNTU1VjloMy41NjR2MTEuNDUyek0yMi4yMjUgMEgxLjc3MUMuNzkyIDAgMCAuNzc0IDAgMS43Mjl2MjAuNTQyQzAgMjMuMjI3Ljc5MiAyNCAxLjc3MSAyNGgyMC40NTFDMjMuMiAyNCAyNCAyMy4yMjcgMjQgMjIuMjcxVjEuNzI5QzI0IC43NzQgMjMuMiAwIDIyLjIyMiAwaC4wMDN6XCIvPlxuICAgICAgICAgIDwvc3ZnPlxuICAgICAgICApO1xuICAgICAgY2FzZSAndHdpdHRlcic6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPHN2ZyBjbGFzc05hbWU9e2NsYXNzTmFtZX0gZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgIDxwYXRoIGQ9XCJNMjMuOTUzIDQuNTdhMTAgMTAgMCAwMS0yLjgyNS43NzUgNC45NTggNC45NTggMCAwMDIuMTYzLTIuNzIzYy0uOTUxLjU1NS0yLjAwNS45NTktMy4xMjcgMS4xODRhNC45MiA0LjkyIDAgMDAtOC4zODQgNC40ODJDNy42OSA4LjA5NSA0LjA2NyA2LjEzIDEuNjQgMy4xNjJhNC44MjIgNC44MjIgMCAwMC0uNjY2IDIuNDc1YzAgMS43MS44NyAzLjIxMyAyLjE4OCA0LjA5NmE0LjkwNCA0LjkwNCAwIDAxLTIuMjI4LS42MTZ2LjA2YTQuOTIzIDQuOTIzIDAgMDAzLjk0NiA0LjgyNyA0Ljk5NiA0Ljk5NiAwIDAxLTIuMjEyLjA4NSA0LjkzNiA0LjkzNiAwIDAwNC42MDQgMy40MTcgOS44NjcgOS44NjcgMCAwMS02LjEwMiAyLjEwNWMtLjM5IDAtLjc3OS0uMDIzLTEuMTctLjA2N2ExMy45OTUgMTMuOTk1IDAgMDA3LjU1NyAyLjIwOWM5LjA1MyAwIDEzLjk5OC03LjQ5NiAxMy45OTgtMTMuOTg1IDAtLjIxIDAtLjQyLS4wMTUtLjYzQTkuOTM1IDkuOTM1IDAgMDAyNCA0LjU5elwiLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ2ZhY2Vib29rJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT17Y2xhc3NOYW1lfSBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgPHBhdGggZD1cIk0yNCAxMi4wNzNjMC02LjYyNy01LjM3My0xMi0xMi0xMnMtMTIgNS4zNzMtMTIgMTJjMCA1Ljk5IDQuMzg4IDEwLjk1NCAxMC4xMjUgMTEuODU0di04LjM4NUg3LjA3OHYtMy40N2gzLjA0N1Y5LjQzYzAtMy4wMDcgMS43OTItNC42NjkgNC41MzMtNC42NjkgMS4zMTIgMCAyLjY4Ni4yMzUgMi42ODYuMjM1djIuOTUzSDE1LjgzYy0xLjQ5MSAwLTEuOTU2LjkyNS0xLjk1NiAxLjg3NHYyLjI1aDMuMzI4bC0uNTMyIDMuNDdoLTIuNzk2djguMzg1QzE5LjYxMiAyMy4wMjcgMjQgMTguMDYyIDI0IDEyLjA3M3pcIi8+XG4gICAgICAgICAgPC9zdmc+XG4gICAgICAgICk7XG4gICAgICBjYXNlICdpbnN0YWdyYW0nOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPXtjbGFzc05hbWV9IGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICA8cGF0aCBkPVwiTTEyLjAxNyAwQzUuMzk2IDAgLjAyOSA1LjM2Ny4wMjkgMTEuOTg3YzAgNi42MiA1LjM2NyAxMS45ODcgMTEuOTg4IDExLjk4NyA2LjYyIDAgMTEuOTg3LTUuMzY3IDExLjk4Ny0xMS45ODdDMjQuMDE0IDUuMzY3IDE4LjYzNy4wMDEgMTIuMDE3LjAwMXpNOC40NDkgMTYuOTg4Yy0xLjI5NyAwLTIuNDQ4LS41OTYtMy4yMDUtMS41MzNsMS43MTQtMS43MTRjLjM5LjU4NiAxLjA3Ljk3NyAxLjg1Ljk3Ny45OCAwIDEuNzc2LS43OTYgMS43NzYtMS43NzYgMC0uOTgtLjc5Ni0xLjc3Ni0xLjc3Ni0xLjc3Ni0uNzggMC0xLjQ2LjM5MS0xLjg1Ljk3N0w0LjI0NCAxMC40M2MuNzU3LS45MzcgMS45MDgtMS41MzMgMy4yMDUtMS41MzMgMi4yNjkgMCA0LjEwNiAxLjgzNyA0LjEwNiA0LjEwNnMtMS44MzcgNC4xMDYtNC4xMDYgNC4xMDZ6bTcuNDQxIDBjLTIuMjY5IDAtNC4xMDYtMS44MzctNC4xMDYtNC4xMDZzMS44MzctNC4xMDYgNC4xMDYtNC4xMDZjMS4yOTcgMCAyLjQ0OC41OTYgMy4yMDUgMS41MzNsLTEuNzE0IDEuNzE0Yy0uMzktLjU4Ni0xLjA3LS45NzctMS44NS0uOTc3LS45OCAwLTEuNzc2Ljc5Ni0xLjc3NiAxLjc3NiAwIC45OC43OTYgMS43NzYgMS43NzYgMS43NzYuNzggMCAxLjQ2LS4zOTEgMS44NS0uOTc3bDEuNzE0IDEuNzE0Yy0uNzU3LjkzNy0xLjkwOCAxLjUzMy0zLjIwNSAxLjUzM3pcIi8+XG4gICAgICAgICAgPC9zdmc+XG4gICAgICAgICk7XG4gICAgICBjYXNlICdnaXRodWInOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPXtjbGFzc05hbWV9IGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICA8cGF0aCBkPVwiTTEyIDBjLTYuNjI2IDAtMTIgNS4zNzMtMTIgMTIgMCA1LjMwMiAzLjQzOCA5LjggOC4yMDcgMTEuMzg3LjU5OS4xMTEuNzkzLS4yNjEuNzkzLS41Nzd2LTIuMjM0Yy0zLjMzOC43MjYtNC4wMzMtMS40MTYtNC4wMzMtMS40MTYtLjU0Ni0xLjM4Ny0xLjMzMy0xLjc1Ni0xLjMzMy0xLjc1Ni0xLjA4OS0uNzQ1LjA4My0uNzI5LjA4My0uNzI5IDEuMjA1LjA4NCAxLjgzOSAxLjIzNyAxLjgzOSAxLjIzNyAxLjA3IDEuODM0IDIuODA3IDEuMzA0IDMuNDkyLjk5Ny4xMDctLjc3NS40MTgtMS4zMDUuNzYyLTEuNjA0LTIuNjY1LS4zMDUtNS40NjctMS4zMzQtNS40NjctNS45MzEgMC0xLjMxMS40NjktMi4zODEgMS4yMzYtMy4yMjEtLjEyNC0uMzAzLS41MzUtMS41MjQuMTE3LTMuMTc2IDAgMCAxLjAwOC0uMzIyIDMuMzAxIDEuMjMuOTU3LS4yNjYgMS45ODMtLjM5OSAzLjAwMy0uNDA0IDEuMDIuMDA1IDIuMDQ3LjEzOCAzLjAwNi40MDQgMi4yOTEtMS41NTIgMy4yOTctMS4yMyAzLjI5Ny0xLjIzLjY1MyAxLjY1My4yNDIgMi44NzQuMTE4IDMuMTc2Ljc3Ljg0IDEuMjM1IDEuOTExIDEuMjM1IDMuMjIxIDAgNC42MDktMi44MDcgNS42MjQtNS40NzkgNS45MjEuNDMuMzcyLjgyMyAxLjEwMi44MjMgMi4yMjJ2My4yOTNjMCAuMzE5LjE5Mi42OTQuODAxLjU3NiA0Ljc2NS0xLjU4OSA4LjE5OS02LjA4NiA4LjE5OS0xMS4zODYgMC02LjYyNy01LjM3My0xMi0xMi0xMnpcIi8+XG4gICAgICAgICAgPC9zdmc+XG4gICAgICAgICk7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gPEVudmVsb3BlSWNvbiBjbGFzc05hbWU9e2NsYXNzTmFtZX0gLz47XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMCB0ZXh0LXdoaXRlXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lci1jdXN0b21cIj5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICB2YXJpYW50cz17c3RhZ2dlckNvbnRhaW5lcn1cbiAgICAgICAgICBpbml0aWFsPVwiaW5pdGlhbFwiXG4gICAgICAgICAgd2hpbGVJblZpZXc9XCJhbmltYXRlXCJcbiAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwicHktMTYgbGc6cHktMjBcIlxuICAgICAgICA+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC04IGxnOmdhcC0xMlwiPlxuICAgICAgICAgICAgey8qIENvbXBhbnkgSW5mbyAqL31cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2IHZhcmlhbnRzPXtzdGFnZ2VySXRlbX0gY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMVwiPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtYi02XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTEwIGgtMTBcIj5cbiAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICBzcmM9XCIvbG9nby5wbmdcIlxuICAgICAgICAgICAgICAgICAgICBhbHQ9XCJUZXJhIFdvcmtzIExvZ29cIlxuICAgICAgICAgICAgICAgICAgICBmaWxsXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9iamVjdC1jb250YWluXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZFwiPlRlcmEgV29ya3M8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDAgLW10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgTGV0J3MgR3JvdyBUb2dldGhlclxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIG1iLTYgdGV4dC1zbSBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICBQcm9mZXNzaW9uYWwgd2ViIGRldmVsb3BtZW50IGFuZCBNZXRhIGFkdmVydGlzaW5nIHNlcnZpY2VzIHRvIGhlbHAgeW91ciBidXNpbmVzcyBncm93IGFuZCBzdWNjZWVkIG9ubGluZS5cbiAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgIHsvKiBDb250YWN0IEluZm8gKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgPEVudmVsb3BlSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtcHJpbWFyeS00MDAgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8YSBcbiAgICAgICAgICAgICAgICAgICAgaHJlZj17YG1haWx0bzoke2NvbnRhY3RJbmZvLmVtYWlsfWB9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtjb250YWN0SW5mby5lbWFpbH1cbiAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICA8UGhvbmVJY29uIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1wcmltYXJ5LTQwMCBmbGV4LXNocmluay0wXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxhIFxuICAgICAgICAgICAgICAgICAgICBocmVmPXtgdGVsOiR7Y29udGFjdEluZm8ucGhvbmV9YH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2NvbnRhY3RJbmZvLnBob25lfVxuICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTMgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgPE1hcFBpbkljb24gY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXByaW1hcnktNDAwIGZsZXgtc2hyaW5rLTAgbXQtMC41XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2Pntjb250YWN0SW5mby5hZGRyZXNzLnN0cmVldH08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICB7Y29udGFjdEluZm8uYWRkcmVzcy5jaXR5fSwge2NvbnRhY3RJbmZvLmFkZHJlc3Muc3RhdGV9IHtjb250YWN0SW5mby5hZGRyZXNzLnppcH1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICAgIHsvKiBTZXJ2aWNlcyAqL31cbiAgICAgICAgICAgIDxtb3Rpb24uZGl2IHZhcmlhbnRzPXtzdGFnZ2VySXRlbX0+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNlwiPlNlcnZpY2VzPC9oMz5cbiAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgIHtmb290ZXJOYXZpZ2F0aW9uLnNlcnZpY2VzLm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGxpIGtleT17aXRlbS5ocmVmfT5cbiAgICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICAgIHsvKiBDb21wYW55ICovfVxuICAgICAgICAgICAgPG1vdGlvbi5kaXYgdmFyaWFudHM9e3N0YWdnZXJJdGVtfT5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi02XCI+Q29tcGFueTwvaDM+XG4gICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICB7Zm9vdGVyTmF2aWdhdGlvbi5jb21wYW55Lm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGxpIGtleT17aXRlbS5ocmVmfT5cbiAgICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tY29sb3JzIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICAgIHsvKiBSZXNvdXJjZXMgKi99XG4gICAgICAgICAgICA8bW90aW9uLmRpdiB2YXJpYW50cz17c3RhZ2dlckl0ZW19PlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTZcIj5SZXNvdXJjZXM8L2gzPlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0zIG1iLTZcIj5cbiAgICAgICAgICAgICAgICB7Zm9vdGVyTmF2aWdhdGlvbi5yZXNvdXJjZXMubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8bGkga2V5PXtpdGVtLmhyZWZ9PlxuICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnMgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC91bD5cblxuICAgICAgICAgICAgICB7LyogU29jaWFsIExpbmtzICovfVxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTNcIj5Gb2xsb3cgVXM8L2g0PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICAgIHtzb2NpYWxMaW5rcy5zbGljZSgwLCA0KS5tYXAoKHNvY2lhbCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICAgIGtleT17c29jaWFsLnBsYXRmb3JtfVxuICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e3NvY2lhbC51cmx9XG4gICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0PVwiX2JsYW5rXCJcbiAgICAgICAgICAgICAgICAgICAgICByZWw9XCJub29wZW5lciBub3JlZmVycmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9e3NvY2lhbC5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxTb2NpYWxJY29uIHBsYXRmb3JtPXtzb2NpYWwucGxhdGZvcm19IC8+XG4gICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgIHsvKiBCb3R0b20gQmFyICovfVxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIHZhcmlhbnRzPXtmYWRlSW5VcH1cbiAgICAgICAgICBpbml0aWFsPVwiaW5pdGlhbFwiXG4gICAgICAgICAgd2hpbGVJblZpZXc9XCJhbmltYXRlXCJcbiAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyLXQgYm9yZGVyLWdyYXktODAwIHB5LTZcIlxuICAgICAgICA+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIG1kOmZsZXgtcm93IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgc3BhY2UteS00IG1kOnNwYWNlLXktMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgwqkge2N1cnJlbnRZZWFyfSBUZXJhIFdvcmtzLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTZcIj5cbiAgICAgICAgICAgICAge2Zvb3Rlck5hdmlnYXRpb24ubGVnYWwubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5ocmVmfVxuICAgICAgICAgICAgICAgICAgaHJlZj17aXRlbS5ocmVmfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtpdGVtLmxhYmVsfVxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9mb290ZXI+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBGb290ZXI7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJMaW5rIiwiSW1hZ2UiLCJtb3Rpb24iLCJFbnZlbG9wZUljb24iLCJQaG9uZUljb24iLCJNYXBQaW5JY29uIiwiZm9vdGVyTmF2aWdhdGlvbiIsInNvY2lhbExpbmtzIiwiY29udGFjdEluZm8iLCJmYWRlSW5VcCIsInN0YWdnZXJDb250YWluZXIiLCJzdGFnZ2VySXRlbSIsIkZvb3RlciIsImN1cnJlbnRZZWFyIiwiRGF0ZSIsImdldEZ1bGxZZWFyIiwiU29jaWFsSWNvbiIsInBsYXRmb3JtIiwiY2xhc3NOYW1lIiwidG9Mb3dlckNhc2UiLCJzdmciLCJmaWxsIiwidmlld0JveCIsInBhdGgiLCJkIiwiZm9vdGVyIiwiZGl2IiwidmFyaWFudHMiLCJpbml0aWFsIiwid2hpbGVJblZpZXciLCJ2aWV3cG9ydCIsIm9uY2UiLCJocmVmIiwic3JjIiwiYWx0Iiwic3BhbiIsInAiLCJhIiwiZW1haWwiLCJwaG9uZSIsImFkZHJlc3MiLCJzdHJlZXQiLCJjaXR5Iiwic3RhdGUiLCJ6aXAiLCJoMyIsInVsIiwic2VydmljZXMiLCJtYXAiLCJpdGVtIiwibGkiLCJsYWJlbCIsImNvbXBhbnkiLCJyZXNvdXJjZXMiLCJoNCIsInNsaWNlIiwic29jaWFsIiwidXJsIiwidGFyZ2V0IiwicmVsIiwiYXJpYS1sYWJlbCIsImxlZ2FsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _data_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/navigation */ \"(ssr)/./src/data/navigation.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui */ \"(ssr)/./src/components/ui/index.ts\");\n/* harmony import */ var _lib_animations_framer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/animations/framer */ \"(ssr)/./src/lib/animations/framer.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nconst Header = ()=>{\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const handleScroll = {\n                \"Header.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 10);\n                }\n            }[\"Header.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Header.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            setIsMobileMenuOpen(false);\n        }\n    }[\"Header.useEffect\"], [\n        pathname\n    ]);\n    const isActiveLink = (href)=>{\n        if (href === '/') {\n            return pathname === '/';\n        }\n        return pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('fixed top-0 left-0 right-0 z-50 transition-all duration-300', isScrolled ? 'bg-white/95 backdrop-blur-sm shadow-lg' : 'bg-transparent'),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-10 h-10 lg:w-12 lg:h-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/logo.png\",\n                                        alt: \"Tera Works Logo\",\n                                        fill: true,\n                                        className: \"object-contain\",\n                                        priority: true\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl lg:text-2xl font-bold text-gray-900\",\n                                            children: \"Tera Works\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs lg:text-sm text-gray-600 -mt-1\",\n                                            children: \"Let's Grow Together\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: _data_navigation__WEBPACK_IMPORTED_MODULE_6__.mainNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('text-sm font-medium transition-colors hover:text-primary-600', isActiveLink(item.href) ? 'text-primary-600' : isScrolled ? 'text-gray-900' : 'text-white'),\n                                    children: item.label\n                                }, item.href, false, {\n                                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                href: \"/contact\",\n                                variant: \"primary\",\n                                children: \"Get Started\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('lg:hidden p-2 rounded-md transition-colors', isScrolled ? 'text-gray-900 hover:bg-gray-100' : 'text-white hover:bg-white/10'),\n                            \"aria-label\": \"Toggle mobile menu\",\n                            children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                    children: isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_8__.mobileMenuVariants,\n                        initial: \"closed\",\n                        animate: \"open\",\n                        exit: \"closed\",\n                        className: \"lg:hidden bg-white border-t border-gray-200 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"py-4 space-y-2\",\n                            children: [\n                                _data_navigation__WEBPACK_IMPORTED_MODULE_6__.mainNavigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                        variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_8__.mobileMenuItemVariants,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('block px-4 py-2 text-base font-medium transition-colors hover:bg-gray-50', isActiveLink(item.href) ? 'text-primary-600 bg-primary-50' : 'text-gray-900'),\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, item.href, false, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 19\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_8__.mobileMenuItemVariants,\n                                    className: \"px-4 pt-4 border-t border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        href: \"/contact\",\n                                        variant: \"primary\",\n                                        className: \"w-full\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_animations_framer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/animations/framer */ \"(ssr)/./src/lib/animations/framer.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Button = ({ children, className, variant = 'primary', size = 'md', disabled = false, loading = false, onClick, type = 'button', href, external = false, ...props })=>{\n    const baseClasses = 'inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';\n    const variantClasses = {\n        primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500',\n        secondary: 'bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500',\n        outline: 'border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500',\n        ghost: 'text-primary-600 hover:bg-primary-50 focus:ring-primary-500'\n    };\n    const sizeClasses = {\n        sm: 'px-3 py-2 text-sm rounded-md',\n        md: 'px-4 py-2 text-base rounded-lg',\n        lg: 'px-6 py-3 text-lg rounded-lg'\n    };\n    const classes = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(baseClasses, variantClasses[variant], sizeClasses[size], className);\n    const LoadingSpinner = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"animate-spin -ml-1 mr-2 h-4 w-4\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    className: \"opacity-25\",\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"4\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    className: \"opacity-75\",\n                    fill: \"currentColor\",\n                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n            lineNumber: 46,\n            columnNumber: 5\n        }, undefined);\n    const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {}, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 70,\n                columnNumber: 19\n            }, undefined),\n            children\n        ]\n    }, void 0, true);\n    if (href) {\n        if (external) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.a, {\n                href: href,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: classes,\n                variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_4__.buttonVariants,\n                initial: \"idle\",\n                whileHover: \"hover\",\n                whileTap: \"tap\",\n                ...props,\n                children: content\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n            href: href,\n            className: classes,\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.span, {\n                variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_4__.buttonVariants,\n                initial: \"idle\",\n                whileHover: \"hover\",\n                whileTap: \"tap\",\n                className: \"flex items-center justify-center w-full h-full\",\n                children: content\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n        type: type,\n        className: classes,\n        disabled: disabled || loading,\n        onClick: onClick,\n        variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_4__.buttonVariants,\n        initial: \"idle\",\n        whileHover: disabled || loading ? \"idle\" : \"hover\",\n        whileTap: disabled || loading ? \"idle\" : \"tap\",\n        ...props,\n        children: content\n    }, void 0, false, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PortfolioCard: () => (/* binding */ PortfolioCard),\n/* harmony export */   ServiceCard: () => (/* binding */ ServiceCard),\n/* harmony export */   TestimonialCard: () => (/* binding */ TestimonialCard),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_animations_framer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/animations/framer */ \"(ssr)/./src/lib/animations/framer.ts\");\n/* __next_internal_client_entry_do_not_use__ ServiceCard,PortfolioCard,TestimonialCard,default auto */ \n\n\n\n\n\n\nconst Card = ({ children, className, title, description, image, href, hover = true, ...props })=>{\n    const baseClasses = 'bg-white rounded-lg shadow-md overflow-hidden transition-shadow duration-300';\n    const classes = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(baseClasses, className);\n    const cardContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n        className: classes,\n        variants: hover ? _lib_animations_framer__WEBPACK_IMPORTED_MODULE_5__.cardVariants : undefined,\n        initial: \"idle\",\n        whileHover: hover ? \"hover\" : \"idle\",\n        ...props,\n        children: [\n            image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-48 w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    src: image,\n                    alt: title || 'Card image',\n                    fill: true,\n                    className: \"object-cover\",\n                    sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900 mb-2\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, undefined),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, undefined),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n    if (href) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n            href: href,\n            className: \"block\",\n            children: cardContent\n        }, void 0, false, {\n            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, undefined);\n    }\n    return cardContent;\n};\n// Service Card Component\nconst ServiceCard = ({ title, description, icon, features, href, className })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        title: title,\n        description: description,\n        href: href,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('h-full', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-4xl mr-3\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 mb-4\",\n                children: description\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"space-y-2\",\n                children: features.slice(0, 4).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"flex items-center text-sm text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-green-500 mr-2 flex-shrink-0\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 20 20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, undefined),\n                            feature\n                        ]\n                    }, index, true, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, undefined);\n};\n// Portfolio Card Component\nconst PortfolioCard = ({ title, description, image, technologies, href, className })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        title: title,\n        description: description,\n        image: image,\n        href: href,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('h-full', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-wrap gap-2 mt-4\",\n            children: [\n                technologies.slice(0, 3).map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"px-2 py-1 bg-primary-100 text-primary-800 text-xs rounded-full\",\n                        children: tech\n                    }, index, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, undefined)),\n                technologies.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full\",\n                    children: [\n                        \"+\",\n                        technologies.length - 3,\n                        \" more\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, undefined);\n};\n// Testimonial Card Component\nconst TestimonialCard = ({ name, position, company, content, avatar, rating, className })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('h-full', className),\n        hover: false,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [\n                    ...Array(5)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('w-5 h-5', i < rating ? 'text-yellow-400' : 'text-gray-300'),\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, undefined)\n                    }, i, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                className: \"text-gray-600 mb-4 italic\",\n                children: [\n                    '\"',\n                    content,\n                    '\"'\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    avatar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-12 h-12 mr-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            src: avatar,\n                            alt: name,\n                            fill: true,\n                            className: \"rounded-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-semibold text-gray-900\",\n                                children: name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    position,\n                                    \" at \",\n                                    company\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Card);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   Textarea: () => (/* binding */ Textarea),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_animations_framer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/animations/framer */ \"(ssr)/./src/lib/animations/framer.ts\");\n/* __next_internal_client_entry_do_not_use__ Textarea,Select,default auto */ \n\n\n\n\nconst Input = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, label, error, helperText, variant = 'default', type = 'text', id, ...props }, ref)=>{\n    const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasValue, setHasValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;\n    const baseInputClasses = 'w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed';\n    const inputClasses = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseInputClasses, error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:border-primary-500', variant === 'floating' && 'pt-6 pb-2', className);\n    const handleFocus = (e)=>{\n        setIsFocused(true);\n        props.onFocus?.(e);\n    };\n    const handleBlur = (e)=>{\n        setIsFocused(false);\n        setHasValue(e.target.value !== '');\n        props.onBlur?.(e);\n    };\n    const handleChange = (e)=>{\n        setHasValue(e.target.value !== '');\n        props.onChange?.(e);\n    };\n    if (variant === 'floating') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative mb-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.input, {\n                    ref: ref,\n                    type: type,\n                    id: inputId,\n                    className: inputClasses,\n                    onFocus: handleFocus,\n                    onBlur: handleBlur,\n                    onChange: handleChange,\n                    variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                    whileFocus: \"focus\",\n                    ...props\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, undefined),\n                label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.label, {\n                    htmlFor: inputId,\n                    variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_3__.labelVariants,\n                    animate: isFocused || hasValue ? \"active\" : \"default\",\n                    className: \"absolute left-3 top-2 pointer-events-none text-sm font-medium\",\n                    children: label\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 13\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-1 text-sm text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 13\n                }, undefined),\n                helperText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: helperText\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n            lineNumber: 60,\n            columnNumber: 9\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                htmlFor: inputId,\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: label\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 96,\n                columnNumber: 11\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.input, {\n                ref: ref,\n                type: type,\n                id: inputId,\n                className: inputClasses,\n                onFocus: handleFocus,\n                onBlur: handleBlur,\n                onChange: handleChange,\n                variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                whileFocus: \"focus\",\n                ...props\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 116,\n                columnNumber: 11\n            }, undefined),\n            helperText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: helperText\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 119,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n        lineNumber: 94,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = 'Input';\nconst Textarea = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, label, error, helperText, id, ...props }, ref)=>{\n    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;\n    const baseClasses = 'w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed resize-vertical min-h-[100px]';\n    const classes = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:border-primary-500', className);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                htmlFor: textareaId,\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: label\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 152,\n                columnNumber: 11\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.textarea, {\n                ref: ref,\n                id: textareaId,\n                className: classes,\n                variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                whileFocus: \"focus\",\n                ...props\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 168,\n                columnNumber: 11\n            }, undefined),\n            helperText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: helperText\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 171,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n        lineNumber: 150,\n        columnNumber: 7\n    }, undefined);\n});\nTextarea.displayName = 'Textarea';\nconst Select = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, label, error, helperText, options, id, ...props }, ref)=>{\n    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;\n    const baseClasses = 'w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed bg-white';\n    const classes = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:border-primary-500', className);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                htmlFor: selectId,\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: label\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 205,\n                columnNumber: 11\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.select, {\n                ref: ref,\n                id: selectId,\n                className: classes,\n                variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_3__.formFieldVariants,\n                whileFocus: \"focus\",\n                ...props,\n                children: options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                        value: option.value,\n                        children: option.label\n                    }, option.value, false, {\n                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 227,\n                columnNumber: 11\n            }, undefined),\n            helperText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: helperText\n            }, void 0, false, {\n                fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 230,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n        lineNumber: 203,\n        columnNumber: 7\n    }, undefined);\n});\nSelect.displayName = 'Select';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Input);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Modal.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Modal.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_animations_framer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/animations/framer */ \"(ssr)/./src/lib/animations/framer.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Modal = ({ isOpen, onClose, children, title, size = 'md', closeOnOverlayClick = true, closeOnEscape = true, className })=>{\n    const sizeClasses = {\n        sm: 'max-w-md',\n        md: 'max-w-lg',\n        lg: 'max-w-2xl',\n        xl: 'max-w-4xl'\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Modal.useEffect\": ()=>{\n            const handleEscape = {\n                \"Modal.useEffect.handleEscape\": (event)=>{\n                    if (closeOnEscape && event.key === 'Escape') {\n                        onClose();\n                    }\n                }\n            }[\"Modal.useEffect.handleEscape\"];\n            if (isOpen) {\n                document.addEventListener('keydown', handleEscape);\n                document.body.style.overflow = 'hidden';\n            }\n            return ({\n                \"Modal.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"Modal.useEffect\"];\n        }\n    }[\"Modal.useEffect\"], [\n        isOpen,\n        closeOnEscape,\n        onClose\n    ]);\n    const handleOverlayClick = (event)=>{\n        if (closeOnOverlayClick && event.target === event.currentTarget) {\n            onClose();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_3__.modalBackdrop,\n                    initial: \"hidden\",\n                    animate: \"visible\",\n                    exit: \"hidden\",\n                    className: \"fixed inset-0 bg-black bg-opacity-50\",\n                    onClick: handleOverlayClick\n                }, void 0, false, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    variants: _lib_animations_framer__WEBPACK_IMPORTED_MODULE_3__.modalContent,\n                    initial: \"hidden\",\n                    animate: \"visible\",\n                    exit: \"exit\",\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('relative bg-white rounded-lg shadow-xl w-full', sizeClasses[size], className),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-full hover:bg-gray-100\",\n                                    \"aria-label\": \"Close modal\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n            lineNumber: 54,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Modal.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Modal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Modal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/index.ts":
/*!************************************!*\
  !*** ./src/components/ui/index.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* reexport safe */ _Button__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Card: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Input: () => (/* reexport safe */ _Input__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Modal: () => (/* reexport safe */ _Modal__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   PortfolioCard: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__.PortfolioCard),\n/* harmony export */   Select: () => (/* reexport safe */ _Input__WEBPACK_IMPORTED_MODULE_2__.Select),\n/* harmony export */   ServiceCard: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__.ServiceCard),\n/* harmony export */   TestimonialCard: () => (/* reexport safe */ _Card__WEBPACK_IMPORTED_MODULE_1__.TestimonialCard),\n/* harmony export */   Textarea: () => (/* reexport safe */ _Input__WEBPACK_IMPORTED_MODULE_2__.Textarea)\n/* harmony export */ });\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _Card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _Input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Input */ \"(ssr)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _Modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Modal */ \"(ssr)/./src/components/ui/Modal.tsx\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQTZDO0FBQ3lDO0FBQ3pCO0FBQ2xCIiwic291cmNlcyI6WyJFOlxcQXVnbWVudCBDb2RlIFRlc3RpbmdcXHRlcmEtd29ya3MtcG9ydGZvbGlvXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBkZWZhdWx0IGFzIEJ1dHRvbiB9IGZyb20gJy4vQnV0dG9uJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2FyZCwgU2VydmljZUNhcmQsIFBvcnRmb2xpb0NhcmQsIFRlc3RpbW9uaWFsQ2FyZCB9IGZyb20gJy4vQ2FyZCc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIElucHV0LCBUZXh0YXJlYSwgU2VsZWN0IH0gZnJvbSAnLi9JbnB1dCc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIE1vZGFsIH0gZnJvbSAnLi9Nb2RhbCc7XG4iXSwibmFtZXMiOlsiZGVmYXVsdCIsIkJ1dHRvbiIsIkNhcmQiLCJTZXJ2aWNlQ2FyZCIsIlBvcnRmb2xpb0NhcmQiLCJUZXN0aW1vbmlhbENhcmQiLCJJbnB1dCIsIlRleHRhcmVhIiwiU2VsZWN0IiwiTW9kYWwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/data/navigation.ts":
/*!********************************!*\
  !*** ./src/data/navigation.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contactInfo: () => (/* binding */ contactInfo),\n/* harmony export */   footerNavigation: () => (/* binding */ footerNavigation),\n/* harmony export */   mainNavigation: () => (/* binding */ mainNavigation),\n/* harmony export */   quickLinks: () => (/* binding */ quickLinks),\n/* harmony export */   socialLinks: () => (/* binding */ socialLinks)\n/* harmony export */ });\nconst mainNavigation = [\n    {\n        label: 'Home',\n        href: '/'\n    },\n    {\n        label: 'About',\n        href: '/about'\n    },\n    {\n        label: 'Services',\n        href: '/services',\n        children: [\n            {\n                label: 'Web Development',\n                href: '/services/web-development'\n            },\n            {\n                label: 'Meta Advertising',\n                href: '/services/meta-advertising'\n            },\n            {\n                label: 'Booking Systems',\n                href: '/services/booking-systems'\n            }\n        ]\n    },\n    {\n        label: 'Portfolio',\n        href: '/portfolio'\n    },\n    {\n        label: 'Blog',\n        href: '/blog'\n    },\n    {\n        label: 'Contact',\n        href: '/contact'\n    }\n];\nconst footerNavigation = {\n    services: [\n        {\n            label: 'Custom Website Development',\n            href: '/services/web-development'\n        },\n        {\n            label: 'E-commerce Solutions',\n            href: '/services/ecommerce'\n        },\n        {\n            label: 'Booking Systems',\n            href: '/services/booking-systems'\n        },\n        {\n            label: 'Meta Advertising',\n            href: '/services/meta-advertising'\n        },\n        {\n            label: 'Website Maintenance',\n            href: '/services/maintenance'\n        }\n    ],\n    company: [\n        {\n            label: 'About Us',\n            href: '/about'\n        },\n        {\n            label: 'Our Process',\n            href: '/process'\n        },\n        {\n            label: 'Case Studies',\n            href: '/portfolio'\n        },\n        {\n            label: 'Testimonials',\n            href: '/testimonials'\n        },\n        {\n            label: 'Careers',\n            href: '/careers'\n        }\n    ],\n    resources: [\n        {\n            label: 'Blog',\n            href: '/blog'\n        },\n        {\n            label: 'FAQ',\n            href: '/faq'\n        },\n        {\n            label: 'Support',\n            href: '/support'\n        },\n        {\n            label: 'Documentation',\n            href: '/docs'\n        },\n        {\n            label: 'Contact',\n            href: '/contact'\n        }\n    ],\n    legal: [\n        {\n            label: 'Privacy Policy',\n            href: '/privacy'\n        },\n        {\n            label: 'Terms of Service',\n            href: '/terms'\n        },\n        {\n            label: 'Cookie Policy',\n            href: '/cookies'\n        },\n        {\n            label: 'GDPR',\n            href: '/gdpr'\n        }\n    ]\n};\nconst socialLinks = [\n    {\n        platform: 'LinkedIn',\n        url: 'https://linkedin.com/company/tera-works',\n        icon: 'linkedin',\n        label: 'Follow us on LinkedIn'\n    },\n    {\n        platform: 'Twitter',\n        url: 'https://twitter.com/teraworks',\n        icon: 'twitter',\n        label: 'Follow us on Twitter'\n    },\n    {\n        platform: 'Facebook',\n        url: 'https://facebook.com/teraworks',\n        icon: 'facebook',\n        label: 'Like us on Facebook'\n    },\n    {\n        platform: 'Instagram',\n        url: 'https://instagram.com/teraworks',\n        icon: 'instagram',\n        label: 'Follow us on Instagram'\n    },\n    {\n        platform: 'GitHub',\n        url: 'https://github.com/teraworks',\n        icon: 'github',\n        label: 'View our code on GitHub'\n    },\n    {\n        platform: 'Email',\n        url: 'mailto:<EMAIL>',\n        icon: 'email',\n        label: 'Send us an email'\n    }\n];\nconst contactInfo = {\n    email: '<EMAIL>',\n    phone: '+****************',\n    address: {\n        street: '123 Business Ave',\n        city: 'Tech City',\n        state: 'TC',\n        zip: '12345',\n        country: 'United States'\n    },\n    hours: {\n        weekdays: '9:00 AM - 6:00 PM',\n        weekends: '10:00 AM - 4:00 PM',\n        timezone: 'EST'\n    }\n};\nconst quickLinks = [\n    {\n        label: 'Get Started',\n        href: '/contact',\n        description: 'Start your project today'\n    },\n    {\n        label: 'View Portfolio',\n        href: '/portfolio',\n        description: 'See our latest work'\n    },\n    {\n        label: 'Read Blog',\n        href: '/blog',\n        description: 'Latest insights and tips'\n    },\n    {\n        label: 'Book Consultation',\n        href: '/consultation',\n        description: 'Free 30-minute consultation'\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/data/navigation.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/animations/framer.ts":
/*!**************************************!*\
  !*** ./src/lib/animations/framer.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants),\n/* harmony export */   cardVariants: () => (/* binding */ cardVariants),\n/* harmony export */   createFadeVariants: () => (/* binding */ createFadeVariants),\n/* harmony export */   createStaggerVariants: () => (/* binding */ createStaggerVariants),\n/* harmony export */   fadeInLeft: () => (/* binding */ fadeInLeft),\n/* harmony export */   fadeInRight: () => (/* binding */ fadeInRight),\n/* harmony export */   fadeInUp: () => (/* binding */ fadeInUp),\n/* harmony export */   formFieldVariants: () => (/* binding */ formFieldVariants),\n/* harmony export */   labelVariants: () => (/* binding */ labelVariants),\n/* harmony export */   loadingSpinner: () => (/* binding */ loadingSpinner),\n/* harmony export */   mobileMenuItemVariants: () => (/* binding */ mobileMenuItemVariants),\n/* harmony export */   mobileMenuVariants: () => (/* binding */ mobileMenuVariants),\n/* harmony export */   modalBackdrop: () => (/* binding */ modalBackdrop),\n/* harmony export */   modalContent: () => (/* binding */ modalContent),\n/* harmony export */   pageTransition: () => (/* binding */ pageTransition),\n/* harmony export */   pageVariants: () => (/* binding */ pageVariants),\n/* harmony export */   portfolioGridVariants: () => (/* binding */ portfolioGridVariants),\n/* harmony export */   portfolioItemVariants: () => (/* binding */ portfolioItemVariants),\n/* harmony export */   scaleIn: () => (/* binding */ scaleIn),\n/* harmony export */   staggerContainer: () => (/* binding */ staggerContainer),\n/* harmony export */   staggerItem: () => (/* binding */ staggerItem)\n/* harmony export */ });\n// Common animation variants\nconst fadeInUp = {\n    initial: {\n        opacity: 0,\n        y: 60\n    },\n    animate: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6,\n            ease: \"easeOut\"\n        }\n    },\n    exit: {\n        opacity: 0,\n        y: -60\n    }\n};\nconst fadeInLeft = {\n    initial: {\n        opacity: 0,\n        x: -60\n    },\n    animate: {\n        opacity: 1,\n        x: 0,\n        transition: {\n            duration: 0.6,\n            ease: \"easeOut\"\n        }\n    },\n    exit: {\n        opacity: 0,\n        x: 60\n    }\n};\nconst fadeInRight = {\n    initial: {\n        opacity: 0,\n        x: 60\n    },\n    animate: {\n        opacity: 1,\n        x: 0,\n        transition: {\n            duration: 0.6,\n            ease: \"easeOut\"\n        }\n    },\n    exit: {\n        opacity: 0,\n        x: -60\n    }\n};\nconst scaleIn = {\n    initial: {\n        opacity: 0,\n        scale: 0.8\n    },\n    animate: {\n        opacity: 1,\n        scale: 1,\n        transition: {\n            duration: 0.5,\n            ease: \"easeOut\"\n        }\n    },\n    exit: {\n        opacity: 0,\n        scale: 0.8\n    }\n};\nconst staggerContainer = {\n    initial: {},\n    animate: {\n        transition: {\n            staggerChildren: 0.1,\n            delayChildren: 0.3\n        }\n    }\n};\nconst staggerItem = {\n    initial: {\n        opacity: 0,\n        y: 20\n    },\n    animate: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.5,\n            ease: \"easeOut\"\n        }\n    }\n};\n// Button animations\nconst buttonVariants = {\n    idle: {\n        scale: 1\n    },\n    hover: {\n        scale: 1.05,\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        }\n    },\n    tap: {\n        scale: 0.95,\n        transition: {\n            duration: 0.1\n        }\n    }\n};\n// Card animations\nconst cardVariants = {\n    idle: {\n        y: 0,\n        scale: 1,\n        boxShadow: \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\"\n    },\n    hover: {\n        y: -8,\n        scale: 1.02,\n        boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1)\",\n        transition: {\n            duration: 0.3,\n            ease: \"easeOut\"\n        }\n    }\n};\n// Modal animations\nconst modalBackdrop = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1\n    }\n};\nconst modalContent = {\n    hidden: {\n        opacity: 0,\n        scale: 0.8,\n        y: 50\n    },\n    visible: {\n        opacity: 1,\n        scale: 1,\n        y: 0,\n        transition: {\n            type: \"spring\",\n            damping: 25,\n            stiffness: 300\n        }\n    },\n    exit: {\n        opacity: 0,\n        scale: 0.8,\n        y: 50,\n        transition: {\n            duration: 0.2\n        }\n    }\n};\n// Navigation animations\nconst mobileMenuVariants = {\n    closed: {\n        opacity: 0,\n        height: 0,\n        transition: {\n            duration: 0.3,\n            ease: \"easeInOut\"\n        }\n    },\n    open: {\n        opacity: 1,\n        height: \"auto\",\n        transition: {\n            duration: 0.3,\n            ease: \"easeInOut\",\n            staggerChildren: 0.1,\n            delayChildren: 0.1\n        }\n    }\n};\nconst mobileMenuItemVariants = {\n    closed: {\n        opacity: 0,\n        x: -20\n    },\n    open: {\n        opacity: 1,\n        x: 0,\n        transition: {\n            duration: 0.3,\n            ease: \"easeOut\"\n        }\n    }\n};\n// Form animations\nconst formFieldVariants = {\n    focus: {\n        scale: 1.02,\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        }\n    },\n    blur: {\n        scale: 1,\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        }\n    }\n};\nconst labelVariants = {\n    default: {\n        y: 0,\n        scale: 1,\n        color: \"#6B7280\"\n    },\n    active: {\n        y: -24,\n        scale: 0.8,\n        color: \"#3B82F6\",\n        transition: {\n            duration: 0.2,\n            ease: \"easeOut\"\n        }\n    }\n};\n// Portfolio grid animations\nconst portfolioGridVariants = {\n    hidden: {\n        opacity: 0\n    },\n    show: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.1\n        }\n    }\n};\nconst portfolioItemVariants = {\n    hidden: {\n        opacity: 0,\n        scale: 0.8\n    },\n    show: {\n        opacity: 1,\n        scale: 1,\n        transition: {\n            duration: 0.5,\n            ease: \"easeOut\"\n        }\n    }\n};\n// Page transition animations\nconst pageVariants = {\n    initial: {\n        opacity: 0,\n        y: 20\n    },\n    in: {\n        opacity: 1,\n        y: 0\n    },\n    out: {\n        opacity: 0,\n        y: -20\n    }\n};\nconst pageTransition = {\n    type: \"tween\",\n    ease: \"anticipate\",\n    duration: 0.5\n};\n// Loading animations\nconst loadingSpinner = {\n    animate: {\n        rotate: 360,\n        transition: {\n            duration: 1,\n            repeat: Infinity,\n            ease: \"linear\"\n        }\n    }\n};\n// Utility functions for animations\nconst createStaggerVariants = (staggerDelay = 0.1, childDelay = 0.3)=>({\n        initial: {},\n        animate: {\n            transition: {\n                staggerChildren: staggerDelay,\n                delayChildren: childDelay\n            }\n        }\n    });\nconst createFadeVariants = (direction = 'up', distance = 60)=>{\n    const getInitialPosition = ()=>{\n        switch(direction){\n            case 'up':\n                return {\n                    y: distance\n                };\n            case 'down':\n                return {\n                    y: -distance\n                };\n            case 'left':\n                return {\n                    x: -distance\n                };\n            case 'right':\n                return {\n                    x: distance\n                };\n            default:\n                return {\n                    y: distance\n                };\n        }\n    };\n    return {\n        initial: {\n            opacity: 0,\n            ...getInitialPosition()\n        },\n        animate: {\n            opacity: 1,\n            x: 0,\n            y: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\"\n            }\n        },\n        exit: {\n            opacity: 0,\n            ...getInitialPosition()\n        }\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/animations/framer.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateReadingTime: () => (/* binding */ calculateReadingTime),\n/* harmony export */   capitalize: () => (/* binding */ capitalize),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   device: () => (/* binding */ device),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPhoneNumber: () => (/* binding */ formatPhoneNumber),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getContrastColor: () => (/* binding */ getContrastColor),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   isInViewport: () => (/* binding */ isInViewport),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   scrollToElement: () => (/* binding */ scrollToElement),\n/* harmony export */   storage: () => (/* binding */ storage),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   url: () => (/* binding */ url)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n\nfunction cn(...inputs) {\n    return (0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs);\n}\n// Format date utility\nfunction formatDate(date) {\n    const d = new Date(date);\n    return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n}\n// Debounce utility\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n// Throttle utility\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n// Scroll to element utility\nfunction scrollToElement(elementId, offset = 0) {\n    const element = document.getElementById(elementId);\n    if (element) {\n        const elementPosition = element.getBoundingClientRect().top;\n        const offsetPosition = elementPosition + window.pageYOffset - offset;\n        window.scrollTo({\n            top: offsetPosition,\n            behavior: 'smooth'\n        });\n    }\n}\n// Check if element is in viewport\nfunction isInViewport(element) {\n    const rect = element.getBoundingClientRect();\n    return rect.top >= 0 && rect.left >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && rect.right <= (window.innerWidth || document.documentElement.clientWidth);\n}\n// Generate random ID\nfunction generateId(length = 8) {\n    return Math.random().toString(36).substring(2, length + 2);\n}\n// Validate email\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n// Format phone number\nfunction formatPhoneNumber(phoneNumber) {\n    const cleaned = phoneNumber.replace(/\\D/g, '');\n    const match = cleaned.match(/^(\\d{3})(\\d{3})(\\d{4})$/);\n    if (match) {\n        return `(${match[1]}) ${match[2]}-${match[3]}`;\n    }\n    return phoneNumber;\n}\n// Capitalize first letter\nfunction capitalize(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\n// Truncate text\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength).trim() + '...';\n}\n// Get initials from name\nfunction getInitials(name) {\n    return name.split(' ').map((word)=>word.charAt(0).toUpperCase()).join('').substring(0, 2);\n}\n// Format currency\nfunction formatCurrency(amount, currency = 'USD') {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: currency\n    }).format(amount);\n}\n// Format number with commas\nfunction formatNumber(num) {\n    return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\n}\n// Calculate reading time\nfunction calculateReadingTime(text) {\n    const wordsPerMinute = 200;\n    const words = text.trim().split(/\\s+/).length;\n    return Math.ceil(words / wordsPerMinute);\n}\n// Get contrast color (black or white) based on background\nfunction getContrastColor(hexColor) {\n    const r = parseInt(hexColor.slice(1, 3), 16);\n    const g = parseInt(hexColor.slice(3, 5), 16);\n    const b = parseInt(hexColor.slice(5, 7), 16);\n    const brightness = (r * 299 + g * 587 + b * 114) / 1000;\n    return brightness > 128 ? '#000000' : '#ffffff';\n}\n// Deep clone object\nfunction deepClone(obj) {\n    if (obj === null || typeof obj !== 'object') return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === 'object') {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n// Local storage utilities\nconst storage = {\n    get: (key, defaultValue)=>{\n        if (true) return defaultValue || null;\n        try {\n            const item = window.localStorage.getItem(key);\n            return item ? JSON.parse(item) : defaultValue || null;\n        } catch (error) {\n            console.error(`Error getting localStorage key \"${key}\":`, error);\n            return defaultValue || null;\n        }\n    },\n    set: (key, value)=>{\n        if (true) return;\n        try {\n            window.localStorage.setItem(key, JSON.stringify(value));\n        } catch (error) {\n            console.error(`Error setting localStorage key \"${key}\":`, error);\n        }\n    },\n    remove: (key)=>{\n        if (true) return;\n        try {\n            window.localStorage.removeItem(key);\n        } catch (error) {\n            console.error(`Error removing localStorage key \"${key}\":`, error);\n        }\n    },\n    clear: ()=>{\n        if (true) return;\n        try {\n            window.localStorage.clear();\n        } catch (error) {\n            console.error('Error clearing localStorage:', error);\n        }\n    }\n};\n// URL utilities\nconst url = {\n    getQueryParam: (param)=>{\n        if (true) return null;\n        const urlParams = new URLSearchParams(window.location.search);\n        return urlParams.get(param);\n    },\n    setQueryParam: (param, value)=>{\n        if (true) return;\n        const url = new URL(window.location.href);\n        url.searchParams.set(param, value);\n        window.history.pushState({}, '', url.toString());\n    },\n    removeQueryParam: (param)=>{\n        if (true) return;\n        const url = new URL(window.location.href);\n        url.searchParams.delete(param);\n        window.history.pushState({}, '', url.toString());\n    }\n};\n// Device detection\nconst device = {\n    isMobile: ()=>{\n        if (true) return false;\n        return window.innerWidth < 768;\n    },\n    isTablet: ()=>{\n        if (true) return false;\n        return window.innerWidth >= 768 && window.innerWidth < 1024;\n    },\n    isDesktop: ()=>{\n        if (true) return false;\n        return window.innerWidth >= 1024;\n    },\n    isTouchDevice: ()=>{\n        if (true) return false;\n        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/@heroicons","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();