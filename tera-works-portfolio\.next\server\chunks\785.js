exports.id=785,exports.ids=[785],exports.modules={459:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},1135:()=>{},2408:(e,s,t)=>{Promise.resolve().then(t.bind(t,2830)),Promise.resolve().then(t.bind(t,5736))},2830:(e,s,t)=>{"use strict";t.d(s,{default:()=>x});var r=t(687);t(3210);var a=t(5814),l=t.n(a),i=t(474),o=t(6001),n=t(4859),c=t(137),d=t(6942),m=t(5017),h=t(4020);let x=()=>{let e=new Date().getFullYear(),s=({platform:e,className:s="w-5 h-5"})=>{switch(e.toLowerCase()){case"linkedin":return(0,r.jsx)("svg",{className:s,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})});case"twitter":return(0,r.jsx)("svg",{className:s,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})});case"facebook":return(0,r.jsx)("svg",{className:s,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})});case"instagram":return(0,r.jsx)("svg",{className:s,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.596-3.205-1.533l1.714-1.714c.39.586 1.07.977 1.85.977.98 0 1.776-.796 1.776-1.776 0-.98-.796-1.776-1.776-1.776-.78 0-1.46.391-1.85.977L4.244 10.43c.757-.937 1.908-1.533 3.205-1.533 2.269 0 4.106 1.837 4.106 4.106s-1.837 4.106-4.106 4.106zm7.441 0c-2.269 0-4.106-1.837-4.106-4.106s1.837-4.106 4.106-4.106c1.297 0 2.448.596 3.205 1.533l-1.714 1.714c-.39-.586-1.07-.977-1.85-.977-.98 0-1.776.796-1.776 1.776 0 .98.796 1.776 1.776 1.776.78 0 1.46-.391 1.85-.977l1.714 1.714c-.757.937-1.908 1.533-3.205 1.533z"})});case"github":return(0,r.jsx)("svg",{className:s,fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})});default:return(0,r.jsx)(n.A,{className:s})}};return(0,r.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,r.jsxs)("div",{className:"container-custom",children:[(0,r.jsx)(o.P.div,{variants:h.bK,initial:"initial",whileInView:"animate",viewport:{once:!0},className:"py-16 lg:py-20",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12",children:[(0,r.jsxs)(o.P.div,{variants:h.Rf,className:"lg:col-span-1",children:[(0,r.jsxs)(l(),{href:"/",className:"flex items-center space-x-2 mb-6",children:[(0,r.jsx)("div",{className:"relative w-10 h-10",children:(0,r.jsx)(i.default,{src:"/logo.png",alt:"Tera Works Logo",fill:!0,className:"object-contain"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-xl font-bold",children:"Tera Works"}),(0,r.jsx)("span",{className:"text-sm text-gray-400 -mt-1",children:"Let's Grow Together"})]})]}),(0,r.jsx)("p",{className:"text-gray-300 mb-6 text-sm leading-relaxed",children:"Professional web development and Meta advertising services to help your business grow and succeed online."}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 text-primary-400 flex-shrink-0"}),(0,r.jsx)("a",{href:`mailto:${m.r_.email}`,className:"text-gray-300 hover:text-white transition-colors",children:m.r_.email})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 text-primary-400 flex-shrink-0"}),(0,r.jsx)("a",{href:`tel:${m.r_.phone}`,className:"text-gray-300 hover:text-white transition-colors",children:m.r_.phone})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3 text-sm",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 text-primary-400 flex-shrink-0 mt-0.5"}),(0,r.jsxs)("div",{className:"text-gray-300",children:[(0,r.jsx)("div",{children:m.r_.address.street}),(0,r.jsxs)("div",{children:[m.r_.address.city,", ",m.r_.address.state," ",m.r_.address.zip]})]})]})]})]}),(0,r.jsxs)(o.P.div,{variants:h.Rf,children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Services"}),(0,r.jsx)("ul",{className:"space-y-3",children:m.ii.services.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:e.href,className:"text-gray-300 hover:text-white transition-colors text-sm",children:e.label})},e.href))})]}),(0,r.jsxs)(o.P.div,{variants:h.Rf,children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Company"}),(0,r.jsx)("ul",{className:"space-y-3",children:m.ii.company.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:e.href,className:"text-gray-300 hover:text-white transition-colors text-sm",children:e.label})},e.href))})]}),(0,r.jsxs)(o.P.div,{variants:h.Rf,children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Resources"}),(0,r.jsx)("ul",{className:"space-y-3 mb-6",children:m.ii.resources.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:e.href,className:"text-gray-300 hover:text-white transition-colors text-sm",children:e.label})},e.href))}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Follow Us"}),(0,r.jsx)("div",{className:"flex space-x-4",children:m.lj.slice(0,4).map(e=>(0,r.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white transition-colors","aria-label":e.label,children:(0,r.jsx)(s,{platform:e.platform})},e.platform))})]})]})]})}),(0,r.jsx)(o.P.div,{variants:h.tE,initial:"initial",whileInView:"animate",viewport:{once:!0},className:"border-t border-gray-800 py-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-400",children:["\xa9 ",e," Tera Works. All rights reserved."]}),(0,r.jsx)("div",{className:"flex space-x-6",children:m.ii.legal.map(e=>(0,r.jsx)(l(),{href:e.href,className:"text-sm text-gray-400 hover:text-white transition-colors",children:e.label},e.href))})]})})]})})}},4020:(e,s,t)=>{"use strict";t.d(s,{CV:()=>x,Rf:()=>l,Tz:()=>c,VM:()=>h,Xy:()=>d,Ym:()=>n,bK:()=>a,fP:()=>o,pP:()=>m,ru:()=>i,tE:()=>r});let r={initial:{opacity:0,y:60},animate:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}},exit:{opacity:0,y:-60}},a={initial:{},animate:{transition:{staggerChildren:.1,delayChildren:.3}}},l={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.5,ease:"easeOut"}}},i={idle:{scale:1},hover:{scale:1.05,transition:{duration:.2,ease:"easeOut"}},tap:{scale:.95,transition:{duration:.1}}},o={idle:{y:0,scale:1,boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"},hover:{y:-8,scale:1.02,boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)",transition:{duration:.3,ease:"easeOut"}}},n={closed:{opacity:0,height:0,transition:{duration:.3,ease:"easeInOut"}},open:{opacity:1,height:"auto",transition:{duration:.3,ease:"easeInOut",staggerChildren:.1,delayChildren:.1}}},c={closed:{opacity:0,x:-20},open:{opacity:1,x:0,transition:{duration:.3,ease:"easeOut"}}},d={focus:{scale:1.02,transition:{duration:.2,ease:"easeOut"}},blur:{scale:1,transition:{duration:.2,ease:"easeOut"}}},m={default:{y:0,scale:1,color:"#6B7280"},active:{y:-24,scale:.8,color:"#3B82F6",transition:{duration:.2,ease:"easeOut"}}},h={hidden:{opacity:0},show:{opacity:1,transition:{staggerChildren:.1}}},x={hidden:{opacity:0,scale:.8},show:{opacity:1,scale:1,transition:{duration:.5,ease:"easeOut"}}}},4431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m,metadata:()=>d});var r=t(7413),a=t(1194),l=t.n(a),i=t(1169),o=t.n(i);t(1135);var n=t(8926),c=t(4712);let d={title:{default:"Tera Works - Professional Web Development & Meta Advertising",template:"%s | Tera Works"},description:"Professional website development and Meta advertising services. Let's grow together with custom web solutions and targeted social media campaigns.",keywords:["web development","meta advertising","website design","social media marketing","facebook ads","instagram ads","booking systems"],authors:[{name:"Tera Works"}],creator:"Tera Works",openGraph:{type:"website",locale:"en_US",url:"https://teraworks.com",siteName:"Tera Works",title:"Tera Works - Professional Web Development & Meta Advertising",description:"Professional website development and Meta advertising services. Let's grow together.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Tera Works - Let's Grow Together"}]},twitter:{card:"summary_large_image",title:"Tera Works - Professional Web Development & Meta Advertising",description:"Professional website development and Meta advertising services.",images:["/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function m({children:e}){return(0,r.jsx)("html",{lang:"en",className:`${l().variable} ${o().variable}`,children:(0,r.jsxs)("body",{className:"font-sans antialiased",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("main",{className:"min-h-screen",children:e}),(0,r.jsx)(c.default,{})]})})}},4712:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Footer.tsx","default")},4780:(e,s,t)=>{"use strict";t.d(s,{cn:()=>a});var r=t(9384);function a(...e){return(0,r.$)(e)}},4803:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},5017:(e,s,t)=>{"use strict";t.d(s,{ii:()=>a,lj:()=>l,r_:()=>i,ss:()=>r});let r=[{label:"Home",href:"/"},{label:"About",href:"/about"},{label:"Services",href:"/services",children:[{label:"Web Development",href:"/services/web-development"},{label:"Meta Advertising",href:"/services/meta-advertising"},{label:"Booking Systems",href:"/services/booking-systems"}]},{label:"Portfolio",href:"/portfolio"},{label:"Blog",href:"/blog"},{label:"Contact",href:"/contact"}],a={services:[{label:"Custom Website Development",href:"/services/web-development"},{label:"E-commerce Solutions",href:"/services/ecommerce"},{label:"Booking Systems",href:"/services/booking-systems"},{label:"Meta Advertising",href:"/services/meta-advertising"},{label:"Website Maintenance",href:"/services/maintenance"}],company:[{label:"About Us",href:"/about"},{label:"Our Process",href:"/process"},{label:"Case Studies",href:"/portfolio"},{label:"Testimonials",href:"/testimonials"},{label:"Careers",href:"/careers"}],resources:[{label:"Blog",href:"/blog"},{label:"FAQ",href:"/faq"},{label:"Support",href:"/support"},{label:"Documentation",href:"/docs"},{label:"Contact",href:"/contact"}],legal:[{label:"Privacy Policy",href:"/privacy"},{label:"Terms of Service",href:"/terms"},{label:"Cookie Policy",href:"/cookies"},{label:"GDPR",href:"/gdpr"}]},l=[{platform:"LinkedIn",url:"https://linkedin.com/company/tera-works",icon:"linkedin",label:"Follow us on LinkedIn"},{platform:"Twitter",url:"https://twitter.com/teraworks",icon:"twitter",label:"Follow us on Twitter"},{platform:"Facebook",url:"https://facebook.com/teraworks",icon:"facebook",label:"Like us on Facebook"},{platform:"Instagram",url:"https://instagram.com/teraworks",icon:"instagram",label:"Follow us on Instagram"},{platform:"GitHub",url:"https://github.com/teraworks",icon:"github",label:"View our code on GitHub"},{platform:"Email",url:"mailto:<EMAIL>",icon:"email",label:"Send us an email"}],i={email:"<EMAIL>",phone:"+****************",address:{street:"123 Business Ave",city:"Tech City",state:"TC",zip:"12345",country:"United States"},hours:{weekdays:"9:00 AM - 6:00 PM",weekends:"10:00 AM - 4:00 PM",timezone:"EST"}}},5560:(e,s,t)=>{Promise.resolve().then(t.bind(t,4712)),Promise.resolve().then(t.bind(t,8926))},5736:(e,s,t)=>{"use strict";t.d(s,{default:()=>v});var r=t(687),a=t(3210),l=t(5814),i=t.n(l),o=t(474),n=t(6189),c=t(8920),d=t(6001),m=t(1836),h=t(6510),x=t(4780),p=t(5017),f=t(8290),u=t(4020);let v=()=>{let[e,s]=(0,a.useState)(!1),[t,l]=(0,a.useState)(!1),v=(0,n.usePathname)();(0,a.useEffect)(()=>{let e=()=>{s(window.scrollY>10)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,a.useEffect)(()=>{l(!1)},[v]);let b=e=>"/"===e?"/"===v:v.startsWith(e);return(0,r.jsx)("header",{className:(0,x.cn)("fixed top-0 left-0 right-0 z-50 transition-all duration-300",e?"bg-white/95 backdrop-blur-sm shadow-lg":"bg-transparent"),children:(0,r.jsxs)("div",{className:"container-custom",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-16 lg:h-20",children:[(0,r.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"relative w-10 h-10 lg:w-12 lg:h-12",children:(0,r.jsx)(o.default,{src:"/logo.png",alt:"Tera Works Logo",fill:!0,className:"object-contain",priority:!0})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-xl lg:text-2xl font-bold text-gray-900",children:"Tera Works"}),(0,r.jsx)("span",{className:"text-xs lg:text-sm text-gray-600 -mt-1",children:"Let's Grow Together"})]})]}),(0,r.jsx)("nav",{className:"hidden lg:flex items-center space-x-8",children:p.ss.map(s=>(0,r.jsx)(i(),{href:s.href,className:(0,x.cn)("text-sm font-medium transition-colors hover:text-primary-600",b(s.href)?"text-primary-600":e?"text-gray-900":"text-white"),children:s.label},s.href))}),(0,r.jsx)("div",{className:"hidden lg:flex items-center space-x-4",children:(0,r.jsx)(f.$n,{href:"/contact",variant:"primary",children:"Get Started"})}),(0,r.jsx)("button",{type:"button",onClick:()=>l(!t),className:(0,x.cn)("lg:hidden p-2 rounded-md transition-colors",e?"text-gray-900 hover:bg-gray-100":"text-white hover:bg-white/10"),"aria-label":"Toggle mobile menu",children:t?(0,r.jsx)(m.A,{className:"w-6 h-6"}):(0,r.jsx)(h.A,{className:"w-6 h-6"})})]}),(0,r.jsx)(c.N,{children:t&&(0,r.jsx)(d.P.div,{variants:u.Ym,initial:"closed",animate:"open",exit:"closed",className:"lg:hidden bg-white border-t border-gray-200 shadow-lg",children:(0,r.jsxs)("nav",{className:"py-4 space-y-2",children:[p.ss.map(e=>(0,r.jsx)(d.P.div,{variants:u.Tz,children:(0,r.jsx)(i(),{href:e.href,className:(0,x.cn)("block px-4 py-2 text-base font-medium transition-colors hover:bg-gray-50",b(e.href)?"text-primary-600 bg-primary-50":"text-gray-900"),children:e.label})},e.href)),(0,r.jsx)(d.P.div,{variants:u.Tz,className:"px-4 pt-4 border-t border-gray-200",children:(0,r.jsx)(f.$n,{href:"/contact",variant:"primary",className:"w-full",children:"Get Started"})})]})})})]})})}},8290:(e,s,t)=>{"use strict";t.d(s,{$n:()=>d,pd:()=>b,l6:()=>v,mB:()=>x,_f:()=>p,TM:()=>u});var r=t(687),a=t(3210),l=t(5814),i=t.n(l),o=t(6001),n=t(4780),c=t(4020);let d=({children:e,className:s,variant:t="primary",size:a="md",disabled:l=!1,loading:d=!1,onClick:m,type:h="button",href:x,external:p=!1,...f})=>{let u=(0,n.cn)("inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500",secondary:"bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500",outline:"border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500",ghost:"text-primary-600 hover:bg-primary-50 focus:ring-primary-500"}[t],{sm:"px-3 py-2 text-sm rounded-md",md:"px-4 py-2 text-base rounded-lg",lg:"px-6 py-3 text-lg rounded-lg"}[a],s),v=(0,r.jsxs)(r.Fragment,{children:[d&&(0,r.jsx)(()=>(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),{}),e]});return x?p?(0,r.jsx)(o.P.a,{href:x,target:"_blank",rel:"noopener noreferrer",className:u,variants:c.ru,initial:"idle",whileHover:"hover",whileTap:"tap",...f,children:v}):(0,r.jsx)(i(),{href:x,className:u,...f,children:(0,r.jsx)(o.P.span,{variants:c.ru,initial:"idle",whileHover:"hover",whileTap:"tap",className:"flex items-center justify-center w-full h-full",children:v})}):(0,r.jsx)(o.P.button,{type:h,className:u,disabled:l||d,onClick:m,variants:c.ru,initial:"idle",whileHover:l||d?"idle":"hover",whileTap:l||d?"idle":"tap",...f,children:v})};var m=t(474);let h=({children:e,className:s,title:t,description:a,image:l,href:d,hover:h=!0,...x})=>{let p=(0,n.cn)("bg-white rounded-lg shadow-md overflow-hidden transition-shadow duration-300",s),f=(0,r.jsxs)(o.P.div,{className:p,variants:h?c.fP:void 0,initial:"idle",whileHover:h?"hover":"idle",...x,children:[l&&(0,r.jsx)("div",{className:"relative h-48 w-full",children:(0,r.jsx)(m.default,{src:l,alt:t||"Card image",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),(0,r.jsxs)("div",{className:"p-6",children:[t&&(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:t}),a&&(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:a}),e]})]});return d?(0,r.jsx)(i(),{href:d,className:"block",children:f}):f},x=({title:e,description:s,icon:t,features:a,href:l,className:i})=>(0,r.jsxs)(h,{title:e,description:s,href:l,className:(0,n.cn)("h-full",i),children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("span",{className:"text-4xl mr-3",children:t}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:e})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:s}),(0,r.jsx)("ul",{className:"space-y-2",children:a.slice(0,4).map((e,s)=>(0,r.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-green-500 mr-2 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),e]},s))})]}),p=({name:e,position:s,company:t,content:a,avatar:l,rating:i,className:o})=>(0,r.jsxs)(h,{className:(0,n.cn)("h-full",o),hover:!1,children:[(0,r.jsx)("div",{className:"flex items-center mb-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,r.jsx)("svg",{className:(0,n.cn)("w-5 h-5",s<i?"text-yellow-400":"text-gray-300"),fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},s))}),(0,r.jsxs)("blockquote",{className:"text-gray-600 mb-4 italic",children:['"',a,'"']}),(0,r.jsxs)("div",{className:"flex items-center",children:[l&&(0,r.jsx)("div",{className:"relative w-12 h-12 mr-4",children:(0,r.jsx)(m.default,{src:l,alt:e,fill:!0,className:"rounded-full object-cover"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-semibold text-gray-900",children:e}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[s," at ",t]})]})]})]}),f=(0,a.forwardRef)(({className:e,label:s,error:t,helperText:l,variant:i="default",type:d="text",id:m,...h},x)=>{let[p,f]=(0,a.useState)(!1),[u,v]=(0,a.useState)(!1),b=m||`input-${Math.random().toString(36).substr(2,9)}`,g=(0,n.cn)("w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed",t?"border-red-300 focus:ring-red-500":"border-gray-300 focus:border-primary-500","floating"===i&&"pt-6 pb-2",e),y=e=>{f(!0),h.onFocus?.(e)},j=e=>{f(!1),v(""!==e.target.value),h.onBlur?.(e)},w=e=>{v(""!==e.target.value),h.onChange?.(e)};return"floating"===i?(0,r.jsxs)("div",{className:"relative mb-6",children:[(0,r.jsx)(o.P.input,{ref:x,type:d,id:b,className:g,onFocus:y,onBlur:j,onChange:w,variants:c.Xy,whileFocus:"focus",...h}),s&&(0,r.jsx)(o.P.label,{htmlFor:b,variants:c.pP,animate:p||u?"active":"default",className:"absolute left-3 top-2 pointer-events-none text-sm font-medium",children:s}),t&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:t}),l&&!t&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:l})]}):(0,r.jsxs)("div",{className:"mb-4",children:[s&&(0,r.jsx)("label",{htmlFor:b,className:"block text-sm font-medium text-gray-700 mb-1",children:s}),(0,r.jsx)(o.P.input,{ref:x,type:d,id:b,className:g,onFocus:y,onBlur:j,onChange:w,variants:c.Xy,whileFocus:"focus",...h}),t&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:t}),l&&!t&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:l})]})});f.displayName="Input";let u=(0,a.forwardRef)(({className:e,label:s,error:t,helperText:a,id:l,...i},d)=>{let m=l||`textarea-${Math.random().toString(36).substr(2,9)}`,h=(0,n.cn)("w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed resize-vertical min-h-[100px]",t?"border-red-300 focus:ring-red-500":"border-gray-300 focus:border-primary-500",e);return(0,r.jsxs)("div",{className:"mb-4",children:[s&&(0,r.jsx)("label",{htmlFor:m,className:"block text-sm font-medium text-gray-700 mb-1",children:s}),(0,r.jsx)(o.P.textarea,{ref:d,id:m,className:h,variants:c.Xy,whileFocus:"focus",...i}),t&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:t}),a&&!t&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:a})]})});u.displayName="Textarea";let v=(0,a.forwardRef)(({className:e,label:s,error:t,helperText:a,options:l,id:i,...d},m)=>{let h=i||`select-${Math.random().toString(36).substr(2,9)}`,x=(0,n.cn)("w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed bg-white",t?"border-red-300 focus:ring-red-500":"border-gray-300 focus:border-primary-500",e);return(0,r.jsxs)("div",{className:"mb-4",children:[s&&(0,r.jsx)("label",{htmlFor:h,className:"block text-sm font-medium text-gray-700 mb-1",children:s}),(0,r.jsx)(o.P.select,{ref:m,id:h,className:x,variants:c.Xy,whileFocus:"focus",...d,children:l.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))}),t&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:t}),a&&!t&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:a})]})});v.displayName="Select";let b=f},8926:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Header.tsx","default")}};