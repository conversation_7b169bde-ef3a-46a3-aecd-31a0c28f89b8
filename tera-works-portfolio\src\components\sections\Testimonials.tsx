'use client';

import React, { useRef, useState, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { ChevronLeftIcon, ChevronRightIcon, StarIcon } from '@heroicons/react/24/solid';
import { TestimonialCard } from '@/components/ui';
import { useGSAP } from '@gsap/react';
import { animations } from '@/lib/animations/gsap';
import { testimonials } from '@/data/testimonials';
import { staggerContainer, staggerItem } from '@/lib/animations/framer';

const Testimonials: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const carouselRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  useGSAP(() => {
    if (carouselRef.current) {
      animations.scrollReveal(carouselRef.current, {
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 80%',
        }
      });
    }
  }, { scope: sectionRef });

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    );
    setIsAutoPlaying(false);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
    setIsAutoPlaying(false);
  };

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
  };

  return (
    <section 
      id="testimonials"
      ref={sectionRef}
      className="section-padding bg-white"
    >
      <div className="container-custom">
        {/* Header */}
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div variants={staggerItem}>
            <span className="inline-block px-4 py-2 bg-accent-100 text-accent-800 rounded-full text-sm font-medium mb-6">
              Client Testimonials
            </span>
          </motion.div>

          <motion.h2 
            variants={staggerItem}
            className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6"
          >
            What Our Clients Say
          </motion.h2>

          <motion.p 
            variants={staggerItem}
            className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed"
          >
            Don't just take our word for it. Here's what our satisfied clients have to say 
            about working with Tera Works and the results we've achieved together.
          </motion.p>
        </motion.div>

        {/* Main Testimonial Carousel */}
        <div 
          ref={carouselRef}
          className="relative max-w-4xl mx-auto mb-16"
        >
          <div className="overflow-hidden rounded-2xl">
            <motion.div
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentIndex * 100}%)` }}
            >
              {testimonials.map((testimonial, index) => (
                <div key={testimonial.id} className="w-full flex-shrink-0">
                  <div className="bg-gradient-to-br from-primary-50 to-accent-50 p-8 lg:p-12 text-center">
                    {/* Rating Stars */}
                    <div className="flex justify-center mb-6">
                      {[...Array(5)].map((_, i) => (
                        <StarIcon
                          key={i}
                          className={`w-6 h-6 ${
                            i < testimonial.rating ? 'text-yellow-400' : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>

                    {/* Quote */}
                    <blockquote className="text-xl lg:text-2xl text-gray-700 font-medium mb-8 leading-relaxed italic">
                      "{testimonial.content}"
                    </blockquote>

                    {/* Client Info */}
                    <div className="flex items-center justify-center space-x-4">
                      {testimonial.avatar && (
                        <div className="relative w-16 h-16">
                          <Image
                            src={testimonial.avatar}
                            alt={testimonial.name}
                            fill
                            className="rounded-full object-cover border-4 border-white shadow-lg"
                            sizes="64px"
                          />
                        </div>
                      )}
                      <div className="text-left">
                        <div className="font-bold text-gray-900 text-lg">
                          {testimonial.name}
                        </div>
                        <div className="text-gray-600">
                          {testimonial.position} at {testimonial.company}
                        </div>
                        <div className="text-primary-600 text-sm font-medium">
                          {testimonial.projectType}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </motion.div>
          </div>

          {/* Navigation Arrows */}
          <button
            type="button"
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-shadow duration-300 text-gray-600 hover:text-primary-600"
            aria-label="Previous testimonial"
          >
            <ChevronLeftIcon className="w-6 h-6" />
          </button>

          <button
            type="button"
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white rounded-full p-3 shadow-lg hover:shadow-xl transition-shadow duration-300 text-gray-600 hover:text-primary-600"
            aria-label="Next testimonial"
          >
            <ChevronRightIcon className="w-6 h-6" />
          </button>

          {/* Dots Indicator */}
          <div className="flex justify-center space-x-2 mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                type="button"
                onClick={() => goToTestimonial(index)}
                className={`w-3 h-3 rounded-full transition-colors duration-300 ${
                  index === currentIndex ? 'bg-primary-600' : 'bg-gray-300'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>

        {/* Testimonial Grid */}
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {testimonials.slice(0, 3).map((testimonial, index) => (
            <motion.div key={testimonial.id} variants={staggerItem}>
              <TestimonialCard
                name={testimonial.name}
                position={testimonial.position}
                company={testimonial.company}
                content={testimonial.content}
                avatar={testimonial.avatar}
                rating={testimonial.rating}
                className="h-full"
              />
            </motion.div>
          ))}
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="mt-16 pt-16 border-t border-gray-200"
        >
          <motion.div variants={staggerItem} className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Trusted by Growing Businesses
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We're proud to work with businesses of all sizes, from startups to established companies, 
              helping them achieve their digital marketing goals.
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center opacity-60">
            {/* Client Logos Placeholder */}
            {[1, 2, 3, 4].map((i) => (
              <motion.div
                key={i}
                variants={staggerItem}
                className="bg-gray-200 h-16 rounded-lg flex items-center justify-center"
              >
                <span className="text-gray-500 font-medium">Client Logo {i}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Testimonials;
