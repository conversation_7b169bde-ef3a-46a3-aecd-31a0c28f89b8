[{"name": "generate-buildid", "duration": 402, "timestamp": 1565890691944, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750438432769, "traceId": "8400fc57bd9d4328"}, {"name": "load-custom-routes", "duration": 4118, "timestamp": 1565890692549, "id": 5, "parentId": 1, "tags": {}, "startTime": 1750438432769, "traceId": "8400fc57bd9d4328"}, {"name": "create-dist-dir", "duration": 663, "timestamp": 1565890915159, "id": 6, "parentId": 1, "tags": {}, "startTime": 1750438432992, "traceId": "8400fc57bd9d4328"}, {"name": "create-pages-mapping", "duration": 541, "timestamp": 1565890974852, "id": 7, "parentId": 1, "tags": {}, "startTime": 1750438433052, "traceId": "8400fc57bd9d4328"}, {"name": "collect-app-paths", "duration": 7420, "timestamp": 1565890975474, "id": 8, "parentId": 1, "tags": {}, "startTime": 1750438433052, "traceId": "8400fc57bd9d4328"}, {"name": "create-app-mapping", "duration": 25398, "timestamp": 1565890982963, "id": 9, "parentId": 1, "tags": {}, "startTime": 1750438433060, "traceId": "8400fc57bd9d4328"}, {"name": "public-dir-conflict-check", "duration": 15356, "timestamp": 1565891009160, "id": 10, "parentId": 1, "tags": {}, "startTime": 1750438433086, "traceId": "8400fc57bd9d4328"}, {"name": "generate-routes-manifest", "duration": 4244, "timestamp": 1565891025300, "id": 11, "parentId": 1, "tags": {}, "startTime": 1750438433102, "traceId": "8400fc57bd9d4328"}, {"name": "create-entrypoints", "duration": 40772, "timestamp": 1565893463251, "id": 15, "parentId": 13, "tags": {}, "startTime": 1750438435540, "traceId": "8400fc57bd9d4328"}, {"name": "generate-webpack-config", "duration": 1512182, "timestamp": 1565893504480, "id": 16, "parentId": 14, "tags": {}, "startTime": 1750438435581, "traceId": "8400fc57bd9d4328"}, {"name": "next-trace-entrypoint-plugin", "duration": 4172, "timestamp": 1565895463616, "id": 18, "parentId": 17, "tags": {}, "startTime": 1750438437540, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 896825, "timestamp": 1565895489648, "id": 22, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1750438437566, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 1007723, "timestamp": 1565895489716, "id": 23, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1750438437566, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 1187935, "timestamp": 1565895489805, "id": 25, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1750438437566, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 1324064, "timestamp": 1565895489335, "id": 21, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750438437566, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 1340476, "timestamp": 1565895488133, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750438437565, "traceId": "8400fc57bd9d4328"}, {"name": "build-module-tsx", "duration": 32081, "timestamp": 1565896837034, "id": 26, "parentId": 17, "tags": {"name": "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\About.tsx", "layer": "rsc"}, "startTime": 1750438438914, "traceId": "8400fc57bd9d4328"}, {"name": "build-module-tsx", "duration": 25048, "timestamp": 1565896846009, "id": 27, "parentId": 17, "tags": {"name": "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Contact.tsx", "layer": "rsc"}, "startTime": 1750438438923, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 1383676, "timestamp": 1565895489764, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750438437566, "traceId": "8400fc57bd9d4328"}, {"name": "build-module-tsx", "duration": 85937, "timestamp": 1565896972233, "id": 38, "parentId": 17, "tags": {"name": "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\About.tsx", "layer": "ssr"}, "startTime": 1750438439049, "traceId": "8400fc57bd9d4328"}, {"name": "build-module-tsx", "duration": 102384, "timestamp": 1565896977348, "id": 39, "parentId": 17, "tags": {"name": "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Contact.tsx", "layer": "ssr"}, "startTime": 1750438439054, "traceId": "8400fc57bd9d4328"}, {"name": "make", "duration": 2393782, "timestamp": 1565895487237, "id": 19, "parentId": 17, "tags": {}, "startTime": 1750438437564, "traceId": "8400fc57bd9d4328"}, {"name": "get-entries", "duration": 2610, "timestamp": 1565897883551, "id": 41, "parentId": 40, "tags": {}, "startTime": 1750438439960, "traceId": "8400fc57bd9d4328"}, {"name": "node-file-trace-plugin", "duration": 360293, "timestamp": 1565897891388, "id": 42, "parentId": 40, "tags": {"traceEntryCount": "8"}, "startTime": 1750438439968, "traceId": "8400fc57bd9d4328"}, {"name": "collect-traced-files", "duration": 1463, "timestamp": 1565898251863, "id": 43, "parentId": 40, "tags": {}, "startTime": 1750438440328, "traceId": "8400fc57bd9d4328"}, {"name": "finish-modules", "duration": 370294, "timestamp": 1565897883049, "id": 40, "parentId": 18, "tags": {}, "startTime": 1750438439960, "traceId": "8400fc57bd9d4328"}, {"name": "chunk-graph", "duration": 45651, "timestamp": 1565898369172, "id": 45, "parentId": 44, "tags": {}, "startTime": 1750438440446, "traceId": "8400fc57bd9d4328"}, {"name": "optimize-modules", "duration": 88, "timestamp": 1565898415087, "id": 47, "parentId": 44, "tags": {}, "startTime": 1750438440492, "traceId": "8400fc57bd9d4328"}, {"name": "optimize-chunks", "duration": 49733, "timestamp": 1565898415357, "id": 48, "parentId": 44, "tags": {}, "startTime": 1750438440492, "traceId": "8400fc57bd9d4328"}, {"name": "optimize-tree", "duration": 470, "timestamp": 1565898465466, "id": 49, "parentId": 44, "tags": {}, "startTime": 1750438440542, "traceId": "8400fc57bd9d4328"}, {"name": "optimize-chunk-modules", "duration": 76512, "timestamp": 1565898466150, "id": 50, "parentId": 44, "tags": {}, "startTime": 1750438440543, "traceId": "8400fc57bd9d4328"}, {"name": "optimize", "duration": 127902, "timestamp": 1565898414974, "id": 46, "parentId": 44, "tags": {}, "startTime": 1750438440492, "traceId": "8400fc57bd9d4328"}, {"name": "module-hash", "duration": 66659, "timestamp": 1565898608221, "id": 51, "parentId": 44, "tags": {}, "startTime": 1750438440685, "traceId": "8400fc57bd9d4328"}, {"name": "code-generation", "duration": 114644, "timestamp": 1565898675023, "id": 52, "parentId": 44, "tags": {}, "startTime": 1750438440752, "traceId": "8400fc57bd9d4328"}, {"name": "hash", "duration": 26645, "timestamp": 1565898805785, "id": 53, "parentId": 44, "tags": {}, "startTime": 1750438440882, "traceId": "8400fc57bd9d4328"}, {"name": "code-generation-jobs", "duration": 684, "timestamp": 1565898832422, "id": 54, "parentId": 44, "tags": {}, "startTime": 1750438440909, "traceId": "8400fc57bd9d4328"}, {"name": "module-assets", "duration": 828, "timestamp": 1565898832988, "id": 55, "parentId": 44, "tags": {}, "startTime": 1750438440910, "traceId": "8400fc57bd9d4328"}, {"name": "create-chunk-assets", "duration": 9159, "timestamp": 1565898833878, "id": 56, "parentId": 44, "tags": {}, "startTime": 1750438440910, "traceId": "8400fc57bd9d4328"}, {"name": "minify-js", "duration": 30838, "timestamp": 1565898884410, "id": 58, "parentId": 57, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1750438440961, "traceId": "8400fc57bd9d4328"}, {"name": "minify-js", "duration": 28145, "timestamp": 1565898887120, "id": 59, "parentId": 57, "tags": {"name": "../app/favicon.ico/route.js", "cache": "HIT"}, "startTime": 1750438440964, "traceId": "8400fc57bd9d4328"}, {"name": "minify-js", "duration": 28077, "timestamp": 1565898887192, "id": 60, "parentId": 57, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1750438440964, "traceId": "8400fc57bd9d4328"}, {"name": "minify-js", "duration": 28056, "timestamp": 1565898887215, "id": 61, "parentId": 57, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1750438440964, "traceId": "8400fc57bd9d4328"}, {"name": "minify-js", "duration": 368, "timestamp": 1565898914907, "id": 63, "parentId": 57, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1750438440992, "traceId": "8400fc57bd9d4328"}, {"name": "minify-js", "duration": 299, "timestamp": 1565898914982, "id": 64, "parentId": 57, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1750438440992, "traceId": "8400fc57bd9d4328"}, {"name": "minify-js", "duration": 280, "timestamp": 1565898915004, "id": 65, "parentId": 57, "tags": {"name": "447.js", "cache": "HIT"}, "startTime": 1750438440992, "traceId": "8400fc57bd9d4328"}, {"name": "minify-js", "duration": 264, "timestamp": 1565898915024, "id": 66, "parentId": 57, "tags": {"name": "37.js", "cache": "HIT"}, "startTime": 1750438440992, "traceId": "8400fc57bd9d4328"}, {"name": "minify-js", "duration": 251, "timestamp": 1565898915040, "id": 67, "parentId": 57, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1750438440992, "traceId": "8400fc57bd9d4328"}, {"name": "minify-js", "duration": 87, "timestamp": 1565898915206, "id": 68, "parentId": 57, "tags": {"name": "785.js", "cache": "HIT"}, "startTime": 1750438440992, "traceId": "8400fc57bd9d4328"}, {"name": "minify-js", "duration": 330815, "timestamp": 1565898887235, "id": 62, "parentId": 57, "tags": {"name": "../app/page.js", "cache": "MISS"}, "startTime": 1750438440964, "traceId": "8400fc57bd9d4328"}, {"name": "minify-webpack-plugin-optimize", "duration": 360065, "timestamp": 1565898858012, "id": 57, "parentId": 17, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1750438440935, "traceId": "8400fc57bd9d4328"}, {"name": "css-minimizer-plugin", "duration": 488, "timestamp": 1565899218752, "id": 69, "parentId": 17, "tags": {}, "startTime": 1750438441295, "traceId": "8400fc57bd9d4328"}, {"name": "create-trace-assets", "duration": 5856, "timestamp": 1565899220345, "id": 70, "parentId": 18, "tags": {}, "startTime": 1750438441297, "traceId": "8400fc57bd9d4328"}, {"name": "seal", "duration": 951098, "timestamp": 1565898317015, "id": 44, "parentId": 17, "tags": {}, "startTime": 1750438440394, "traceId": "8400fc57bd9d4328"}, {"name": "webpack-compilation", "duration": 3842912, "timestamp": 1565895456550, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1750438437533, "traceId": "8400fc57bd9d4328"}, {"name": "emit", "duration": 421366, "timestamp": 1565899301067, "id": 71, "parentId": 14, "tags": {}, "startTime": 1750438441378, "traceId": "8400fc57bd9d4328"}, {"name": "webpack-close", "duration": 1030613, "timestamp": 1565899726472, "id": 72, "parentId": 14, "tags": {"name": "server"}, "startTime": 1750438441803, "traceId": "8400fc57bd9d4328"}, {"name": "webpack-generate-error-stats", "duration": 11792, "timestamp": 1565900757227, "id": 73, "parentId": 72, "tags": {}, "startTime": 1750438442834, "traceId": "8400fc57bd9d4328"}, {"name": "run-webpack-compiler", "duration": 7306425, "timestamp": 1565893463230, "id": 14, "parentId": 13, "tags": {}, "startTime": 1750438435540, "traceId": "8400fc57bd9d4328"}, {"name": "format-webpack-messages", "duration": 184, "timestamp": 1565900769675, "id": 74, "parentId": 13, "tags": {}, "startTime": 1750438442846, "traceId": "8400fc57bd9d4328"}, {"name": "worker-main-server", "duration": 7308009, "timestamp": 1565893462356, "id": 13, "parentId": 1, "tags": {}, "startTime": 1750438435539, "traceId": "8400fc57bd9d4328"}, {"name": "create-entrypoints", "duration": 49748, "timestamp": 1565903680543, "id": 77, "parentId": 75, "tags": {}, "startTime": 1750438445757, "traceId": "8400fc57bd9d4328"}, {"name": "generate-webpack-config", "duration": 1246381, "timestamp": 1565903730667, "id": 78, "parentId": 76, "tags": {}, "startTime": 1750438445807, "traceId": "8400fc57bd9d4328"}, {"name": "make", "duration": 2241, "timestamp": 1565905403117, "id": 80, "parentId": 79, "tags": {}, "startTime": 1750438447480, "traceId": "8400fc57bd9d4328"}, {"name": "chunk-graph", "duration": 1455, "timestamp": 1565905412868, "id": 82, "parentId": 81, "tags": {}, "startTime": 1750438447490, "traceId": "8400fc57bd9d4328"}, {"name": "optimize-modules", "duration": 122, "timestamp": 1565905414843, "id": 84, "parentId": 81, "tags": {}, "startTime": 1750438447492, "traceId": "8400fc57bd9d4328"}, {"name": "optimize-chunks", "duration": 5013, "timestamp": 1565905415216, "id": 85, "parentId": 81, "tags": {}, "startTime": 1750438447492, "traceId": "8400fc57bd9d4328"}, {"name": "optimize-tree", "duration": 494, "timestamp": 1565905420437, "id": 86, "parentId": 81, "tags": {}, "startTime": 1750438447497, "traceId": "8400fc57bd9d4328"}, {"name": "optimize-chunk-modules", "duration": 1563, "timestamp": 1565905421931, "id": 87, "parentId": 81, "tags": {}, "startTime": 1750438447499, "traceId": "8400fc57bd9d4328"}, {"name": "optimize", "duration": 9242, "timestamp": 1565905414496, "id": 83, "parentId": 81, "tags": {}, "startTime": 1750438447491, "traceId": "8400fc57bd9d4328"}, {"name": "module-hash", "duration": 210, "timestamp": 1565905426354, "id": 88, "parentId": 81, "tags": {}, "startTime": 1750438447503, "traceId": "8400fc57bd9d4328"}, {"name": "code-generation", "duration": 562, "timestamp": 1565905426698, "id": 89, "parentId": 81, "tags": {}, "startTime": 1750438447503, "traceId": "8400fc57bd9d4328"}, {"name": "hash", "duration": 1019, "timestamp": 1565905427921, "id": 90, "parentId": 81, "tags": {}, "startTime": 1750438447505, "traceId": "8400fc57bd9d4328"}, {"name": "code-generation-jobs", "duration": 334, "timestamp": 1565905428931, "id": 91, "parentId": 81, "tags": {}, "startTime": 1750438447506, "traceId": "8400fc57bd9d4328"}, {"name": "module-assets", "duration": 204, "timestamp": 1565905429195, "id": 92, "parentId": 81, "tags": {}, "startTime": 1750438447506, "traceId": "8400fc57bd9d4328"}, {"name": "create-chunk-assets", "duration": 458, "timestamp": 1565905429424, "id": 93, "parentId": 81, "tags": {}, "startTime": 1750438447506, "traceId": "8400fc57bd9d4328"}, {"name": "minify-js", "duration": 446, "timestamp": 1565905466034, "id": 95, "parentId": 94, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1750438447543, "traceId": "8400fc57bd9d4328"}, {"name": "minify-webpack-plugin-optimize", "duration": 6191, "timestamp": 1565905460316, "id": 94, "parentId": 79, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1750438447537, "traceId": "8400fc57bd9d4328"}, {"name": "css-minimizer-plugin", "duration": 4494, "timestamp": 1565905466773, "id": 96, "parentId": 79, "tags": {}, "startTime": 1750438447543, "traceId": "8400fc57bd9d4328"}, {"name": "seal", "duration": 69882, "timestamp": 1565905411204, "id": 81, "parentId": 79, "tags": {}, "startTime": 1750438447488, "traceId": "8400fc57bd9d4328"}, {"name": "webpack-compilation", "duration": 104956, "timestamp": 1565905376936, "id": 79, "parentId": 76, "tags": {"name": "edge-server"}, "startTime": 1750438447454, "traceId": "8400fc57bd9d4328"}, {"name": "emit", "duration": 13562, "timestamp": 1565905482845, "id": 97, "parentId": 76, "tags": {}, "startTime": 1750438447560, "traceId": "8400fc57bd9d4328"}, {"name": "webpack-close", "duration": 4893, "timestamp": 1565905500071, "id": 98, "parentId": 76, "tags": {"name": "edge-server"}, "startTime": 1750438447577, "traceId": "8400fc57bd9d4328"}, {"name": "webpack-generate-error-stats", "duration": 8477, "timestamp": 1565905505134, "id": 99, "parentId": 98, "tags": {}, "startTime": 1750438447582, "traceId": "8400fc57bd9d4328"}, {"name": "run-webpack-compiler", "duration": 1833430, "timestamp": 1565903680522, "id": 76, "parentId": 75, "tags": {}, "startTime": 1750438445757, "traceId": "8400fc57bd9d4328"}, {"name": "format-webpack-messages", "duration": 196, "timestamp": 1565905513968, "id": 100, "parentId": 75, "tags": {}, "startTime": 1750438447591, "traceId": "8400fc57bd9d4328"}, {"name": "worker-main-edge-server", "duration": 1834978, "timestamp": 1565903679557, "id": 75, "parentId": 1, "tags": {}, "startTime": 1750438445756, "traceId": "8400fc57bd9d4328"}, {"name": "create-entrypoints", "duration": 61362, "timestamp": 1565908246362, "id": 103, "parentId": 101, "tags": {}, "startTime": 1750438450324, "traceId": "8400fc57bd9d4328"}, {"name": "generate-webpack-config", "duration": 1118275, "timestamp": 1565908308118, "id": 104, "parentId": 102, "tags": {}, "startTime": 1750438450385, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 884679, "timestamp": 1565909880320, "id": 110, "parentId": 106, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1750438451958, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 1008184, "timestamp": 1565909880562, "id": 111, "parentId": 106, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1750438451958, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 1018397, "timestamp": 1565909880681, "id": 113, "parentId": 106, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1750438451958, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 1068722, "timestamp": 1565909880632, "id": 112, "parentId": 106, "tags": {"request": "E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1750438451958, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 1132207, "timestamp": 1565909880244, "id": 109, "parentId": 106, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1750438451957, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 1134572, "timestamp": 1565909880128, "id": 108, "parentId": 106, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1750438451957, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 1157956, "timestamp": 1565909878918, "id": 107, "parentId": 106, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1750438451956, "traceId": "8400fc57bd9d4328"}, {"name": "build-module-tsx", "duration": 114175, "timestamp": 1565911038994, "id": 116, "parentId": 105, "tags": {"name": "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\About.tsx", "layer": "app-pages-browser"}, "startTime": 1750438453116, "traceId": "8400fc57bd9d4328"}, {"name": "build-module-tsx", "duration": 125625, "timestamp": 1565911054219, "id": 117, "parentId": 105, "tags": {"name": "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Contact.tsx", "layer": "app-pages-browser"}, "startTime": 1750438453131, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 1769421, "timestamp": 1565909880711, "id": 114, "parentId": 106, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"}, "startTime": 1750438451958, "traceId": "8400fc57bd9d4328"}, {"name": "add-entry", "duration": 1769422, "timestamp": 1565909880740, "id": 115, "parentId": 106, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CPortfolio.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CTestimonials.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"}, "startTime": 1750438451958, "traceId": "8400fc57bd9d4328"}, {"name": "make", "duration": 1773600, "timestamp": 1565909877304, "id": 106, "parentId": 105, "tags": {}, "startTime": 1750438451955, "traceId": "8400fc57bd9d4328"}, {"name": "chunk-graph", "duration": 60941, "timestamp": 1565911785221, "id": 119, "parentId": 118, "tags": {}, "startTime": 1750438453862, "traceId": "8400fc57bd9d4328"}]