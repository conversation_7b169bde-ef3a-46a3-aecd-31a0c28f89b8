[{"name":"generate-buildid","duration":389,"timestamp":1567811145421,"id":4,"parentId":1,"tags":{},"startTime":1750440353222,"traceId":"6c3b02f8e23377ed"},{"name":"load-custom-routes","duration":2871,"timestamp":1567811146005,"id":5,"parentId":1,"tags":{},"startTime":1750440353223,"traceId":"6c3b02f8e23377ed"},{"name":"create-dist-dir","duration":663,"timestamp":1567811333833,"id":6,"parentId":1,"tags":{},"startTime":1750440353411,"traceId":"6c3b02f8e23377ed"},{"name":"create-pages-mapping","duration":430,"timestamp":1567811711944,"id":7,"parentId":1,"tags":{},"startTime":1750440353789,"traceId":"6c3b02f8e23377ed"},{"name":"collect-app-paths","duration":4094,"timestamp":1567811712457,"id":8,"parentId":1,"tags":{},"startTime":1750440353790,"traceId":"6c3b02f8e23377ed"},{"name":"create-app-mapping","duration":9024,"timestamp":1567811716631,"id":9,"parentId":1,"tags":{},"startTime":1750440353794,"traceId":"6c3b02f8e23377ed"},{"name":"public-dir-conflict-check","duration":1144,"timestamp":1567811727048,"id":10,"parentId":1,"tags":{},"startTime":1750440353804,"traceId":"6c3b02f8e23377ed"},{"name":"generate-routes-manifest","duration":5321,"timestamp":1567811728590,"id":11,"parentId":1,"tags":{},"startTime":1750440353806,"traceId":"6c3b02f8e23377ed"},{"name":"create-entrypoints","duration":101146,"timestamp":1567814988694,"id":15,"parentId":13,"tags":{},"startTime":1750440357066,"traceId":"6c3b02f8e23377ed"},{"name":"generate-webpack-config","duration":1157536,"timestamp":1567815090474,"id":16,"parentId":14,"tags":{},"startTime":1750440357168,"traceId":"6c3b02f8e23377ed"},{"name":"next-trace-entrypoint-plugin","duration":5242,"timestamp":1567816621100,"id":18,"parentId":17,"tags":{},"startTime":1750440358699,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":223526,"timestamp":1567817359030,"id":34,"parentId":20,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?page=%2Fapi%2Fcontact%2Froute&name=app%2Fapi%2Fcontact%2Froute&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fapi%2Fcontact%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750440359436,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":183056,"timestamp":1567817405479,"id":36,"parentId":22,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?page=%2Fsitemap.xml%2Froute&name=app%2Fsitemap.xml%2Froute&pagePath=private-next-app-dir%2Fsitemap.ts&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fsitemap&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750440359483,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":194928,"timestamp":1567817404975,"id":35,"parentId":21,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?page=%2Frobots.txt%2Froute&name=app%2Frobots.txt%2Froute&pagePath=private-next-app-dir%2Frobots.ts&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Frobots&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750440359482,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":415658,"timestamp":1567817405827,"id":37,"parentId":26,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fnot-found&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750440359483,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":411105,"timestamp":1567817487223,"id":43,"parentId":27,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750440359565,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":1261197,"timestamp":1567816647021,"id":25,"parentId":19,"tags":{"request":"next/dist/pages/_app"},"startTime":1750440358724,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":1261442,"timestamp":1567816646988,"id":24,"parentId":19,"tags":{"request":"next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"},"startTime":1750440358724,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":1401802,"timestamp":1567816647110,"id":28,"parentId":19,"tags":{"request":"next/dist/pages/_document"},"startTime":1750440358725,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-ts","duration":118269,"timestamp":1567818047328,"id":47,"parentId":45,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\robots.ts","layer":"rsc"},"startTime":1750440360125,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":460144,"timestamp":1567817720783,"id":45,"parentId":35,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-metadata-route-loader.js?filePath=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp%5Crobots.ts&isDynamicRouteExtension=1!?__next_metadata_route__","layer":"rsc"},"startTime":1750440359798,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-ts","duration":235328,"timestamp":1567818035774,"id":46,"parentId":44,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\sitemap.ts","layer":"rsc"},"startTime":1750440360113,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-ts","duration":240733,"timestamp":1567818048257,"id":48,"parentId":34,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\api\\contact\\route.ts","layer":"rsc"},"startTime":1750440360126,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":599654,"timestamp":1567817719198,"id":44,"parentId":36,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-metadata-route-loader.js?filePath=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp%5Csitemap.ts&isDynamicRouteExtension=1!?__next_metadata_route__","layer":"rsc"},"startTime":1750440359797,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":147735,"timestamp":1567818292535,"id":50,"parentId":37,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\error.tsx","layer":"rsc"},"startTime":1750440360370,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":160057,"timestamp":1567818291982,"id":49,"parentId":37,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\not-found.tsx","layer":"rsc"},"startTime":1750440360369,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":160956,"timestamp":1567818292934,"id":51,"parentId":37,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\loading.tsx","layer":"rsc"},"startTime":1750440360370,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2006825,"timestamp":1567816646950,"id":23,"parentId":19,"tags":{"request":"next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750440358724,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2025723,"timestamp":1567816643546,"id":20,"parentId":19,"tags":{"request":"next-app-loader?page=%2Fapi%2Fcontact%2Froute&name=app%2Fapi%2Fcontact%2Froute&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fapi%2Fcontact%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750440358721,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":1277314,"timestamp":1567817407152,"id":38,"parentId":29,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?page=%2Fportfolio%2Fpage&name=app%2Fportfolio%2Fpage&pagePath=private-next-app-dir%2Fportfolio%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fportfolio%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750440359485,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-js","duration":51921,"timestamp":1567818681531,"id":52,"parentId":45,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\metadata\\resolve-route-data.js","layer":"rsc"},"startTime":1750440360759,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2135888,"timestamp":1567816646808,"id":21,"parentId":19,"tags":{"request":"next-app-loader?page=%2Frobots.txt%2Froute&name=app%2Frobots.txt%2Froute&pagePath=private-next-app-dir%2Frobots.ts&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Frobots&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750440358724,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2135822,"timestamp":1567816646898,"id":22,"parentId":19,"tags":{"request":"next-app-loader?page=%2Fsitemap.xml%2Froute&name=app%2Fsitemap.xml%2Froute&pagePath=private-next-app-dir%2Fsitemap.ts&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fsitemap&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750440358724,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":1377502,"timestamp":1567817408088,"id":40,"parentId":31,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?page=%2Fservices%2Fpage&name=app%2Fservices%2Fpage&pagePath=private-next-app-dir%2Fservices%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fservices%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750440359486,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":1440884,"timestamp":1567817407740,"id":39,"parentId":30,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?page=%2Fblog%2Fpage&name=app%2Fblog%2Fpage&pagePath=private-next-app-dir%2Fblog%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fblog%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750440359485,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":1496846,"timestamp":1567817408760,"id":42,"parentId":33,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?page=%2Fcontact%2Fpage&name=app%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fcontact%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750440359486,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":1561090,"timestamp":1567817408419,"id":41,"parentId":32,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-app-loader\\index.js?page=%2Fabout%2Fpage&name=app%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fabout%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!","layer":"rsc"},"startTime":1750440359486,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":13480,"timestamp":1567819021740,"id":53,"parentId":38,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\portfolio\\page.tsx","layer":"rsc"},"startTime":1750440361099,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":17185,"timestamp":1567819022161,"id":54,"parentId":40,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\services\\page.tsx","layer":"rsc"},"startTime":1750440361100,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":48838,"timestamp":1567819023064,"id":56,"parentId":42,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\contact\\page.tsx","layer":"rsc"},"startTime":1750440361100,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":55867,"timestamp":1567819022714,"id":55,"parentId":39,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\blog\\page.tsx","layer":"rsc"},"startTime":1750440361100,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2491203,"timestamp":1567816647470,"id":29,"parentId":19,"tags":{"request":"next-app-loader?page=%2Fportfolio%2Fpage&name=app%2Fportfolio%2Fpage&pagePath=private-next-app-dir%2Fportfolio%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fportfolio%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750440358725,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2491814,"timestamp":1567816647594,"id":31,"parentId":19,"tags":{"request":"next-app-loader?page=%2Fservices%2Fpage&name=app%2Fservices%2Fpage&pagePath=private-next-app-dir%2Fservices%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fservices%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750440358725,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":19289,"timestamp":1567819132772,"id":57,"parentId":41,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\about\\page.tsx","layer":"rsc"},"startTime":1750440361210,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":14693,"timestamp":1567819141637,"id":58,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Contact.tsx","layer":"rsc"},"startTime":1750440361219,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2511107,"timestamp":1567816647613,"id":33,"parentId":19,"tags":{"request":"next-app-loader?page=%2Fcontact%2Fpage&name=app%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fcontact%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750440358725,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-js","duration":7198,"timestamp":1567819169952,"id":59,"parentId":55,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\client\\app-dir\\link.js","layer":"rsc"},"startTime":1750440361247,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2535028,"timestamp":1567816647602,"id":32,"parentId":19,"tags":{"request":"next-app-loader?page=%2Fabout%2Fpage&name=app%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fabout%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750440358725,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2537252,"timestamp":1567816647570,"id":30,"parentId":19,"tags":{"request":"next-app-loader?page=%2Fblog%2Fpage&name=app%2Fblog%2Fpage&pagePath=private-next-app-dir%2Fblog%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fblog%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750440358725,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-ts","duration":2822,"timestamp":1567819188783,"id":60,"parentId":49,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\index.ts","layer":"rsc"},"startTime":1750440361266,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":4353,"timestamp":1567819223270,"id":61,"parentId":60,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Button.tsx","layer":"rsc"},"startTime":1750440361301,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":8968,"timestamp":1567819223728,"id":62,"parentId":60,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Card.tsx","layer":"rsc"},"startTime":1750440361301,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":7244,"timestamp":1567819232898,"id":63,"parentId":60,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Modal.tsx","layer":"rsc"},"startTime":1750440361310,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":9202,"timestamp":1567819233322,"id":64,"parentId":60,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Input.tsx","layer":"rsc"},"startTime":1750440361311,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2598710,"timestamp":1567816647048,"id":26,"parentId":19,"tags":{"request":"next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fnot-found&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750440358724,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2598700,"timestamp":1567816647081,"id":27,"parentId":19,"tags":{"request":"next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CAugment%20Code%20Testing%5Ctera-works-portfolio%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1750440358725,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":6305,"timestamp":1567819710531,"id":165,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?server=true!","layer":"ssr"},"startTime":1750440361788,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":368,"timestamp":1567819716986,"id":166,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?server=true!","layer":"rsc"},"startTime":1750440361794,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":2419,"timestamp":1567819717399,"id":167,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCard.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22ServiceCard%22%2C%22PortfolioCard%22%2C%22TestimonialCard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CInput.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22Textarea%22%2C%22Select%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CModal.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!","layer":"ssr"},"startTime":1750440361795,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":1050,"timestamp":1567819719874,"id":168,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCard.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22ServiceCard%22%2C%22PortfolioCard%22%2C%22TestimonialCard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CInput.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22Textarea%22%2C%22Select%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CModal.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!","layer":"rsc"},"startTime":1750440361797,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":481,"timestamp":1567819720973,"id":169,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!","layer":"ssr"},"startTime":1750440361798,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":393,"timestamp":1567819721491,"id":170,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!","layer":"rsc"},"startTime":1750440361799,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":382,"timestamp":1567819721914,"id":171,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CPortfolio.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!","layer":"ssr"},"startTime":1750440361799,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":408,"timestamp":1567819722429,"id":172,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CPortfolio.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!","layer":"rsc"},"startTime":1750440361800,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":839,"timestamp":1567819722862,"id":173,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!","layer":"ssr"},"startTime":1750440361800,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":492,"timestamp":1567819723734,"id":174,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!","layer":"rsc"},"startTime":1750440361801,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":377,"timestamp":1567819724249,"id":175,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!","layer":"ssr"},"startTime":1750440361802,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":358,"timestamp":1567819724647,"id":176,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!","layer":"rsc"},"startTime":1750440361802,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":350,"timestamp":1567819725026,"id":177,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!","layer":"ssr"},"startTime":1750440361802,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":352,"timestamp":1567819725396,"id":178,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!","layer":"rsc"},"startTime":1750440361803,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":333,"timestamp":1567819725767,"id":179,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!","layer":"ssr"},"startTime":1750440361803,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":330,"timestamp":1567819726121,"id":180,"parentId":17,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!","layer":"rsc"},"startTime":1750440361804,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":135049,"timestamp":1567819791384,"id":182,"parentId":179,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Contact.tsx","layer":"ssr"},"startTime":1750440361869,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":140839,"timestamp":1567819788273,"id":181,"parentId":169,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\error.tsx","layer":"ssr"},"startTime":1750440361866,"traceId":"6c3b02f8e23377ed"},{"name":"make","duration":4416385,"timestamp":1567816642849,"id":19,"parentId":17,"tags":{},"startTime":1750440358720,"traceId":"6c3b02f8e23377ed"},{"name":"get-entries","duration":14909,"timestamp":1567821062606,"id":184,"parentId":183,"tags":{},"startTime":1750440363140,"traceId":"6c3b02f8e23377ed"},{"name":"node-file-trace-plugin","duration":330295,"timestamp":1567821106174,"id":185,"parentId":183,"tags":{"traceEntryCount":"26"},"startTime":1750440363184,"traceId":"6c3b02f8e23377ed"},{"name":"collect-traced-files","duration":2083,"timestamp":1567821436495,"id":186,"parentId":183,"tags":{},"startTime":1750440363514,"traceId":"6c3b02f8e23377ed"},{"name":"finish-modules","duration":376557,"timestamp":1567821062035,"id":183,"parentId":18,"tags":{},"startTime":1750440363139,"traceId":"6c3b02f8e23377ed"},{"name":"chunk-graph","duration":90562,"timestamp":1567821570443,"id":188,"parentId":187,"tags":{},"startTime":1750440363648,"traceId":"6c3b02f8e23377ed"},{"name":"optimize-modules","duration":57,"timestamp":1567821661246,"id":190,"parentId":187,"tags":{},"startTime":1750440363739,"traceId":"6c3b02f8e23377ed"},{"name":"optimize-chunks","duration":112138,"timestamp":1567821661480,"id":191,"parentId":187,"tags":{},"startTime":1750440363739,"traceId":"6c3b02f8e23377ed"},{"name":"optimize-tree","duration":352,"timestamp":1567821773851,"id":192,"parentId":187,"tags":{},"startTime":1750440363851,"traceId":"6c3b02f8e23377ed"},{"name":"optimize-chunk-modules","duration":86040,"timestamp":1567821774453,"id":193,"parentId":187,"tags":{},"startTime":1750440363852,"traceId":"6c3b02f8e23377ed"},{"name":"optimize","duration":199575,"timestamp":1567821661141,"id":189,"parentId":187,"tags":{},"startTime":1750440363739,"traceId":"6c3b02f8e23377ed"},{"name":"module-hash","duration":83915,"timestamp":1567821940466,"id":194,"parentId":187,"tags":{},"startTime":1750440364018,"traceId":"6c3b02f8e23377ed"},{"name":"code-generation","duration":1048930,"timestamp":1567822024555,"id":195,"parentId":187,"tags":{},"startTime":1750440364102,"traceId":"6c3b02f8e23377ed"},{"name":"hash","duration":36622,"timestamp":1567823091722,"id":196,"parentId":187,"tags":{},"startTime":1750440365169,"traceId":"6c3b02f8e23377ed"},{"name":"code-generation-jobs","duration":696,"timestamp":1567823128336,"id":197,"parentId":187,"tags":{},"startTime":1750440365206,"traceId":"6c3b02f8e23377ed"},{"name":"module-assets","duration":925,"timestamp":1567823128853,"id":198,"parentId":187,"tags":{},"startTime":1750440365206,"traceId":"6c3b02f8e23377ed"},{"name":"create-chunk-assets","duration":23232,"timestamp":1567823129858,"id":199,"parentId":187,"tags":{},"startTime":1750440365207,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":66410,"timestamp":1567823307231,"id":206,"parentId":200,"tags":{"name":"../pages/_app.js","cache":"HIT"},"startTime":1750440365385,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":51897,"timestamp":1567823321764,"id":215,"parentId":200,"tags":{"name":"../webpack-runtime.js","cache":"HIT"},"startTime":1750440365399,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":51839,"timestamp":1567823321825,"id":216,"parentId":200,"tags":{"name":"447.js","cache":"HIT"},"startTime":1750440365399,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":12940,"timestamp":1567823360729,"id":220,"parentId":200,"tags":{"name":"548.js","cache":"HIT"},"startTime":1750440365438,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":166200,"timestamp":1567823289163,"id":201,"parentId":200,"tags":{"name":"../app/api/contact/route.js","cache":"MISS"},"startTime":1750440365367,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":160380,"timestamp":1567823295108,"id":202,"parentId":200,"tags":{"name":"../app/robots.txt/route.js","cache":"MISS"},"startTime":1750440365373,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":158157,"timestamp":1567823297394,"id":203,"parentId":200,"tags":{"name":"../app/sitemap.xml/route.js","cache":"MISS"},"startTime":1750440365375,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":152367,"timestamp":1567823307494,"id":207,"parentId":200,"tags":{"name":"../app/_not-found/page.js","cache":"MISS"},"startTime":1750440365385,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":166792,"timestamp":1567823299476,"id":204,"parentId":200,"tags":{"name":"../app/favicon.ico/route.js","cache":"MISS"},"startTime":1750440365377,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":160748,"timestamp":1567823310826,"id":209,"parentId":200,"tags":{"name":"../pages/_document.js","cache":"MISS"},"startTime":1750440365388,"traceId":"6c3b02f8e23377ed"}]
[{"name":"minify-js","duration":170818,"timestamp":1567823306316,"id":205,"parentId":200,"tags":{"name":"../pages/_error.js","cache":"MISS"},"startTime":1750440365384,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":170114,"timestamp":1567823308038,"id":208,"parentId":200,"tags":{"name":"../app/page.js","cache":"MISS"},"startTime":1750440365385,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":167315,"timestamp":1567823311183,"id":210,"parentId":200,"tags":{"name":"../app/portfolio/page.js","cache":"MISS"},"startTime":1750440365389,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":168061,"timestamp":1567823312779,"id":211,"parentId":200,"tags":{"name":"../app/blog/page.js","cache":"MISS"},"startTime":1750440365390,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":176779,"timestamp":1567823314543,"id":213,"parentId":200,"tags":{"name":"../app/about/page.js","cache":"MISS"},"startTime":1750440365392,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":177765,"timestamp":1567823313636,"id":212,"parentId":200,"tags":{"name":"../app/services/page.js","cache":"MISS"},"startTime":1750440365391,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":177058,"timestamp":1567823320961,"id":214,"parentId":200,"tags":{"name":"../app/contact/page.js","cache":"MISS"},"startTime":1750440365398,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":272941,"timestamp":1567823360774,"id":221,"parentId":200,"tags":{"name":"163.js","cache":"MISS"},"startTime":1750440365438,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":269861,"timestamp":1567823363929,"id":222,"parentId":200,"tags":{"name":"537.js","cache":"MISS"},"startTime":1750440365441,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":269836,"timestamp":1567823366053,"id":223,"parentId":200,"tags":{"name":"381.js","cache":"MISS"},"startTime":1750440365443,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":338120,"timestamp":1567823370539,"id":224,"parentId":200,"tags":{"name":"556.js","cache":"MISS"},"startTime":1750440365448,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":336088,"timestamp":1567823372661,"id":225,"parentId":200,"tags":{"name":"147.js","cache":"MISS"},"startTime":1750440365450,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":412571,"timestamp":1567823360062,"id":219,"parentId":200,"tags":{"name":"580.js","cache":"MISS"},"startTime":1750440365437,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":432986,"timestamp":1567823357937,"id":218,"parentId":200,"tags":{"name":"156.js","cache":"MISS"},"startTime":1750440365435,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":1108463,"timestamp":1567823321851,"id":217,"parentId":200,"tags":{"name":"755.js","cache":"MISS"},"startTime":1750440365399,"traceId":"6c3b02f8e23377ed"},{"name":"minify-webpack-plugin-optimize","duration":1177425,"timestamp":1567823252921,"id":200,"parentId":17,"tags":{"compilationName":"server","mangle":"true"},"startTime":1750440365330,"traceId":"6c3b02f8e23377ed"},{"name":"css-minimizer-plugin","duration":389,"timestamp":1567824430800,"id":226,"parentId":17,"tags":{},"startTime":1750440366508,"traceId":"6c3b02f8e23377ed"},{"name":"create-trace-assets","duration":14145,"timestamp":1567824431717,"id":227,"parentId":18,"tags":{},"startTime":1750440366509,"traceId":"6c3b02f8e23377ed"},{"name":"seal","duration":2998561,"timestamp":1567821508751,"id":187,"parentId":17,"tags":{},"startTime":1750440363586,"traceId":"6c3b02f8e23377ed"},{"name":"webpack-compilation","duration":8025767,"timestamp":1567816613209,"id":17,"parentId":14,"tags":{"name":"server"},"startTime":1750440358691,"traceId":"6c3b02f8e23377ed"},{"name":"emit","duration":802757,"timestamp":1567824640240,"id":228,"parentId":14,"tags":{},"startTime":1750440366718,"traceId":"6c3b02f8e23377ed"},{"name":"webpack-close","duration":1912214,"timestamp":1567825450009,"id":229,"parentId":14,"tags":{"name":"server"},"startTime":1750440367527,"traceId":"6c3b02f8e23377ed"},{"name":"webpack-generate-error-stats","duration":7802,"timestamp":1567827362423,"id":230,"parentId":229,"tags":{},"startTime":1750440369440,"traceId":"6c3b02f8e23377ed"},{"name":"run-webpack-compiler","duration":12384348,"timestamp":1567814988671,"id":14,"parentId":13,"tags":{},"startTime":1750440357066,"traceId":"6c3b02f8e23377ed"},{"name":"format-webpack-messages","duration":290,"timestamp":1567827373046,"id":231,"parentId":13,"tags":{},"startTime":1750440369450,"traceId":"6c3b02f8e23377ed"},{"name":"worker-main-server","duration":12386533,"timestamp":1567814987657,"id":13,"parentId":1,"tags":{},"startTime":1750440357065,"traceId":"6c3b02f8e23377ed"},{"name":"create-entrypoints","duration":95633,"timestamp":1567830137620,"id":234,"parentId":232,"tags":{},"startTime":1750440372215,"traceId":"6c3b02f8e23377ed"},{"name":"generate-webpack-config","duration":1134538,"timestamp":1567830233689,"id":235,"parentId":233,"tags":{},"startTime":1750440372311,"traceId":"6c3b02f8e23377ed"},{"name":"make","duration":1739,"timestamp":1567831817587,"id":237,"parentId":236,"tags":{},"startTime":1750440373894,"traceId":"6c3b02f8e23377ed"},{"name":"chunk-graph","duration":2560,"timestamp":1567831829194,"id":239,"parentId":238,"tags":{},"startTime":1750440373906,"traceId":"6c3b02f8e23377ed"},{"name":"optimize-modules","duration":106,"timestamp":1567831832176,"id":241,"parentId":238,"tags":{},"startTime":1750440373909,"traceId":"6c3b02f8e23377ed"},{"name":"optimize-chunks","duration":2974,"timestamp":1567831832578,"id":242,"parentId":238,"tags":{},"startTime":1750440373909,"traceId":"6c3b02f8e23377ed"},{"name":"optimize-tree","duration":364,"timestamp":1567831835764,"id":243,"parentId":238,"tags":{},"startTime":1750440373913,"traceId":"6c3b02f8e23377ed"},{"name":"optimize-chunk-modules","duration":1461,"timestamp":1567831836749,"id":244,"parentId":238,"tags":{},"startTime":1750440373914,"traceId":"6c3b02f8e23377ed"},{"name":"optimize","duration":6465,"timestamp":1567831832001,"id":240,"parentId":238,"tags":{},"startTime":1750440373909,"traceId":"6c3b02f8e23377ed"},{"name":"module-hash","duration":261,"timestamp":1567831840908,"id":245,"parentId":238,"tags":{},"startTime":1750440373918,"traceId":"6c3b02f8e23377ed"},{"name":"code-generation","duration":746,"timestamp":1567831841281,"id":246,"parentId":238,"tags":{},"startTime":1750440373918,"traceId":"6c3b02f8e23377ed"},{"name":"hash","duration":4356,"timestamp":1567831848714,"id":247,"parentId":238,"tags":{},"startTime":1750440373926,"traceId":"6c3b02f8e23377ed"},{"name":"code-generation-jobs","duration":715,"timestamp":1567831853052,"id":248,"parentId":238,"tags":{},"startTime":1750440373930,"traceId":"6c3b02f8e23377ed"},{"name":"module-assets","duration":263,"timestamp":1567831853664,"id":249,"parentId":238,"tags":{},"startTime":1750440373931,"traceId":"6c3b02f8e23377ed"},{"name":"create-chunk-assets","duration":581,"timestamp":1567831853964,"id":250,"parentId":238,"tags":{},"startTime":1750440373931,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":686,"timestamp":1567831902075,"id":252,"parentId":251,"tags":{"name":"interception-route-rewrite-manifest.js","cache":"HIT"},"startTime":1750440373979,"traceId":"6c3b02f8e23377ed"},{"name":"minify-webpack-plugin-optimize","duration":14399,"timestamp":1567831888396,"id":251,"parentId":236,"tags":{"compilationName":"edge-server","mangle":"true"},"startTime":1750440373965,"traceId":"6c3b02f8e23377ed"},{"name":"css-minimizer-plugin","duration":327,"timestamp":1567831903031,"id":253,"parentId":236,"tags":{},"startTime":1750440373980,"traceId":"6c3b02f8e23377ed"},{"name":"seal","duration":91589,"timestamp":1567831824855,"id":238,"parentId":236,"tags":{},"startTime":1750440373902,"traceId":"6c3b02f8e23377ed"},{"name":"webpack-compilation","duration":125503,"timestamp":1567831791916,"id":236,"parentId":233,"tags":{"name":"edge-server"},"startTime":1750440373869,"traceId":"6c3b02f8e23377ed"},{"name":"emit","duration":10041,"timestamp":1567831918615,"id":254,"parentId":233,"tags":{},"startTime":1750440373996,"traceId":"6c3b02f8e23377ed"},{"name":"webpack-close","duration":1911,"timestamp":1567831931342,"id":255,"parentId":233,"tags":{"name":"edge-server"},"startTime":1750440374008,"traceId":"6c3b02f8e23377ed"},{"name":"webpack-generate-error-stats","duration":7368,"timestamp":1567831933386,"id":256,"parentId":255,"tags":{},"startTime":1750440374010,"traceId":"6c3b02f8e23377ed"},{"name":"run-webpack-compiler","duration":1803535,"timestamp":1567830137583,"id":233,"parentId":232,"tags":{},"startTime":1750440372214,"traceId":"6c3b02f8e23377ed"},{"name":"format-webpack-messages","duration":181,"timestamp":1567831941133,"id":257,"parentId":232,"tags":{},"startTime":1750440374018,"traceId":"6c3b02f8e23377ed"},{"name":"worker-main-edge-server","duration":1805320,"timestamp":1567830136282,"id":232,"parentId":1,"tags":{},"startTime":1750440372213,"traceId":"6c3b02f8e23377ed"},{"name":"create-entrypoints","duration":79662,"timestamp":1567834337965,"id":260,"parentId":258,"tags":{},"startTime":1750440376415,"traceId":"6c3b02f8e23377ed"},{"name":"generate-webpack-config","duration":1464018,"timestamp":1567834417988,"id":261,"parentId":259,"tags":{},"startTime":1750440376495,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":24248,"timestamp":1567836893366,"id":284,"parentId":270,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?server=false!","layer":"app-pages-browser"},"startTime":1750440378970,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":11813,"timestamp":1567836917714,"id":285,"parentId":273,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCard.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22ServiceCard%22%2C%22PortfolioCard%22%2C%22TestimonialCard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CInput.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22Textarea%22%2C%22Select%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CModal.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!","layer":"app-pages-browser"},"startTime":1750440378995,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":7069,"timestamp":1567836929656,"id":286,"parentId":276,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!","layer":"app-pages-browser"},"startTime":1750440379007,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":2521,"timestamp":1567836937149,"id":287,"parentId":279,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CPortfolio.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!","layer":"app-pages-browser"},"startTime":1750440379014,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":1387,"timestamp":1567836939748,"id":288,"parentId":280,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=false!","layer":"app-pages-browser"},"startTime":1750440379017,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":1213,"timestamp":1567836941206,"id":289,"parentId":281,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!","layer":"app-pages-browser"},"startTime":1750440379018,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":1489,"timestamp":1567836942487,"id":290,"parentId":282,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!","layer":"app-pages-browser"},"startTime":1750440379019,"traceId":"6c3b02f8e23377ed"},{"name":"build-module","duration":1180,"timestamp":1567836944156,"id":291,"parentId":283,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!","layer":"app-pages-browser"},"startTime":1750440379021,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":663247,"timestamp":1567836283916,"id":270,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":663270,"timestamp":1567836283944,"id":271,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":665962,"timestamp":1567836283965,"id":272,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":665566,"timestamp":1567836284381,"id":274,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":665526,"timestamp":1567836284427,"id":277,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":1156227,"timestamp":1567836283602,"id":267,"parentId":263,"tags":{"request":"next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"},"startTime":1750440378360,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":1166585,"timestamp":1567836283870,"id":269,"parentId":263,"tags":{"request":"next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":1273830,"timestamp":1567836283808,"id":268,"parentId":263,"tags":{"request":"E:\\Augment Code Testing\\tera-works-portfolio\\node_modules\\next\\dist\\client\\router.js"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":1363848,"timestamp":1567836283537,"id":266,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750440378360,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":1371614,"timestamp":1567836283439,"id":265,"parentId":263,"tags":{"request":"./node_modules/next/dist/client/app-next.js"},"startTime":1750440378360,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":1373891,"timestamp":1567836284462,"id":280,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":1376025,"timestamp":1567836284237,"id":273,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CButton.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCard.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22ServiceCard%22%2C%22PortfolioCard%22%2C%22TestimonialCard%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CInput.tsx%22%2C%22ids%22%3A%5B%22default%22%2C%22Textarea%22%2C%22Select%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CModal.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":72575,"timestamp":1567837621564,"id":292,"parentId":286,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\error.tsx","layer":"app-pages-browser"},"startTime":1750440379698,"traceId":"6c3b02f8e23377ed"},{"name":"build-module-tsx","duration":89913,"timestamp":1567837706313,"id":293,"parentId":262,"tags":{"name":"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Contact.tsx","layer":"app-pages-browser"},"startTime":1750440379783,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":1545058,"timestamp":1567836279534,"id":264,"parentId":263,"tags":{"request":"./node_modules/next/dist/client/next.js"},"startTime":1750440378356,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":1740630,"timestamp":1567836284499,"id":283,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":1909134,"timestamp":1567836284417,"id":276,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2050964,"timestamp":1567836284479,"id":281,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2050995,"timestamp":1567836284489,"id":282,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2051097,"timestamp":1567836284401,"id":275,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2051066,"timestamp":1567836284439,"id":278,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CAbout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CContact.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CPortfolio.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CServices.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CTestimonials.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"add-entry","duration":2051063,"timestamp":1567836284448,"id":279,"parentId":263,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5CAugment%20Code%20Testing%5C%5Ctera-works-portfolio%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CPortfolio.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"},"startTime":1750440378361,"traceId":"6c3b02f8e23377ed"},{"name":"make","duration":2057732,"timestamp":1567836278249,"id":263,"parentId":262,"tags":{},"startTime":1750440378355,"traceId":"6c3b02f8e23377ed"},{"name":"chunk-graph","duration":105612,"timestamp":1567838490598,"id":295,"parentId":294,"tags":{},"startTime":1750440380567,"traceId":"6c3b02f8e23377ed"},{"name":"optimize-modules","duration":69,"timestamp":1567838596482,"id":297,"parentId":294,"tags":{},"startTime":1750440380673,"traceId":"6c3b02f8e23377ed"},{"name":"optimize-chunks","duration":115733,"timestamp":1567838629816,"id":299,"parentId":294,"tags":{},"startTime":1750440380707,"traceId":"6c3b02f8e23377ed"},{"name":"optimize-tree","duration":761,"timestamp":1567838745733,"id":300,"parentId":294,"tags":{},"startTime":1750440380823,"traceId":"6c3b02f8e23377ed"},{"name":"optimize-chunk-modules","duration":96176,"timestamp":1567838746774,"id":301,"parentId":294,"tags":{},"startTime":1750440380824,"traceId":"6c3b02f8e23377ed"},{"name":"optimize","duration":246863,"timestamp":1567838596351,"id":296,"parentId":294,"tags":{},"startTime":1750440380673,"traceId":"6c3b02f8e23377ed"},{"name":"module-hash","duration":75362,"timestamp":1567838904954,"id":302,"parentId":294,"tags":{},"startTime":1750440380982,"traceId":"6c3b02f8e23377ed"},{"name":"code-generation","duration":964721,"timestamp":1567838980468,"id":303,"parentId":294,"tags":{},"startTime":1750440381057,"traceId":"6c3b02f8e23377ed"},{"name":"hash","duration":34518,"timestamp":1567839962716,"id":304,"parentId":294,"tags":{},"startTime":1750440382040,"traceId":"6c3b02f8e23377ed"},{"name":"code-generation-jobs","duration":548,"timestamp":1567839997230,"id":305,"parentId":294,"tags":{},"startTime":1750440382074,"traceId":"6c3b02f8e23377ed"},{"name":"module-assets","duration":1225,"timestamp":1567839997594,"id":306,"parentId":294,"tags":{},"startTime":1750440382074,"traceId":"6c3b02f8e23377ed"},{"name":"create-chunk-assets","duration":27046,"timestamp":1567839998874,"id":307,"parentId":294,"tags":{},"startTime":1750440382076,"traceId":"6c3b02f8e23377ed"},{"name":"NextJsBuildManifest-generateClientManifest","duration":10660,"timestamp":1567840033526,"id":309,"parentId":262,"tags":{},"startTime":1750440382110,"traceId":"6c3b02f8e23377ed"},{"name":"NextJsBuildManifest-createassets","duration":13710,"timestamp":1567840030517,"id":308,"parentId":262,"tags":{},"startTime":1750440382107,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":121337,"timestamp":1567840199252,"id":311,"parentId":310,"tags":{"name":"static/chunks/main-829d43144a777921.js","cache":"HIT"},"startTime":1750440382276,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":120945,"timestamp":1567840199661,"id":312,"parentId":310,"tags":{"name":"static/chunks/main-app-0fb3df70f88f0f71.js","cache":"HIT"},"startTime":1750440382277,"traceId":"6c3b02f8e23377ed"}]
[{"name":"minify-js","duration":120872,"timestamp":1567840199737,"id":313,"parentId":310,"tags":{"name":"static/chunks/pages/_app-92f2aae776f86b9c.js","cache":"HIT"},"startTime":1750440382277,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":120817,"timestamp":1567840199794,"id":314,"parentId":310,"tags":{"name":"static/chunks/pages/_error-71d2b6a7b832d02a.js","cache":"HIT"},"startTime":1750440382277,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":87313,"timestamp":1567840233304,"id":329,"parentId":310,"tags":{"name":"static/chunks/webpack-0d51f671013f410e.js","cache":"HIT"},"startTime":1750440382310,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":87238,"timestamp":1567840233382,"id":330,"parentId":310,"tags":{"name":"static/chunks/341.bcfcae6c1ce57183.js","cache":"HIT"},"startTime":1750440382310,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":87177,"timestamp":1567840233446,"id":331,"parentId":310,"tags":{"name":"static/chunks/472.c1612746d03db5d0.js","cache":"HIT"},"startTime":1750440382310,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":87152,"timestamp":1567840233474,"id":332,"parentId":310,"tags":{"name":"static/chunks/framework-f593a28cde54158e.js","cache":"HIT"},"startTime":1750440382310,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":87122,"timestamp":1567840233506,"id":333,"parentId":310,"tags":{"name":"static/chunks/c15bf2b0-7f3f4bd25724833d.js","cache":"HIT"},"startTime":1750440382310,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":88269,"timestamp":1567840233670,"id":334,"parentId":310,"tags":{"name":"static/chunks/4bd1b696-299743f5624cdabe.js","cache":"HIT"},"startTime":1750440382311,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":5168,"timestamp":1567840316820,"id":344,"parentId":310,"tags":{"name":"server/middleware-react-loadable-manifest.js","cache":"HIT"},"startTime":1750440382394,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":1446,"timestamp":1567840320546,"id":348,"parentId":310,"tags":{"name":"server/next-font-manifest.js","cache":"HIT"},"startTime":1750440382397,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":205720,"timestamp":1567840208472,"id":317,"parentId":310,"tags":{"name":"static/chunks/app/sitemap.xml/route-586f32987751ffaf.js","cache":"MISS"},"startTime":1750440382285,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":214529,"timestamp":1567840199823,"id":315,"parentId":310,"tags":{"name":"static/chunks/app/api/contact/route-2a66e30153936ff9.js","cache":"MISS"},"startTime":1750440382277,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":203659,"timestamp":1567840210754,"id":319,"parentId":310,"tags":{"name":"static/chunks/app/_not-found/page-7317872c187008d3.js","cache":"MISS"},"startTime":1750440382288,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":205553,"timestamp":1567840208907,"id":318,"parentId":310,"tags":{"name":"static/chunks/app/not-found-1cf35cbf6d9fe570.js","cache":"MISS"},"startTime":1750440382286,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":189834,"timestamp":1567840224670,"id":322,"parentId":310,"tags":{"name":"static/chunks/app/loading-cc9e1f53d748b4ef.js","cache":"MISS"},"startTime":1750440382302,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":190564,"timestamp":1567840223994,"id":321,"parentId":310,"tags":{"name":"static/chunks/app/error-14f874a2fb6fda21.js","cache":"MISS"},"startTime":1750440382301,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":206815,"timestamp":1567840207788,"id":316,"parentId":310,"tags":{"name":"static/chunks/app/robots.txt/route-18acd34893641fd0.js","cache":"MISS"},"startTime":1750440382285,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":183306,"timestamp":1567840231374,"id":325,"parentId":310,"tags":{"name":"static/chunks/app/blog/page-0290db192af197ff.js","cache":"MISS"},"startTime":1750440382308,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":184358,"timestamp":1567840230528,"id":324,"parentId":310,"tags":{"name":"static/chunks/app/portfolio/page-79a39464346b3b9a.js","cache":"MISS"},"startTime":1750440382307,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":203601,"timestamp":1567840211348,"id":320,"parentId":310,"tags":{"name":"static/chunks/app/layout-a849fd35954b3501.js","cache":"MISS"},"startTime":1750440382288,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":182019,"timestamp":1567840232975,"id":328,"parentId":310,"tags":{"name":"static/chunks/app/contact/page-7ba52afb1d6adfbd.js","cache":"MISS"},"startTime":1750440382310,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":183179,"timestamp":1567840231858,"id":326,"parentId":310,"tags":{"name":"static/chunks/app/services/page-ecb792757df8b326.js","cache":"MISS"},"startTime":1750440382309,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":188422,"timestamp":1567840226658,"id":323,"parentId":310,"tags":{"name":"static/chunks/app/page-db9eb0c0022e2488.js","cache":"MISS"},"startTime":1750440382304,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":182639,"timestamp":1567840232483,"id":327,"parentId":310,"tags":{"name":"static/chunks/app/about/page-7dbfa5e40da25aa6.js","cache":"MISS"},"startTime":1750440382309,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":222078,"timestamp":1567840233728,"id":335,"parentId":310,"tags":{"name":"static/chunks/874-e909718850e7282e.js","cache":"MISS"},"startTime":1750440382311,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":185809,"timestamp":1567840305278,"id":339,"parentId":310,"tags":{"name":"static/chunks/644-94627d9bfe8ff0ba.js","cache":"MISS"},"startTime":1750440382382,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":199445,"timestamp":1567840310348,"id":340,"parentId":310,"tags":{"name":"static/chunks/439-5b7833356b76e827.js","cache":"MISS"},"startTime":1750440382387,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":283277,"timestamp":1567840313285,"id":341,"parentId":310,"tags":{"name":"static/chunks/275-ae70c80c30ca6149.js","cache":"MISS"},"startTime":1750440382390,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":381482,"timestamp":1567840291871,"id":337,"parentId":310,"tags":{"name":"static/chunks/375-1509d2da38fef02e.js","cache":"MISS"},"startTime":1750440382369,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":357703,"timestamp":1567840315742,"id":343,"parentId":310,"tags":{"name":"static/chunks/417-2fde19106466096d.js","cache":"MISS"},"startTime":1750440382393,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":359065,"timestamp":1567840314487,"id":342,"parentId":310,"tags":{"name":"static/chunks/776-0d7fe9b36ccca2ca.js","cache":"MISS"},"startTime":1750440382391,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":356638,"timestamp":1567840316965,"id":345,"parentId":310,"tags":{"name":"static/-ay7Vpv8Pnn20wAKQrmSL/_ssgManifest.js","cache":"MISS"},"startTime":1750440382394,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":353749,"timestamp":1567840319885,"id":346,"parentId":310,"tags":{"name":"server/middleware-build-manifest.js","cache":"MISS"},"startTime":1750440382397,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":353372,"timestamp":1567840320292,"id":347,"parentId":310,"tags":{"name":"static/-ay7Vpv8Pnn20wAKQrmSL/_buildManifest.js","cache":"MISS"},"startTime":1750440382397,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":670507,"timestamp":1567840239427,"id":336,"parentId":310,"tags":{"name":"static/chunks/44-3a6bfe27560d793c.js","cache":"MISS"},"startTime":1750440382316,"traceId":"6c3b02f8e23377ed"},{"name":"minify-js","duration":689809,"timestamp":1567840293751,"id":338,"parentId":310,"tags":{"name":"static/chunks/684-2db1d94d3e67dc70.js","cache":"MISS"},"startTime":1750440382371,"traceId":"6c3b02f8e23377ed"},{"name":"minify-webpack-plugin-optimize","duration":933678,"timestamp":1567840049907,"id":310,"parentId":262,"tags":{"compilationName":"client","mangle":"true"},"startTime":1750440382127,"traceId":"6c3b02f8e23377ed"},{"name":"minify-css","duration":3072,"timestamp":1567840984428,"id":350,"parentId":349,"tags":{"file":"static/css/9e60bb2683b082a4.css","cache":"HIT"},"startTime":1750440383061,"traceId":"6c3b02f8e23377ed"},{"name":"css-minimizer-plugin","duration":3558,"timestamp":1567840983958,"id":349,"parentId":262,"tags":{},"startTime":1750440383061,"traceId":"6c3b02f8e23377ed"},{"name":"seal","duration":2620412,"timestamp":1567838412604,"id":294,"parentId":262,"tags":{},"startTime":1750440380489,"traceId":"6c3b02f8e23377ed"},{"name":"webpack-compilation","duration":4773706,"timestamp":1567836260184,"id":262,"parentId":259,"tags":{"name":"client"},"startTime":1750440378337,"traceId":"6c3b02f8e23377ed"},{"name":"emit","duration":1274154,"timestamp":1567841037159,"id":351,"parentId":259,"tags":{},"startTime":1750440383114,"traceId":"6c3b02f8e23377ed"},{"name":"webpack-close","duration":1528848,"timestamp":1567842314801,"id":352,"parentId":259,"tags":{"name":"client"},"startTime":1750440384392,"traceId":"6c3b02f8e23377ed"},{"name":"webpack-generate-error-stats","duration":5912,"timestamp":1567843843782,"id":353,"parentId":352,"tags":{},"startTime":1750440385921,"traceId":"6c3b02f8e23377ed"},{"name":"run-webpack-compiler","duration":9512303,"timestamp":1567834337937,"id":259,"parentId":258,"tags":{},"startTime":1750440376415,"traceId":"6c3b02f8e23377ed"},{"name":"format-webpack-messages","duration":174,"timestamp":1567843850251,"id":354,"parentId":258,"tags":{},"startTime":1750440385927,"traceId":"6c3b02f8e23377ed"},{"name":"worker-main-client","duration":9513823,"timestamp":1567834336919,"id":258,"parentId":1,"tags":{},"startTime":1750440376414,"traceId":"6c3b02f8e23377ed"},{"name":"verify-and-lint","duration":13932394,"timestamp":1567844316340,"id":358,"parentId":1,"tags":{},"startTime":1750440386393,"traceId":"6c3b02f8e23377ed"},{"name":"verify-typescript-setup","duration":18429031,"timestamp":1567844218444,"id":357,"parentId":1,"tags":{},"startTime":1750440386295,"traceId":"6c3b02f8e23377ed"},{"name":"check-static-error-page","duration":11316,"timestamp":1567862802435,"id":361,"parentId":360,"tags":{},"startTime":1750440404879,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":5199,"timestamp":1567862943182,"id":362,"parentId":360,"tags":{"page":"/_app"},"startTime":1750440405020,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":2661,"timestamp":1567862945761,"id":364,"parentId":360,"tags":{"page":"/_document"},"startTime":1750440405023,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":3591,"timestamp":1567862945557,"id":363,"parentId":360,"tags":{"page":"/_error"},"startTime":1750440405023,"traceId":"6c3b02f8e23377ed"},{"name":"is-page-static","duration":2279599,"timestamp":1567862983892,"id":377,"parentId":367,"tags":{},"startTime":1750440405061,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":2318270,"timestamp":1567862946392,"id":367,"parentId":360,"tags":{"page":"/api/contact"},"startTime":1750440405023,"traceId":"6c3b02f8e23377ed"},{"name":"is-page-static","duration":2278822,"timestamp":1567862986309,"id":378,"parentId":373,"tags":{},"startTime":1750440405063,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":2318133,"timestamp":1567862947104,"id":373,"parentId":360,"tags":{"page":"/robots.txt"},"startTime":1750440405024,"traceId":"6c3b02f8e23377ed"},{"name":"is-page-static","duration":2281277,"timestamp":1567862986466,"id":379,"parentId":374,"tags":{},"startTime":1750440405064,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":2320632,"timestamp":1567862947170,"id":374,"parentId":360,"tags":{"page":"/sitemap.xml"},"startTime":1750440405024,"traceId":"6c3b02f8e23377ed"},{"name":"is-page-static","duration":2413251,"timestamp":1567862950901,"id":376,"parentId":375,"tags":{},"startTime":1750440405028,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":2416975,"timestamp":1567862947229,"id":375,"parentId":360,"tags":{"page":"/favicon.ico"},"startTime":1750440405024,"traceId":"6c3b02f8e23377ed"},{"name":"is-page-static","duration":2077347,"timestamp":1567863287004,"id":380,"parentId":365,"tags":{},"startTime":1750440405364,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":2418435,"timestamp":1567862945946,"id":365,"parentId":360,"tags":{"page":"/_not-found"},"startTime":1750440405023,"traceId":"6c3b02f8e23377ed"},{"name":"is-page-static","duration":2094123,"timestamp":1567863287134,"id":383,"parentId":366,"tags":{},"startTime":1750440405364,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":2435045,"timestamp":1567862946265,"id":366,"parentId":360,"tags":{"page":"/about"},"startTime":1750440405023,"traceId":"6c3b02f8e23377ed"},{"name":"is-page-static","duration":2103892,"timestamp":1567863287164,"id":384,"parentId":371,"tags":{},"startTime":1750440405364,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":2444172,"timestamp":1567862946928,"id":371,"parentId":360,"tags":{"page":"/portfolio"},"startTime":1750440405024,"traceId":"6c3b02f8e23377ed"},{"name":"is-page-static","duration":1722173,"timestamp":1567863679041,"id":385,"parentId":369,"tags":{},"startTime":1750440405756,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":2454685,"timestamp":1567862946570,"id":369,"parentId":360,"tags":{"page":"/contact"},"startTime":1750440405024,"traceId":"6c3b02f8e23377ed"},{"name":"is-page-static","duration":1561498,"timestamp":1567863851273,"id":386,"parentId":372,"tags":{},"startTime":1750440405928,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":2465806,"timestamp":1567862947027,"id":372,"parentId":360,"tags":{"page":"/services"},"startTime":1750440405024,"traceId":"6c3b02f8e23377ed"},{"name":"is-page-static","duration":2125881,"timestamp":1567863287066,"id":381,"parentId":370,"tags":{},"startTime":1750440405364,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":2466305,"timestamp":1567862946659,"id":370,"parentId":360,"tags":{"page":"/"},"startTime":1750440405024,"traceId":"6c3b02f8e23377ed"},{"name":"is-page-static","duration":2162539,"timestamp":1567863287101,"id":382,"parentId":368,"tags":{},"startTime":1750440405364,"traceId":"6c3b02f8e23377ed"},{"name":"check-page","duration":2503209,"timestamp":1567862946483,"id":368,"parentId":360,"tags":{"page":"/blog"},"startTime":1750440405024,"traceId":"6c3b02f8e23377ed"},{"name":"static-check","duration":2648553,"timestamp":1567862801247,"id":360,"parentId":1,"tags":{},"startTime":1750440404878,"traceId":"6c3b02f8e23377ed"},{"name":"generate-required-server-files","duration":905,"timestamp":1567865450843,"id":388,"parentId":1,"tags":{},"startTime":1750440407528,"traceId":"6c3b02f8e23377ed"},{"name":"write-routes-manifest","duration":41394,"timestamp":1567865481438,"id":390,"parentId":1,"tags":{},"startTime":1750440407558,"traceId":"6c3b02f8e23377ed"},{"name":"load-dotenv","duration":94,"timestamp":1567869756884,"id":393,"parentId":392,"tags":{},"startTime":1750440411834,"traceId":"6c3b02f8e23377ed"},{"name":"run-export-path-map","duration":1201,"timestamp":1567870024919,"id":394,"parentId":392,"tags":{},"startTime":1750440412102,"traceId":"6c3b02f8e23377ed"},{"name":"next-export","duration":4709720,"timestamp":1567869754941,"id":392,"parentId":1,"tags":{},"startTime":1750440411832,"traceId":"6c3b02f8e23377ed"},{"name":"move-exported-app-not-found-","duration":24183,"timestamp":1567874472919,"id":395,"parentId":391,"tags":{},"startTime":1750440416550,"traceId":"6c3b02f8e23377ed"},{"name":"move-exported-page","duration":125678,"timestamp":1567874497397,"id":396,"parentId":391,"tags":{},"startTime":1750440416574,"traceId":"6c3b02f8e23377ed"},{"name":"static-generation","duration":6057848,"timestamp":1567869733833,"id":391,"parentId":1,"tags":{},"startTime":1750440411811,"traceId":"6c3b02f8e23377ed"},{"name":"write-routes-manifest","duration":116926,"timestamp":1567875791716,"id":397,"parentId":1,"tags":{},"startTime":1750440417869,"traceId":"6c3b02f8e23377ed"},{"name":"node-file-trace-build","duration":35770503,"timestamp":1567865457470,"id":389,"parentId":1,"tags":{"isTurbotrace":"false"},"startTime":1750440407535,"traceId":"6c3b02f8e23377ed"},{"name":"apply-include-excludes","duration":7420,"timestamp":1567901228024,"id":398,"parentId":1,"tags":{},"startTime":1750440443310,"traceId":"6c3b02f8e23377ed"},{"name":"print-tree-view","duration":21061,"timestamp":1567901237324,"id":399,"parentId":1,"tags":{},"startTime":1750440443314,"traceId":"6c3b02f8e23377ed"},{"name":"telemetry-flush","duration":151,"timestamp":1567901258415,"id":400,"parentId":1,"tags":{},"startTime":1750440443336,"traceId":"6c3b02f8e23377ed"},{"name":"next-build","duration":90533555,"timestamp":1567810725038,"id":1,"tags":{"buildMode":"default","isTurboBuild":"false","version":"15.3.4","has-custom-webpack-config":"false","use-build-worker":"true"},"startTime":1750440352802,"traceId":"6c3b02f8e23377ed"}]
