"use strict";(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2567:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>x,tree:()=>p});var o=t(5239),n=t(8088),s=t(8170),i=t.n(s),a=t(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let p={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1102)),"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,6812)),"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,7393)),"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,1102)),"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=[],u={require:t,loadChunk:()=>Promise.resolve()},x=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{e.exports=require("path")},9121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[447,755,163],()=>t(2567));module.exports=o})();