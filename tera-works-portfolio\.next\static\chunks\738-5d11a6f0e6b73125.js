"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[738],{2932:(e,a,r)=>{r.d(a,{ii:()=>s,lj:()=>l,r_:()=>i,ss:()=>t});let t=[{label:"Home",href:"/"},{label:"About",href:"/about"},{label:"Services",href:"/services",children:[{label:"Web Development",href:"/services/web-development"},{label:"Meta Advertising",href:"/services/meta-advertising"},{label:"Booking Systems",href:"/services/booking-systems"}]},{label:"Portfolio",href:"/portfolio"},{label:"Blog",href:"/blog"},{label:"Contact",href:"/contact"}],s={services:[{label:"Custom Website Development",href:"/services/web-development"},{label:"E-commerce Solutions",href:"/services/ecommerce"},{label:"Booking Systems",href:"/services/booking-systems"},{label:"Meta Advertising",href:"/services/meta-advertising"},{label:"Website Maintenance",href:"/services/maintenance"}],company:[{label:"About Us",href:"/about"},{label:"Our Process",href:"/process"},{label:"Case Studies",href:"/portfolio"},{label:"Testimonials",href:"/testimonials"},{label:"Careers",href:"/careers"}],resources:[{label:"Blog",href:"/blog"},{label:"FAQ",href:"/faq"},{label:"Support",href:"/support"},{label:"Documentation",href:"/docs"},{label:"Contact",href:"/contact"}],legal:[{label:"Privacy Policy",href:"/privacy"},{label:"Terms of Service",href:"/terms"},{label:"Cookie Policy",href:"/cookies"},{label:"GDPR",href:"/gdpr"}]},l=[{platform:"LinkedIn",url:"https://linkedin.com/company/tera-works",icon:"linkedin",label:"Follow us on LinkedIn"},{platform:"Twitter",url:"https://twitter.com/teraworks",icon:"twitter",label:"Follow us on Twitter"},{platform:"Facebook",url:"https://facebook.com/teraworks",icon:"facebook",label:"Like us on Facebook"},{platform:"Instagram",url:"https://instagram.com/teraworks",icon:"instagram",label:"Follow us on Instagram"},{platform:"GitHub",url:"https://github.com/teraworks",icon:"github",label:"View our code on GitHub"},{platform:"Email",url:"mailto:<EMAIL>",icon:"email",label:"Send us an email"}],i={email:"<EMAIL>",phone:"+****************",address:{street:"123 Business Ave",city:"Tech City",state:"TC",zip:"12345",country:"United States"},hours:{weekdays:"9:00 AM - 6:00 PM",weekends:"10:00 AM - 4:00 PM",timezone:"EST"}}},4910:(e,a,r)=>{r.d(a,{CV:()=>h,Rf:()=>l,Tz:()=>c,VM:()=>u,Xy:()=>d,Ym:()=>n,bK:()=>s,fP:()=>o,pP:()=>m,ru:()=>i,tE:()=>t});let t={initial:{opacity:0,y:60},animate:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}},exit:{opacity:0,y:-60}},s={initial:{},animate:{transition:{staggerChildren:.1,delayChildren:.3}}},l={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.5,ease:"easeOut"}}},i={idle:{scale:1},hover:{scale:1.05,transition:{duration:.2,ease:"easeOut"}},tap:{scale:.95,transition:{duration:.1}}},o={idle:{y:0,scale:1,boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"},hover:{y:-8,scale:1.02,boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)",transition:{duration:.3,ease:"easeOut"}}},n={closed:{opacity:0,height:0,transition:{duration:.3,ease:"easeInOut"}},open:{opacity:1,height:"auto",transition:{duration:.3,ease:"easeInOut",staggerChildren:.1,delayChildren:.1}}},c={closed:{opacity:0,x:-20},open:{opacity:1,x:0,transition:{duration:.3,ease:"easeOut"}}},d={focus:{scale:1.02,transition:{duration:.2,ease:"easeOut"}},blur:{scale:1,transition:{duration:.2,ease:"easeOut"}}},m={default:{y:0,scale:1,color:"#6B7280"},active:{y:-24,scale:.8,color:"#3B82F6",transition:{duration:.2,ease:"easeOut"}}},u={hidden:{opacity:0},show:{opacity:1,transition:{staggerChildren:.1}}},h={hidden:{opacity:0,scale:.8},show:{opacity:1,scale:1,transition:{duration:.5,ease:"easeOut"}}}},8055:(e,a,r)=>{r.d(a,{$n:()=>d,pd:()=>y,l6:()=>b,mB:()=>h,_f:()=>p,TM:()=>f});var t=r(5155),s=r(2115),l=r(6874),i=r.n(l),o=r(6408),n=r(9434),c=r(4910);let d=e=>{let{children:a,className:r,variant:s="primary",size:l="md",disabled:d=!1,loading:m=!1,onClick:u,type:h="button",href:p,external:x=!1,...f}=e,b=(0,n.cn)("inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500",secondary:"bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500",outline:"border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500",ghost:"text-primary-600 hover:bg-primary-50 focus:ring-primary-500"}[s],{sm:"px-3 py-2 text-sm rounded-md",md:"px-4 py-2 text-base rounded-lg",lg:"px-6 py-3 text-lg rounded-lg"}[l],r),y=(0,t.jsxs)(t.Fragment,{children:[m&&(0,t.jsx)(()=>(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),{}),a]});return p?x?(0,t.jsx)(o.P.a,{href:p,target:"_blank",rel:"noopener noreferrer",className:b,variants:c.ru,initial:"idle",whileHover:"hover",whileTap:"tap",...f,children:y}):(0,t.jsx)(i(),{href:p,className:b,...f,children:(0,t.jsx)(o.P.span,{variants:c.ru,initial:"idle",whileHover:"hover",whileTap:"tap",className:"flex items-center justify-center w-full h-full",children:y})}):(0,t.jsx)(o.P.button,{type:h,className:b,disabled:d||m,onClick:u,variants:c.ru,initial:"idle",whileHover:d||m?"idle":"hover",whileTap:d||m?"idle":"tap",...f,children:y})};var m=r(6766);let u=e=>{let{children:a,className:r,title:s,description:l,image:d,href:u,hover:h=!0,...p}=e,x=(0,n.cn)("bg-white rounded-lg shadow-md overflow-hidden transition-shadow duration-300",r),f=(0,t.jsxs)(o.P.div,{className:x,variants:h?c.fP:void 0,initial:"idle",whileHover:h?"hover":"idle",...p,children:[d&&(0,t.jsx)("div",{className:"relative h-48 w-full",children:(0,t.jsx)(m.default,{src:d,alt:s||"Card image",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),(0,t.jsxs)("div",{className:"p-6",children:[s&&(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:s}),l&&(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:l}),a]})]});return u?(0,t.jsx)(i(),{href:u,className:"block",children:f}):f},h=e=>{let{title:a,description:r,icon:s,features:l,href:i,className:o}=e;return(0,t.jsxs)(u,{title:a,description:r,href:i,className:(0,n.cn)("h-full",o),children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)("span",{className:"text-4xl mr-3",children:s}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:a})]}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:r}),(0,t.jsx)("ul",{className:"space-y-2",children:l.slice(0,4).map((e,a)=>(0,t.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,t.jsx)("svg",{className:"w-4 h-4 text-green-500 mr-2 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),e]},a))})]})},p=e=>{let{name:a,position:r,company:s,content:l,avatar:i,rating:o,className:c}=e;return(0,t.jsxs)(u,{className:(0,n.cn)("h-full",c),hover:!1,children:[(0,t.jsx)("div",{className:"flex items-center mb-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,a)=>(0,t.jsx)("svg",{className:(0,n.cn)("w-5 h-5",a<o?"text-yellow-400":"text-gray-300"),fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},a))}),(0,t.jsxs)("blockquote",{className:"text-gray-600 mb-4 italic",children:['"',l,'"']}),(0,t.jsxs)("div",{className:"flex items-center",children:[i&&(0,t.jsx)("div",{className:"relative w-12 h-12 mr-4",children:(0,t.jsx)(m.default,{src:i,alt:a,fill:!0,className:"rounded-full object-cover"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-gray-900",children:a}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[r," at ",s]})]})]})]})},x=(0,s.forwardRef)((e,a)=>{let{className:r,label:l,error:i,helperText:d,variant:m="default",type:u="text",id:h,...p}=e,[x,f]=(0,s.useState)(!1),[b,y]=(0,s.useState)(!1),v=h||"input-".concat(Math.random().toString(36).substr(2,9)),g=(0,n.cn)("w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed",i?"border-red-300 focus:ring-red-500":"border-gray-300 focus:border-primary-500","floating"===m&&"pt-6 pb-2",r),w=e=>{var a;f(!0),null==(a=p.onFocus)||a.call(p,e)},j=e=>{var a;f(!1),y(""!==e.target.value),null==(a=p.onBlur)||a.call(p,e)},N=e=>{var a;y(""!==e.target.value),null==(a=p.onChange)||a.call(p,e)};return"floating"===m?(0,t.jsxs)("div",{className:"relative mb-6",children:[(0,t.jsx)(o.P.input,{ref:a,type:u,id:v,className:g,onFocus:w,onBlur:j,onChange:N,variants:c.Xy,whileFocus:"focus",...p}),l&&(0,t.jsx)(o.P.label,{htmlFor:v,variants:c.pP,animate:x||b?"active":"default",className:"absolute left-3 top-2 pointer-events-none text-sm font-medium",children:l}),i&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i}),d&&!i&&(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:d})]}):(0,t.jsxs)("div",{className:"mb-4",children:[l&&(0,t.jsx)("label",{htmlFor:v,className:"block text-sm font-medium text-gray-700 mb-1",children:l}),(0,t.jsx)(o.P.input,{ref:a,type:u,id:v,className:g,onFocus:w,onBlur:j,onChange:N,variants:c.Xy,whileFocus:"focus",...p}),i&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:i}),d&&!i&&(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:d})]})});x.displayName="Input";let f=(0,s.forwardRef)((e,a)=>{let{className:r,label:s,error:l,helperText:i,id:d,...m}=e,u=d||"textarea-".concat(Math.random().toString(36).substr(2,9)),h=(0,n.cn)("w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed resize-vertical min-h-[100px]",l?"border-red-300 focus:ring-red-500":"border-gray-300 focus:border-primary-500",r);return(0,t.jsxs)("div",{className:"mb-4",children:[s&&(0,t.jsx)("label",{htmlFor:u,className:"block text-sm font-medium text-gray-700 mb-1",children:s}),(0,t.jsx)(o.P.textarea,{ref:a,id:u,className:h,variants:c.Xy,whileFocus:"focus",...m}),l&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l}),i&&!l&&(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:i})]})});f.displayName="Textarea";let b=(0,s.forwardRef)((e,a)=>{let{className:r,label:s,error:l,helperText:i,options:d,id:m,...u}=e,h=m||"select-".concat(Math.random().toString(36).substr(2,9)),p=(0,n.cn)("w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed bg-white",l?"border-red-300 focus:ring-red-500":"border-gray-300 focus:border-primary-500",r);return(0,t.jsxs)("div",{className:"mb-4",children:[s&&(0,t.jsx)("label",{htmlFor:h,className:"block text-sm font-medium text-gray-700 mb-1",children:s}),(0,t.jsx)(o.P.select,{ref:a,id:h,className:p,variants:c.Xy,whileFocus:"focus",...u,children:d.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))}),l&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:l}),i&&!l&&(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:i})]})});b.displayName="Select";let y=x},9434:(e,a,r)=>{r.d(a,{cn:()=>s});var t=r(2596);function s(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return(0,t.$)(a)}}}]);