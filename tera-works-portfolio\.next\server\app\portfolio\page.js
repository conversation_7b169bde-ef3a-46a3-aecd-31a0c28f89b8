(()=>{var e={};e.id=281,e.ids=[281],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1200:(e,t,r)=>{Promise.resolve().then(r.bind(r,2059))},2319:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>a});var o=r(7413),s=r(5947);let a={title:"Portfolio",description:"Explore our portfolio of successful web development and Meta advertising projects."};function i(){return(0,o.jsxs)("div",{className:"pt-20",children:[(0,o.jsx)(s.default,{}),(0,o.jsx)("section",{className:"section-padding bg-white",children:(0,o.jsx)("div",{className:"container-custom",children:(0,o.jsxs)("div",{className:"text-center mb-16",children:[(0,o.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Ready to Start Your Success Story?"}),(0,o.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto mb-8",children:"Every project in our portfolio started with a conversation. Let's discuss how we can help you achieve similar results."}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,o.jsx)("a",{href:"/contact",className:"btn-primary",children:"Start Your Project"}),(0,o.jsx)("a",{href:"/services",className:"btn-secondary",children:"View Our Services"})]})]})})})]})}},2438:(e,t,r)=>{"use strict";r.d(t,{WT:()=>s});var o=r(6208);r(6156),o.Ay.config({autoSleep:60,force3D:!1,nullTargetWarn:!1});let s={heroTitle:e=>o.Ay.fromTo(e,{y:100,opacity:0},{y:0,opacity:1,duration:1.2,ease:"power3.out"}),heroSubtitle:(e,t=.3)=>o.Ay.fromTo(e,{y:50,opacity:0},{y:0,opacity:1,duration:.8,delay:t,ease:"power2.out"}),scrollReveal:(e,t={})=>{let r={y:80,opacity:0,duration:.8,stagger:.2,ease:"power2.out",scrollTrigger:{trigger:Array.isArray(e)?e[0]:e,start:"top 80%",toggleActions:"play none none reverse"}};return o.Ay.fromTo(e,{y:r.y,opacity:0},{...r,...t,y:0,opacity:1})},serviceCards:e=>o.Ay.fromTo(e,{y:60,opacity:0,scale:.9},{y:0,opacity:1,scale:1,duration:.8,stagger:.15,ease:"power2.out",scrollTrigger:{trigger:e[0],start:"top 75%"}}),portfolioItems:e=>o.Ay.fromTo(e,{scale:.8,opacity:0},{scale:1,opacity:1,duration:.6,stagger:.1,ease:"power2.out",scrollTrigger:{trigger:e[0],start:"top 70%"}}),textReveal:e=>{let t=e.textContent?.split("")||[];e.innerHTML=t.map(e=>`<span style="display: inline-block;">${" "===e?"&nbsp;":e}</span>`).join("");let r=e.querySelectorAll("span");return o.Ay.fromTo(r,{y:100,opacity:0},{y:0,opacity:1,duration:.05,stagger:.02,ease:"power2.out"})},counter:(e,t,r=2)=>{let s={value:0};return o.Ay.to(s,{value:t,duration:r,ease:"power2.out",onUpdate:()=>{e.textContent=Math.round(s.value).toString()},scrollTrigger:{trigger:e,start:"top 80%",once:!0}})},cardHover:{enter:e=>o.Ay.to(e,{y:-8,scale:1.02,duration:.3,ease:"power2.out"}),leave:e=>o.Ay.to(e,{y:0,scale:1,duration:.3,ease:"power2.out"})},buttonHover:{enter:e=>o.Ay.to(e,{scale:1.05,duration:.2,ease:"power2.out"}),leave:e=>o.Ay.to(e,{scale:1,duration:.2,ease:"power2.out"})},pageTransition:{enter:e=>o.Ay.fromTo(e,{opacity:0,y:20},{opacity:1,y:0,duration:.5,ease:"power2.out"}),exit:e=>o.Ay.to(e,{opacity:0,y:-20,duration:.3,ease:"power2.in"})}}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3056:(e,t,r)=>{Promise.resolve().then(r.bind(r,5947))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9433:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>p});var o=r(5239),s=r(8088),a=r(8170),i=r.n(a),n=r(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let p={children:["",{children:["portfolio",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2319)),"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\portfolio\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,6812)),"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,7393)),"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,1102)),"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\portfolio\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/portfolio/page",pathname:"/portfolio",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,755,156,163,381],()=>r(9433));module.exports=o})();