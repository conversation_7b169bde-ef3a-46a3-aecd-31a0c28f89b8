import { Testimonial, Statistic } from '@/types';

export const testimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON>',
    position: 'Owner',
    company: 'Spice & Herb Restaurant',
    avatar: '/images/testimonials/sarah-johnson.jpg',
    content: 'Working with Tera Works transformed our restaurant business. The website they built not only looks amazing but has increased our online orders by 150%. The booking system is intuitive and our customers love it.',
    rating: 5,
    date: new Date('2024-03-20'),
    projectType: 'Restaurant Website & Booking System'
  },
  {
    id: '2',
    name: '<PERSON>',
    position: 'Founder',
    company: 'Zeynthra Fashion',
    avatar: '/images/testimonials/michael-chen.jpg',
    content: 'The e-commerce platform Tera Works developed exceeded our expectations. Our conversion rate improved by 85% and the user experience is seamless. Their attention to detail and understanding of our brand was exceptional.',
    rating: 5,
    date: new Date('2024-02-10'),
    projectType: 'E-commerce Development'
  },
  {
    id: '3',
    name: '<PERSON><PERSON>',
    position: 'Professional Photographer',
    company: 'Chalakadulanga Photography',
    avatar: '/images/testimonials/priya-patel.jpg',
    content: 'The portfolio website and booking system have revolutionized my photography business. I\'ve seen a 200% increase in booking inquiries and the automated system saves me hours every week. Absolutely fantastic work!',
    rating: 5,
    date: new Date('2023-12-15'),
    projectType: 'Photography Portfolio & Booking System'
  },
  {
    id: '4',
    name: 'David Rodriguez',
    position: 'Marketing Director',
    company: 'Local Restaurant Chain',
    avatar: '/images/testimonials/david-rodriguez.jpg',
    content: 'The Meta advertising campaigns delivered incredible results. We achieved a 4.2x ROAS and saw a 65% increase in foot traffic. The team\'s expertise in targeting and optimization is outstanding.',
    rating: 5,
    date: new Date('2024-03-05'),
    projectType: 'Meta Advertising Campaign'
  },
  {
    id: '5',
    name: 'Emma Thompson',
    position: 'E-commerce Manager',
    company: 'Fashion Brand Co.',
    avatar: '/images/testimonials/emma-thompson.jpg',
    content: 'Our Meta advertising campaigns with Tera Works generated a 5.8x ROAS and increased our customer base by 180%. Their strategic approach and continuous optimization made all the difference.',
    rating: 5,
    date: new Date('2024-01-25'),
    projectType: 'Meta Advertising & Social Media'
  },
  {
    id: '6',
    name: 'James Wilson',
    position: 'CEO',
    company: 'Tech Startup Inc.',
    avatar: '/images/testimonials/james-wilson.jpg',
    content: 'Tera Works delivered a high-performance website that perfectly represents our brand. The development process was smooth, communication was excellent, and the final result exceeded our expectations.',
    rating: 5,
    date: new Date('2023-11-30'),
    projectType: 'Custom Web Development'
  }
];

export const statistics: Statistic[] = [
  {
    id: '1',
    label: 'Projects Completed',
    value: 50,
    suffix: '+',
    description: 'Successful projects delivered',
    icon: '🚀'
  },
  {
    id: '2',
    label: 'Happy Clients',
    value: 45,
    suffix: '+',
    description: 'Satisfied customers worldwide',
    icon: '😊'
  },
  {
    id: '3',
    label: 'Average ROAS',
    value: 4.8,
    suffix: 'x',
    description: 'Return on advertising spend',
    icon: '📈'
  },
  {
    id: '4',
    label: 'Years Experience',
    value: 5,
    suffix: '+',
    description: 'Years in digital marketing',
    icon: '⭐'
  },
  {
    id: '5',
    label: 'Website Performance',
    value: 98,
    suffix: '%',
    description: 'Average performance score',
    icon: '⚡'
  },
  {
    id: '6',
    label: 'Client Retention',
    value: 92,
    suffix: '%',
    description: 'Clients who return for more projects',
    icon: '🤝'
  }
];

export const getTestimonialsByRating = (minRating: number): Testimonial[] => {
  return testimonials.filter(testimonial => testimonial.rating >= minRating);
};

export const getTestimonialsByProjectType = (projectType: string): Testimonial[] => {
  return testimonials.filter(testimonial => 
    testimonial.projectType.toLowerCase().includes(projectType.toLowerCase())
  );
};

export const getFeaturedTestimonials = (): Testimonial[] => {
  return testimonials.slice(0, 3);
};
