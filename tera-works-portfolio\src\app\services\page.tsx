import { Metadata } from 'next';
import Services from '@/components/sections/Services';

export const metadata: Metadata = {
  title: 'Services',
  description: 'Professional web development and Meta advertising services to grow your business.',
};

export default function ServicesPage() {
  return (
    <div className="pt-20">
      <Services />
      
      {/* Additional Services Content */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">
              Our Process
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl font-bold">1</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Discovery</h3>
                <p className="text-gray-600 text-sm">
                  We start by understanding your business goals, target audience, and current challenges.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl font-bold">2</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Strategy</h3>
                <p className="text-gray-600 text-sm">
                  We develop a comprehensive strategy tailored to your specific needs and objectives.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl font-bold">3</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Execution</h3>
                <p className="text-gray-600 text-sm">
                  Our team implements the strategy with precision, keeping you informed every step of the way.
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl font-bold">4</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Optimization</h3>
                <p className="text-gray-600 text-sm">
                  We continuously monitor and optimize performance to ensure maximum ROI.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
