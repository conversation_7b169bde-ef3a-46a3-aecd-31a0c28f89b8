# Tera Works Portfolio Website

A professional portfolio website for Tera Works, showcasing web development and Meta advertising services. Built with Next.js 15, TypeScript, Tailwind CSS, GSAP, and Framer Motion.

## 🚀 Features

- **Modern Design**: Clean, professional design with smooth animations
- **Responsive**: Fully responsive design that works on all devices
- **Performance Optimized**: Built with Next.js 15 for optimal performance
- **SEO Friendly**: Comprehensive SEO optimization with meta tags and structured data
- **Interactive Animations**: GSAP and Framer Motion animations for engaging user experience
- **Contact Form**: Functional contact form with API integration
- **Portfolio Showcase**: Dynamic portfolio section with filtering capabilities
- **Service Pages**: Detailed service descriptions and pricing information
- **Blog Ready**: Blog structure ready for content management

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: GSAP, Framer Motion, Lottie React
- **Icons**: Heroicons
- **Forms**: React Hook Form with Zod validation
- **Fonts**: Inter & Poppins (Google Fonts)

## 📁 Project Structure

```
tera-works-portfolio/
├── public/                 # Static assets
│   ├── images/            # Optimized images
│   ├── logo.png          # Company logo
│   └── og-image.jpg      # Open Graph image
├── src/
│   ├── app/              # Next.js App Router
│   │   ├── about/        # About page
│   │   ├── portfolio/    # Portfolio page
│   │   ├── services/     # Services page
│   │   ├── contact/      # Contact page
│   │   ├── blog/         # Blog page
│   │   └── api/          # API routes
│   ├── components/       # React components
│   │   ├── ui/           # Reusable UI components
│   │   ├── layout/       # Layout components
│   │   ├── sections/     # Page sections
│   │   ├── animations/   # Animation components
│   │   └── forms/        # Form components
│   ├── lib/              # Utility functions
│   │   ├── animations/   # Animation utilities
│   │   └── utils.ts      # General utilities
│   ├── hooks/            # Custom React hooks
│   ├── types/            # TypeScript definitions
│   ├── data/             # Static data
│   └── styles/           # Additional styles
└── docs/                 # Documentation
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd tera-works-portfolio
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📝 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## 🎨 Customization

### Colors
The color scheme can be customized in `tailwind.config.ts`:

```typescript
colors: {
  primary: { /* Blue shades */ },
  secondary: { /* Gray shades */ },
  accent: { /* Green shades */ },
}
```

### Content
- **Portfolio items**: Edit `src/data/portfolio.ts`
- **Services**: Edit `src/data/services.ts`
- **Testimonials**: Edit `src/data/testimonials.ts`
- **Navigation**: Edit `src/data/navigation.ts`

### Animations
- **GSAP animations**: `src/lib/animations/gsap.ts`
- **Framer Motion variants**: `src/lib/animations/framer.ts`

## 🌐 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms
The project can be deployed to any platform that supports Next.js:
- Netlify
- AWS Amplify
- Railway
- DigitalOcean App Platform

## 📧 Contact Form Setup

The contact form uses a Next.js API route. To integrate with email services:

1. Update `src/app/api/contact/route.ts`
2. Add email service integration (SendGrid, Resend, etc.)
3. Configure environment variables

## 🔧 Environment Variables

Create a `.env.local` file:

```env
# Email Service (optional)
EMAIL_SERVICE_API_KEY=your_api_key

# Analytics (optional)
NEXT_PUBLIC_GA_ID=your_google_analytics_id

# Contact Form
CONTACT_EMAIL=<EMAIL>
```

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is proprietary and confidential. All rights reserved by Tera Works.

## 📞 Support

For support or questions, contact:
- Email: <EMAIL>
- Website: https://teraworks.com

---

Built with ❤️ by Tera Works
