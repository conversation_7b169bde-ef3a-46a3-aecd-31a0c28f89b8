# 🎬 GSAP (GreenSock Animation Platform) Comprehensive Guide

## 📦 Library Overview

### Name & Description
- **Official name**: GSAP (GreenSock Animation Platform)
- **Description**: A high-performance, professional-grade animation library for the modern web that works in all major browsers and supports animating CSS properties, SVG, React components, and more.
- **Key features**:
  - High performance with GPU acceleration
  - Cross-browser compatibility
  - Timeline-based animation control
  - Scroll-triggered animations
  - Modular architecture with plugins
- **Use cases**:
  - Complex animations for web applications
  - Interactive UI transitions
  - Scroll-based visual storytelling
  - Game development
  - Data visualization animations
- **Target audience**: Frontend developers, UI/UX designers, and web application developers requiring advanced animation capabilities.

## 🛠️ Installation & Setup

### Installation Methods

#### NPM Installation
```bash
# Core GSAP library
npm install gsap

# React integration package
npm install @gsap/react
```

#### CDN Installation
```html
<!-- Core GSAP library -->
<script src="https://cdn.jsdelivr.net/npm/gsap@3.13/dist/gsap.min.js"></script>

<!-- ScrollTrigger plugin -->
<script src="https://cdn.jsdelivr.net/npm/gsap@3.13/dist/ScrollTrigger.min.js"></script>

<!-- Draggable plugin -->
<script src="https://cdn.jsdelivr.net/npm/gsap@3.13/dist/Draggable.min.js"></script>```

### Basic Setup

#### Importing GSAP and Plugins
```javascript
// Import core GSAP
import gsap from "gsap";

// Import specific plugins
import ScrollTrigger from "gsap/ScrollTrigger";
import Flip from "gsap/Flip";
import Draggable from "gsap/Draggable";

// Register plugins with GSAP
gsap.registerPlugin(ScrollTrigger, Draggable, Flip);```

#### Setting up GSAP with React
```javascript
import { useRef } from "react";
import gsap from "gsap";
import { useGSAP } from "@gsap/react";

gsap.registerPlugin(useGSAP); // Register the React hook

const container = useRef();
useGSAP(() => {
    // GSAP animation code here
}, { scope: container }); // Define scope using a ref```

## 🧱 Core Concepts

### Main Components/Functions
- **gsap**: Core animation engine for creating tweens and timelines
- **ScrollTrigger**: Creates scroll-based animations and effects
- **Draggable**: Enables drag-and-drop functionality for elements
- **Flip**: Animates state changes with FLIP technique (First, Last, Invert, Play)
- **MotionPathPlugin**: Animates elements along complex SVG paths

### Architecture Overview
GSAP follows a modular architecture where the core engine provides basic animation capabilities, and additional functionality is added through plugins. The library supports both imperative and declarative approaches to animation, with special integrations for React and other frameworks.

The architecture consists of:
1. **Core Engine**: Handles basic tweening and timeline management
2. **Plugins**: Extend functionality for specific use cases (scroll, dragging, path animation)
3. **React Integration**: Provides hooks (`useGSAP`) for managing animations in React components
4. **Utilities**: Helper functions for common animation tasks

## 🎯 Usage Guide

### Basic Usage Example

#### Simple Element Animation
```javascript
// Animate an element to move 100px right over 1 second
gsap.to("#my-element", { 
    duration: 1, 
    x: 100, 
    ease: "power2.out" 
});```

#### Timeline Animation
```javascript
// Create a sequence of animations
const timeline = gsap.timeline();
timeline
    .from("#element1", { opacity: 0, duration: 0.5 })
    .from("#element2", { scale: 0, duration: 0.8 }, "<") // Start at same time as previous
    .to("#element3", { rotation: 360, duration: 1.5 }, "+=0.5"); // Start 0.5s after previous```

### Advanced Features

#### Scroll-Triggered Animations
```javascript
// Create a scroll-triggered animation
gsap.to("#animated-element", {
    scrollTrigger: {
        trigger: "#trigger-element",
        start: "top center",
        end: "bottom center",
        scrub: true,
        markers: false
    },
    x: 300,
    opacity: 0.5
});```

#### Context Management in React
```javascript
// Using contextSafe for event handlers
const container = useRef();

const { contextSafe } = useGSAP({ scope: container });

const onClickHandler = contextSafe(() => {
    gsap.to(".target", { rotation: 180 });
});

// Cleanup on unmount
return () => {
    if (container.current) {
        gsap.context(null, container.current);
    }
};```

#### Multiple Element Animation
```javascript
// Animate multiple elements with scoped selector
gsap.from(".box", { 
    opacity: 0, 
    y: 50, 
    stagger: 0.2, 
    duration: 0.5 
});```

## 🔄 Integration

### With Other Libraries/Frameworks

#### React Integration Best Practices
```javascript
import { useRef, useEffect } from "react";
import gsap from "gsap";
import { useGSAP } from "@gsap/react";

gsap.registerPlugin(useGSAP);

export default function AnimatedComponent() {
    const container = useRef();
    const boxRef = useRef();

    useGSAP(() => {
        // Create timeline animation
        const tl = gsap.timeline();
        tl.from(boxRef.current, { 
            opacity: 0, 
            scale: 0.5, 
            duration: 0.5 
        })
        .to(boxRef.current, { 
            rotation: 360, 
            duration: 1, 
            ease: "power2.inOut" 
        });

        // Return cleanup function
        return () => {
            tl.kill(); // Clean up animation on component unmount
        };
    }, { dependencies: [], scope: container });

    return (
        <div ref={container}>
            <div ref={boxRef} className="animated-box">
                I get animated!
            </div>
        </div>
    );
}```

### TypeScript Support
GSAP has full TypeScript support. When installing via NPM, type definitions are included automatically. For React projects, ensure you have the necessary type declarations:

```typescript
// Typings for GSAP are included in the package
import gsap from "gsap";
import { TimelineLite, Power2 } from "gsap/all";

// For React integration:
import { useGSAP, GSAPContext } from "@gsap/react";

interface AnimatedProps {
    duration?: number;
    delay?: number;
    children: React.ReactNode;
}

const AnimatedComponent: React.FC<AnimatedProps> = ({ duration = 1, delay = 0, children }) => {
    const container = useRef<HTMLDivElement>(null);
    
    useGSAP(() => {
        gsap.fromTo(
            container.current, 
            { opacity: 0, y: 20 }, 
            { 
                opacity: 1, 
                y: 0, 
                duration, 
                delay, 
                ease: "bounce.out"
            }
        );
    }, { scope: container });

    return (
        <div ref={container}>
            {children}
        </div>
    );
};```

## 🧪 Testing

### Unit Testing
When testing GSAP animations in unit tests, focus on verifying the correct setup and configuration rather than the actual animation behavior since animations should be disabled during testing.

```javascript
// Jest test example
import { render, screen } from '@testing-library/react';
import AnimatedComponent from './AnimatedComponent';

it('should render animated component', () => {
    render(<AnimatedComponent />);
    expect(screen.getByText('I get animated!')).toBeInTheDocument();
});```

### E2E Testing
For end-to-end testing, use tools like Cypress or Playwright to verify animation outcomes:

```javascript
// Cypress test example
describe('Animation Tests', () => {
    it('should complete animation correctly', () => {
        cy.visit('/animated-page');
        cy.get('#animated-element')
            .should('have.css', 'transform')
            .and('match', /matrix.*1, 0, 0, 1, 300, 0/); // Verify final position after animation
    });
});```

## 🔧 Configuration

### Available Options
GSAP animations can be configured with various options to control timing, easing, and animation behavior:

```javascript
// Common animation configuration options
gsap.to("#element", {
    duration: 1.5, // Duration in seconds
    delay: 0.5, // Delay before animation starts
    repeat: 2, // Repeat 2 times (total of 3 iterations including initial play)
    repeatDelay: 0.3, // Delay between repeats
    yoyo: true, // Reverse on every other iteration
    ease: "elastic.out(1, 0.3)", // Custom easing function
    paused: false, // Whether to start paused
    overwrite: "auto", // Overwrite behavior for conflicting animations
    onInit: (tween) => console.log('Animation initialized', tween),
    onComplete: () => console.log('Animation completed'),
    onUpdate: (tween) => console.log(`Animation progress: ${tween.progress * 100}%`)
});```

### Customization
GSAP offers extensive customization options for animations, including:

#### Custom Ease Functions
```javascript
// Create custom Bezier ease
gsap.registerPlugin(CustomEase);

CustomEase.create("custom-ease", "M0,0 C0.2,1 0.8,1 1,0");

gsap.to("#element", {
    duration: 1,
    x: 200,
    ease: "custom-ease"
});```

#### Animation Staggers
```javascript
// Staggered animation for multiple elements
gsap.utils.toArray(".stagger-item").forEach((item, i) => {
    gsap.from(item, {
        opacity: 0,
        y: 50,
        duration: 0.5,
        delay: i * 0.1, // Stagger effect with increasing delay
        ease: "power2.out"
    });
});```

## 📈 Performance

### Optimization Tips
- Use `gsap.quickSetter()` for high-performance property updates in scroll-driven animations
- Batch animations using timelines instead of creating individual tweens
- Use `lazy: true` option for animations that don't need immediate rendering
- Prefer `requestAnimationFrame` for manual animation control
- Use `overwrite: "auto"` to prevent animation conflicts
- Use `immediateRender: true` for animations that should render immediately
- Use `autoRemoveChildren: true` for timeline animations that create temporary elements

### Benchmark Results
GSAP is known for its exceptional performance:
- Typically runs at 60fps even with complex animations
- Uses hardware acceleration where available
- Optimized memory management prevents leaks
- Lightweight core (~5KB gzipped)
- Efficient ticker system minimizes CPU/GPU usage

## 📚 API Reference

### Components/Functions

#### gsap
Main entry point for creating animations
- `gsap.to()`: Creates an animation to a target state
- `gsap.from()`: Creates an animation from a starting state
- `gsap.fromTo()`: Creates an animation from one state to another
- `gsap.set()`: Sets properties without animation
- `gsap.delayedCall()`: Delays a function call by a set duration

#### Timelines
- `gsap.timeline()`: Creates a timeline for sequencing animations
- `timeline.add()`: Adds animations to the timeline
- `timeline.play()`, `timeline.pause()`, `timeline.reverse()`, `timeline.seek()`: Control timeline playback
- `timeline.repeat()`, `timeline.repeatDelay()`, `timeline.yoyo()`: Configure repeating animations

#### Plugins
- **ScrollTrigger**: Synchronizes animations with scroll position
- **Draggable**: Enables drag-and-drop interactions
- **MotionPathPlugin**: Animates elements along SVG paths
- **Flip**: Animates state changes efficiently
- **CustomEase**: Create custom easing curves
- **TextPlugin**: Animate text content changes
- **SVGPlugin**: Enhanced SVG manipulation
- **Physics2DPlugin**: Simulate physics-based motion

### Props & Parameters

#### Animation Properties
- `duration`: Duration of the animation in seconds
- `delay`: Delay before animation starts in seconds
- `repeat`: Number of times to repeat (0 means no repetition)
- `repeatDelay`: Delay between repetitions in seconds
- `yoyo`: If true, alternates direction on each repeat
- `ease`: Easing function to use
- `onComplete`: Callback when animation completes
- `onUpdate`: Callback on each animation update
- `onStart`: Callback when animation starts
- `paused`: Whether to start the animation paused
- `overwrite`: Controls how animations interact with existing ones
- `immediateRender`: Forces immediate rendering of the animation's starting values
- `autoRemoveChildren`: Automatically removes child tweens/timelines when parent completes

#### ScrollTrigger Parameters
- `trigger`: DOM element that triggers the animation
- `start`: Position of the trigger that initiates the animation
- `end`: Position of the trigger that ends the animation
- `scrub`: Smoothly scrub through the animation
- `pin`: Elements to pin while animation plays
- `markers`: Show debug markers
- `toggleActions`: Actions to perform at different scroll positions
- `once`: Whether to run the animation only once
- `fastScrollEnd`: Time threshold for detecting quick scrolls

### Events & Callbacks

#### Animation Events
- `onComplete`: Called when the animation finishes
- `onStart`: Called when the animation starts
- `onUpdate`: Called on each animation tick
- `onRepeat`: Called each time the animation repeats
- `onReverseComplete`: Called when the animation reverses completely
- `onOverwrite`: Called when this animation gets overwritten by another
- `onInterrupt`: Called when the animation gets interrupted

#### ScrollTrigger Events
- `onEnter`: Called when the animation enters the viewport
- `onLeave`: Called when the animation leaves the viewport
- `onEnterBack`: Called when the animation re-enters the viewport in reverse
- `onLeaveBack`: Called when the animation leaves the viewport in reverse
- `onUpdate`: Called when the ScrollTrigger instance updates
- `onRefresh`: Called when the ScrollTrigger refreshes
- `onScrubComplete`: Called when scrubbing completes

## 🧩 Recipes

### Common Use Cases

#### Scroll-Driven Animations
```javascript
// Create a horizontal scroll section
gsap.to(".horizontal-scroll", {
    scrollTrigger: {
        trigger: ".horizontal-scroll-container",
        start: "top left",
        end: "right left",
        scrub: true,
        pin: true,
        markers: false,
        horizontal: true // Enable horizontal scrolling
    },
    xPercent: -100 // Move 100% to the left
});```

#### Draggable Elements
```javascript
// Make an element draggable with constraints
Draggable.create("#draggable-element", {
    type: "x,y", // Allow movement in both directions
    bounds: ".drag-container", // Restrict to container
    edgeResistance: 0.65, // Resistance at boundaries
    dragClickables: true, // Allow dragging clickable elements
    onPress: () => console.log('Drag started'),
    onDrag: () => console.log(`Current position: ${this.x}, ${this.y}`),
    onRelease: () => console.log('Drag ended')
});```

### Troubleshooting

#### Common Issues and Solutions
1. **Animations not working**:
   - Check if GSAP is properly imported
   - Ensure elements exist in the DOM before animating
   - Verify selectors match actual elements
   - Check browser console for errors

2. **Performance issues**:
   - Use `lazy: true` for optimized rendering
   - Avoid animating layout-affecting properties
   - Use transforms for positional animations
   - Minimize the number of simultaneous animations

3. **React integration problems**:
   - Always register `useGSAP` as a plugin
   - Use `scope` with refs for proper element targeting
   - Handle cleanup in return function
   - Wrap event handlers with `contextSafe`

4. **ScrollTrigger not working**:
   - Ensure elements are in view
   - Check scroll direction matches animation axis
   - Add `pin: true` if needed
   - Use `invalidateOnRefresh: true` for dynamic content

## 📖 Best Practices

### Development Guidelines
- Keep animations short and purposeful
- Use `gsap.utils.toArray()` for animating multiple elements
- Organize complex animations with timelines
- Use `gsap.context()` for scoping animations to specific DOM elements
- Clean up animations in React components using return function
- Use `gsap.ticker` for custom animation loops
- Use `gsap.matchMedia` for responsive animations

### Performance Recommendations
- Avoid layout thrashing by batching animations
- Use `will-change` CSS property for frequently animated elements
- Prefer transforms and opacity animations for better performance
- Use `gsap.quickSetter()` for high-frequency property updates
- Debounce resize-related animations
- Use `gsap.utils.clamp()` for constrained value ranges
- Implement lazy loading for non-critical animations

## 📚 Additional Resources

### Official Documentation
- [GSAP Core Documentation](https://greensock.com/docs/v3/Installation)
- [GSAP React Integration](https://greensock.com/docs/v3/GSAP+React)
- [ScrollTrigger Documentation](https://greensock.com/docs/v3/Plugins/ScrollTrigger)

### Community & Support
- [GSAP Forum](https://greensock.com/forums/)
- [GSAP GitHub Repository](https://github.com/greensock/gsap)
- [GSAP React GitHub Repository](https://github.com/greensock/react)
- [Stack Overflow GSAP Questions](https://stackoverflow.com/questions/tagged/gsap)

### Tutorials & Examples
- [GSAP Getting Started Guide](https://greensock.com/docs/v3/GettingStarted)
- [GSAP React Demos](https://greensock.com/react/)
- [GSAP ScrollTrigger Demos](https://greensock.com/st-demos/)
- [GSAP CodePen Collection](https://codepen.io/collection/AJpZwO)