exports.id=163,exports.ids=[163],exports.modules={354:(e,t,r)=>{Promise.resolve().then(r.bind(r,3853)),Promise.resolve().then(r.bind(r,1091)),Promise.resolve().then(r.bind(r,8017)),Promise.resolve().then(r.bind(r,7710))},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},459:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},1091:(e,t,r)=>{"use strict";r.d(t,{PortfolioCard:()=>i,ServiceCard:()=>a,TestimonialCard:()=>o,default:()=>l});var s=r(2907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call ServiceCard() from the server but ServiceCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Card.tsx","ServiceCard"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call PortfolioCard() from the server but PortfolioCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Card.tsx","PortfolioCard"),o=(0,s.registerClientReference)(function(){throw Error("Attempted to call TestimonialCard() from the server but TestimonialCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Card.tsx","TestimonialCard"),l=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Card.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Card.tsx","default")},1102:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(7413),a=r(3853);function i(){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("div",{className:"text-6xl font-bold text-primary-600 mb-4",children:"404"}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Page Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(a.default,{href:"/",variant:"primary",className:"w-full",children:"Go Home"}),(0,s.jsx)(a.default,{href:"/contact",variant:"outline",className:"w-full",children:"Contact Us"})]})]})})}r(1091),r(8017),r(7710)},1135:()=>{},1907:(e,t,r)=>{"use strict";r.d(t,{Select:()=>d,Textarea:()=>c,default:()=>m});var s=r(687),a=r(3210),i=r(6001),o=r(4780),l=r(4020);let n=(0,a.forwardRef)(({className:e,label:t,error:r,helperText:n,variant:c="default",type:d="text",id:m,...h},x)=>{let[p,f]=(0,a.useState)(!1),[u,v]=(0,a.useState)(!1),g=m||`input-${Math.random().toString(36).substr(2,9)}`,b=(0,o.cn)("w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed",r?"border-red-300 focus:ring-red-500":"border-gray-300 focus:border-primary-500","floating"===c&&"pt-6 pb-2",e),y=e=>{f(!0),h.onFocus?.(e)},j=e=>{f(!1),v(""!==e.target.value),h.onBlur?.(e)},w=e=>{v(""!==e.target.value),h.onChange?.(e)};return"floating"===c?(0,s.jsxs)("div",{className:"relative mb-6",children:[(0,s.jsx)(i.P.input,{ref:x,type:d,id:g,className:b,onFocus:y,onBlur:j,onChange:w,variants:l.Xy,whileFocus:"focus",...h}),t&&(0,s.jsx)(i.P.label,{htmlFor:g,variants:l.pP,animate:p||u?"active":"default",className:"absolute left-3 top-2 pointer-events-none text-sm font-medium",children:t}),r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:r}),n&&!r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:n})]}):(0,s.jsxs)("div",{className:"mb-4",children:[t&&(0,s.jsx)("label",{htmlFor:g,className:"block text-sm font-medium text-gray-700 mb-1",children:t}),(0,s.jsx)(i.P.input,{ref:x,type:d,id:g,className:b,onFocus:y,onBlur:j,onChange:w,variants:l.Xy,whileFocus:"focus",...h}),r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:r}),n&&!r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:n})]})});n.displayName="Input";let c=(0,a.forwardRef)(({className:e,label:t,error:r,helperText:a,id:n,...c},d)=>{let m=n||`textarea-${Math.random().toString(36).substr(2,9)}`,h=(0,o.cn)("w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed resize-vertical min-h-[100px]",r?"border-red-300 focus:ring-red-500":"border-gray-300 focus:border-primary-500",e);return(0,s.jsxs)("div",{className:"mb-4",children:[t&&(0,s.jsx)("label",{htmlFor:m,className:"block text-sm font-medium text-gray-700 mb-1",children:t}),(0,s.jsx)(i.P.textarea,{ref:d,id:m,className:h,variants:l.Xy,whileFocus:"focus",...c}),r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:r}),a&&!r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:a})]})});c.displayName="Textarea";let d=(0,a.forwardRef)(({className:e,label:t,error:r,helperText:a,options:n,id:c,...d},m)=>{let h=c||`select-${Math.random().toString(36).substr(2,9)}`,x=(0,o.cn)("w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed bg-white",r?"border-red-300 focus:ring-red-500":"border-gray-300 focus:border-primary-500",e);return(0,s.jsxs)("div",{className:"mb-4",children:[t&&(0,s.jsx)("label",{htmlFor:h,className:"block text-sm font-medium text-gray-700 mb-1",children:t}),(0,s.jsx)(i.P.select,{ref:m,id:h,className:x,variants:l.Xy,whileFocus:"focus",...d,children:n.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))}),r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:r}),a&&!r&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:a})]})});d.displayName="Select";let m=n},2408:(e,t,r)=>{Promise.resolve().then(r.bind(r,2830)),Promise.resolve().then(r.bind(r,5736))},2643:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(687);r(3210);var a=r(5814),i=r.n(a),o=r(6001),l=r(4780),n=r(4020);let c=({children:e,className:t,variant:r="primary",size:a="md",disabled:c=!1,loading:d=!1,onClick:m,type:h="button",href:x,external:p=!1,...f})=>{let u=(0,l.cn)("inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500",secondary:"bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500",outline:"border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500",ghost:"text-primary-600 hover:bg-primary-50 focus:ring-primary-500"}[r],{sm:"px-3 py-2 text-sm rounded-md",md:"px-4 py-2 text-base rounded-lg",lg:"px-6 py-3 text-lg rounded-lg"}[a],t),v=(0,s.jsxs)(s.Fragment,{children:[d&&(0,s.jsx)(()=>(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),{}),e]});return x?p?(0,s.jsx)(o.P.a,{href:x,target:"_blank",rel:"noopener noreferrer",className:u,variants:n.ru,initial:"idle",whileHover:"hover",whileTap:"tap",...f,children:v}):(0,s.jsx)(i(),{href:x,className:u,...f,children:(0,s.jsx)(o.P.span,{variants:n.ru,initial:"idle",whileHover:"hover",whileTap:"tap",className:"flex items-center justify-center w-full h-full",children:v})}):(0,s.jsx)(o.P.button,{type:h,className:u,disabled:c||d,onClick:m,variants:n.ru,initial:"idle",whileHover:c||d?"idle":"hover",whileTap:c||d?"idle":"tap",...f,children:v})}},2830:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var s=r(687);r(3210);var a=r(5814),i=r.n(a),o=r(474),l=r(6001),n=r(4859),c=r(137),d=r(6942),m=r(7398),h=r(4020);let x=()=>{let e=new Date().getFullYear(),t=({platform:e,className:t="w-5 h-5"})=>{switch(e.toLowerCase()){case"linkedin":return(0,s.jsx)("svg",{className:t,fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})});case"twitter":return(0,s.jsx)("svg",{className:t,fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})});case"facebook":return(0,s.jsx)("svg",{className:t,fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})});case"instagram":return(0,s.jsx)("svg",{className:t,fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.596-3.205-1.533l1.714-1.714c.39.586 1.07.977 1.85.977.98 0 1.776-.796 1.776-1.776 0-.98-.796-1.776-1.776-1.776-.78 0-1.46.391-1.85.977L4.244 10.43c.757-.937 1.908-1.533 3.205-1.533 2.269 0 4.106 1.837 4.106 4.106s-1.837 4.106-4.106 4.106zm7.441 0c-2.269 0-4.106-1.837-4.106-4.106s1.837-4.106 4.106-4.106c1.297 0 2.448.596 3.205 1.533l-1.714 1.714c-.39-.586-1.07-.977-1.85-.977-.98 0-1.776.796-1.776 1.776 0 .98.796 1.776 1.776 1.776.78 0 1.46-.391 1.85-.977l1.714 1.714c-.757.937-1.908 1.533-3.205 1.533z"})});case"github":return(0,s.jsx)("svg",{className:t,fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})});default:return(0,s.jsx)(n.A,{className:t})}};return(0,s.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,s.jsxs)("div",{className:"container-custom",children:[(0,s.jsx)(l.P.div,{variants:h.bK,initial:"initial",whileInView:"animate",viewport:{once:!0},className:"py-16 lg:py-20",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12",children:[(0,s.jsxs)(l.P.div,{variants:h.Rf,className:"lg:col-span-1",children:[(0,s.jsxs)(i(),{href:"/",className:"flex items-center space-x-2 mb-6",children:[(0,s.jsx)("div",{className:"relative w-10 h-10",children:(0,s.jsx)(o.default,{src:"/logo.png",alt:"Tera Works Logo",fill:!0,className:"object-contain"})}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"text-xl font-bold",children:"Tera Works"}),(0,s.jsx)("span",{className:"text-sm text-gray-400 -mt-1",children:"Let's Grow Together"})]})]}),(0,s.jsx)("p",{className:"text-gray-300 mb-6 text-sm leading-relaxed",children:"Professional web development and Meta advertising services to help your business grow and succeed online."}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,s.jsx)(n.A,{className:"w-4 h-4 text-primary-400 flex-shrink-0"}),(0,s.jsx)("a",{href:`mailto:${m.r_.email}`,className:"text-gray-300 hover:text-white transition-colors",children:m.r_.email})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,s.jsx)(c.A,{className:"w-4 h-4 text-primary-400 flex-shrink-0"}),(0,s.jsx)("a",{href:`tel:${m.r_.phone}`,className:"text-gray-300 hover:text-white transition-colors",children:m.r_.phone})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3 text-sm",children:[(0,s.jsx)(d.A,{className:"w-4 h-4 text-primary-400 flex-shrink-0 mt-0.5"}),(0,s.jsxs)("div",{className:"text-gray-300",children:[(0,s.jsx)("div",{children:m.r_.address.street}),(0,s.jsxs)("div",{children:[m.r_.address.city,", ",m.r_.address.state," ",m.r_.address.zip]})]})]})]})]}),(0,s.jsxs)(l.P.div,{variants:h.Rf,children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Services"}),(0,s.jsx)("ul",{className:"space-y-3",children:m.ii.services.map(e=>(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-white transition-colors text-sm",children:e.label})},e.href))})]}),(0,s.jsxs)(l.P.div,{variants:h.Rf,children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Company"}),(0,s.jsx)("ul",{className:"space-y-3",children:m.ii.company.map(e=>(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-white transition-colors text-sm",children:e.label})},e.href))})]}),(0,s.jsxs)(l.P.div,{variants:h.Rf,children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-6",children:"Resources"}),(0,s.jsx)("ul",{className:"space-y-3 mb-6",children:m.ii.resources.map(e=>(0,s.jsx)("li",{children:(0,s.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-white transition-colors text-sm",children:e.label})},e.href))}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Follow Us"}),(0,s.jsx)("div",{className:"flex space-x-4",children:m.lj.slice(0,4).map(e=>(0,s.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white transition-colors","aria-label":e.label,children:(0,s.jsx)(t,{platform:e.platform})},e.platform))})]})]})]})}),(0,s.jsx)(l.P.div,{variants:h.tE,initial:"initial",whileInView:"animate",viewport:{once:!0},className:"border-t border-gray-800 py-6",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-400",children:["\xa9 ",e," Tera Works. All rights reserved."]}),(0,s.jsx)("div",{className:"flex space-x-6",children:m.ii.legal.map(e=>(0,s.jsx)(i(),{href:e.href,className:"text-sm text-gray-400 hover:text-white transition-colors",children:e.label},e.href))})]})})]})})}},3437:(e,t,r)=>{"use strict";r.d(t,{$n:()=>s.default,TM:()=>i.Textarea,_f:()=>a.TestimonialCard,l6:()=>i.Select,mB:()=>a.ServiceCard,pd:()=>i.default});var s=r(2643),a=r(8749),i=r(1907);r(7576)},3853:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Button.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Button.tsx","default")},4020:(e,t,r)=>{"use strict";r.d(t,{CV:()=>f,Rf:()=>i,Tz:()=>m,VM:()=>p,Xy:()=>h,Ym:()=>d,bK:()=>a,c$:()=>n,fP:()=>l,jE:()=>c,pP:()=>x,ru:()=>o,tE:()=>s});let s={initial:{opacity:0,y:60},animate:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}},exit:{opacity:0,y:-60}},a={initial:{},animate:{transition:{staggerChildren:.1,delayChildren:.3}}},i={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.5,ease:"easeOut"}}},o={idle:{scale:1},hover:{scale:1.05,transition:{duration:.2,ease:"easeOut"}},tap:{scale:.95,transition:{duration:.1}}},l={idle:{y:0,scale:1,boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"},hover:{y:-8,scale:1.02,boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)",transition:{duration:.3,ease:"easeOut"}}},n={hidden:{opacity:0},visible:{opacity:1}},c={hidden:{opacity:0,scale:.8,y:50},visible:{opacity:1,scale:1,y:0,transition:{type:"spring",damping:25,stiffness:300}},exit:{opacity:0,scale:.8,y:50,transition:{duration:.2}}},d={closed:{opacity:0,height:0,transition:{duration:.3,ease:"easeInOut"}},open:{opacity:1,height:"auto",transition:{duration:.3,ease:"easeInOut",staggerChildren:.1,delayChildren:.1}}},m={closed:{opacity:0,x:-20},open:{opacity:1,x:0,transition:{duration:.3,ease:"easeOut"}}},h={focus:{scale:1.02,transition:{duration:.2,ease:"easeOut"}},blur:{scale:1,transition:{duration:.2,ease:"easeOut"}}},x={default:{y:0,scale:1,color:"#6B7280"},active:{y:-24,scale:.8,color:"#3B82F6",transition:{duration:.2,ease:"easeOut"}}},p={hidden:{opacity:0},show:{opacity:1,transition:{staggerChildren:.1}}},f={hidden:{opacity:0,scale:.8},show:{opacity:1,scale:1,transition:{duration:.5,ease:"easeOut"}}}},4329:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(687);r(3210);var a=r(3437);function i({error:e,reset:t}){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Something went wrong!"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"We apologize for the inconvenience. An error occurred while loading this page."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(a.$n,{onClick:t,variant:"primary",className:"w-full",children:"Try Again"}),(0,s.jsx)(a.$n,{href:"/",variant:"outline",className:"w-full",children:"Go Home"})]})]})})}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>d});var s=r(7413),a=r(1194),i=r.n(a),o=r(1169),l=r.n(o);r(1135);var n=r(8926),c=r(4712);let d={title:{default:"Tera Works - Professional Web Development & Meta Advertising",template:"%s | Tera Works"},description:"Professional website development and Meta advertising services. Let's grow together with custom web solutions and targeted social media campaigns.",keywords:["web development","meta advertising","website design","social media marketing","facebook ads","instagram ads","booking systems"],authors:[{name:"Tera Works"}],creator:"Tera Works",openGraph:{type:"website",locale:"en_US",url:"https://teraworks.com",siteName:"Tera Works",title:"Tera Works - Professional Web Development & Meta Advertising",description:"Professional website development and Meta advertising services. Let's grow together.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Tera Works - Let's Grow Together"}]},twitter:{card:"summary_large_image",title:"Tera Works - Professional Web Development & Meta Advertising",description:"Professional website development and Meta advertising services.",images:["/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function m({children:e}){return(0,s.jsx)("html",{lang:"en",className:`${i().variable} ${l().variable}`,children:(0,s.jsxs)("body",{className:"font-sans antialiased",children:[(0,s.jsx)(n.default,{}),(0,s.jsx)("main",{className:"min-h-screen",children:e}),(0,s.jsx)(c.default,{})]})})}},4712:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Footer.tsx","default")},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(9384);function a(...e){return(0,s.$)(e)}},4803:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},5560:(e,t,r)=>{Promise.resolve().then(r.bind(r,4712)),Promise.resolve().then(r.bind(r,8926))},5736:(e,t,r)=>{"use strict";r.d(t,{default:()=>v});var s=r(687),a=r(3210),i=r(5814),o=r.n(i),l=r(474),n=r(6189),c=r(8920),d=r(6001),m=r(1836),h=r(6510),x=r(4780),p=r(7398),f=r(3437),u=r(4020);let v=()=>{let[e,t]=(0,a.useState)(!1),[r,i]=(0,a.useState)(!1),v=(0,n.usePathname)();(0,a.useEffect)(()=>{let e=()=>{t(window.scrollY>10)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,a.useEffect)(()=>{i(!1)},[v]);let g=e=>"/"===e?"/"===v:v.startsWith(e);return(0,s.jsx)("header",{className:(0,x.cn)("fixed top-0 left-0 right-0 z-50 transition-all duration-300",e?"bg-white/95 backdrop-blur-sm shadow-lg":"bg-transparent"),children:(0,s.jsxs)("div",{className:"container-custom",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between h-16 lg:h-20",children:[(0,s.jsxs)(o(),{href:"/",className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"relative w-10 h-10 lg:w-12 lg:h-12",children:(0,s.jsx)(l.default,{src:"/logo.png",alt:"Tera Works Logo",fill:!0,className:"object-contain",priority:!0})}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"text-xl lg:text-2xl font-bold text-gray-900",children:"Tera Works"}),(0,s.jsx)("span",{className:"text-xs lg:text-sm text-gray-600 -mt-1",children:"Let's Grow Together"})]})]}),(0,s.jsx)("nav",{className:"hidden lg:flex items-center space-x-8",children:p.ss.map(t=>(0,s.jsx)(o(),{href:t.href,className:(0,x.cn)("text-sm font-medium transition-colors hover:text-primary-600",g(t.href)?"text-primary-600":e?"text-gray-900":"text-white"),children:t.label},t.href))}),(0,s.jsx)("div",{className:"hidden lg:flex items-center space-x-4",children:(0,s.jsx)(f.$n,{href:"/contact",variant:"primary",children:"Get Started"})}),(0,s.jsx)("button",{type:"button",onClick:()=>i(!r),className:(0,x.cn)("lg:hidden p-2 rounded-md transition-colors",e?"text-gray-900 hover:bg-gray-100":"text-white hover:bg-white/10"),"aria-label":"Toggle mobile menu",children:r?(0,s.jsx)(m.A,{className:"w-6 h-6"}):(0,s.jsx)(h.A,{className:"w-6 h-6"})})]}),(0,s.jsx)(c.N,{children:r&&(0,s.jsx)(d.P.div,{variants:u.Ym,initial:"closed",animate:"open",exit:"closed",className:"lg:hidden bg-white border-t border-gray-200 shadow-lg",children:(0,s.jsxs)("nav",{className:"py-4 space-y-2",children:[p.ss.map(e=>(0,s.jsx)(d.P.div,{variants:u.Tz,children:(0,s.jsx)(o(),{href:e.href,className:(0,x.cn)("block px-4 py-2 text-base font-medium transition-colors hover:bg-gray-50",g(e.href)?"text-primary-600 bg-primary-50":"text-gray-900"),children:e.label})},e.href)),(0,s.jsx)(d.P.div,{variants:u.Tz,className:"px-4 pt-4 border-t border-gray-200",children:(0,s.jsx)(f.$n,{href:"/contact",variant:"primary",className:"w-full",children:"Get Started"})})]})})})]})})}},5937:(e,t,r)=>{Promise.resolve().then(r.bind(r,4329))},6487:()=>{},6812:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\error.tsx","default")},7393:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(7413);function a(){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"})}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Loading..."}),(0,s.jsx)("p",{className:"text-gray-600",children:"Please wait while we prepare your content"})]})})}},7398:(e,t,r)=>{"use strict";r.d(t,{ii:()=>a,lj:()=>i,r_:()=>o,ss:()=>s});let s=[{label:"Home",href:"/"},{label:"About",href:"/about"},{label:"Services",href:"/services",children:[{label:"Web Development",href:"/services/web-development"},{label:"Meta Advertising",href:"/services/meta-advertising"},{label:"Booking Systems",href:"/services/booking-systems"}]},{label:"Portfolio",href:"/portfolio"},{label:"Blog",href:"/blog"},{label:"Contact",href:"/contact"}],a={services:[{label:"Custom Website Development",href:"/services/web-development"},{label:"E-commerce Solutions",href:"/services/ecommerce"},{label:"Booking Systems",href:"/services/booking-systems"},{label:"Meta Advertising",href:"/services/meta-advertising"},{label:"Website Maintenance",href:"/services/maintenance"}],company:[{label:"About Us",href:"/about"},{label:"Our Process",href:"/process"},{label:"Case Studies",href:"/portfolio"},{label:"Testimonials",href:"/testimonials"},{label:"Careers",href:"/careers"}],resources:[{label:"Blog",href:"/blog"},{label:"FAQ",href:"/faq"},{label:"Support",href:"/support"},{label:"Documentation",href:"/docs"},{label:"Contact",href:"/contact"}],legal:[{label:"Privacy Policy",href:"/privacy"},{label:"Terms of Service",href:"/terms"},{label:"Cookie Policy",href:"/cookies"},{label:"GDPR",href:"/gdpr"}]},i=[{platform:"LinkedIn",url:"https://linkedin.com/company/tera-works",icon:"linkedin",label:"Follow us on LinkedIn"},{platform:"Twitter",url:"https://twitter.com/teraworks",icon:"twitter",label:"Follow us on Twitter"},{platform:"Facebook",url:"https://facebook.com/teraworks",icon:"facebook",label:"Like us on Facebook"},{platform:"Instagram",url:"https://instagram.com/teraworks",icon:"instagram",label:"Follow us on Instagram"},{platform:"GitHub",url:"https://github.com/teraworks",icon:"github",label:"View our code on GitHub"},{platform:"Email",url:"mailto:<EMAIL>",icon:"email",label:"Send us an email"}],o={email:"<EMAIL>",phone:"+****************",address:{street:"123 Business Ave",city:"Tech City",state:"TC",zip:"12345",country:"United States"},hours:{weekdays:"9:00 AM - 6:00 PM",weekends:"10:00 AM - 4:00 PM",timezone:"EST"}}},7576:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(687),a=r(3210),i=r(8920),o=r(6001),l=r(1836),n=r(4780),c=r(4020);let d=({isOpen:e,onClose:t,children:r,title:d,size:m="md",closeOnOverlayClick:h=!0,closeOnEscape:x=!0,className:p})=>((0,a.useEffect)(()=>{let r=e=>{x&&"Escape"===e.key&&t()};return e&&(document.addEventListener("keydown",r),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",r),document.body.style.overflow="unset"}},[e,x,t]),(0,s.jsx)(i.N,{children:e&&(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,s.jsx)(o.P.div,{variants:c.c$,initial:"hidden",animate:"visible",exit:"hidden",className:"fixed inset-0 bg-black bg-opacity-50",onClick:e=>{h&&e.target===e.currentTarget&&t()}}),(0,s.jsxs)(o.P.div,{variants:c.jE,initial:"hidden",animate:"visible",exit:"exit",className:(0,n.cn)("relative bg-white rounded-lg shadow-xl w-full",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[m],p),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[d&&(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:d}),(0,s.jsx)("button",{onClick:t,className:"p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-full hover:bg-gray-100","aria-label":"Close modal",children:(0,s.jsx)(l.A,{className:"w-5 h-5"})})]}),(0,s.jsx)("div",{className:"p-6",children:r})]})]})}))},7638:(e,t,r)=>{Promise.resolve().then(r.bind(r,6812))},7710:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Modal.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Modal.tsx","default")},8017:(e,t,r)=>{"use strict";r.d(t,{Select:()=>i,Textarea:()=>a,default:()=>o});var s=r(2907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call Textarea() from the server but Textarea is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Input.tsx","Textarea"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call Select() from the server but Select is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Input.tsx","Select"),o=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\ui\\\\Input.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Input.tsx","default")},8335:()=>{},8506:(e,t,r)=>{Promise.resolve().then(r.bind(r,2643)),Promise.resolve().then(r.bind(r,8749)),Promise.resolve().then(r.bind(r,1907)),Promise.resolve().then(r.bind(r,7576))},8749:(e,t,r)=>{"use strict";r.d(t,{PortfolioCard:()=>h,ServiceCard:()=>m,TestimonialCard:()=>x,default:()=>p});var s=r(687);r(3210);var a=r(5814),i=r.n(a),o=r(474),l=r(6001),n=r(4780),c=r(4020);let d=({children:e,className:t,title:r,description:a,image:d,href:m,hover:h=!0,...x})=>{let p=(0,n.cn)("bg-white rounded-lg shadow-md overflow-hidden transition-shadow duration-300",t),f=(0,s.jsxs)(l.P.div,{className:p,variants:h?c.fP:void 0,initial:"idle",whileHover:h?"hover":"idle",...x,children:[d&&(0,s.jsx)("div",{className:"relative h-48 w-full",children:(0,s.jsx)(o.default,{src:d,alt:r||"Card image",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),(0,s.jsxs)("div",{className:"p-6",children:[r&&(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:r}),a&&(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:a}),e]})]});return m?(0,s.jsx)(i(),{href:m,className:"block",children:f}):f},m=({title:e,description:t,icon:r,features:a,href:i,className:o})=>(0,s.jsxs)(d,{title:e,description:t,href:i,className:(0,n.cn)("h-full",o),children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("span",{className:"text-4xl mr-3",children:r}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:e})]}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:t}),(0,s.jsx)("ul",{className:"space-y-2",children:a.slice(0,4).map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,s.jsx)("svg",{className:"w-4 h-4 text-green-500 mr-2 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),e]},t))})]}),h=({title:e,description:t,image:r,technologies:a,href:i,className:o})=>(0,s.jsx)(d,{title:e,description:t,image:r,href:i,className:(0,n.cn)("h-full",o),children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mt-4",children:[a.slice(0,3).map((e,t)=>(0,s.jsx)("span",{className:"px-2 py-1 bg-primary-100 text-primary-800 text-xs rounded-full",children:e},t)),a.length>3&&(0,s.jsxs)("span",{className:"px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full",children:["+",a.length-3," more"]})]})}),x=({name:e,position:t,company:r,content:a,avatar:i,rating:l,className:c})=>(0,s.jsxs)(d,{className:(0,n.cn)("h-full",c),hover:!1,children:[(0,s.jsx)("div",{className:"flex items-center mb-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,s.jsx)("svg",{className:(0,n.cn)("w-5 h-5",t<l?"text-yellow-400":"text-gray-300"),fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},t))}),(0,s.jsxs)("blockquote",{className:"text-gray-600 mb-4 italic",children:['"',a,'"']}),(0,s.jsxs)("div",{className:"flex items-center",children:[i&&(0,s.jsx)("div",{className:"relative w-12 h-12 mr-4",children:(0,s.jsx)(o.default,{src:i,alt:e,fill:!0,className:"rounded-full object-cover"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-semibold text-gray-900",children:e}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[t," at ",r]})]})]})]}),p=d},8926:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Augment Code Testing\\\\tera-works-portfolio\\\\src\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Header.tsx","default")}};