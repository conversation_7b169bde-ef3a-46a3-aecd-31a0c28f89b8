"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[375],{802:(e,t,r)=>{r.d(t,{Ay:()=>eb});var n,i,o,s,a,l,c,u=r(934),f={},p=180/Math.PI,d=Math.PI/180,h=Math.atan2,g=/([A-Z])/g,v=/(left|right|width|margin|padding|x)/i,m=/[\s,\(]\S/,y={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},x=function(e,t){return t.set(t.t,t.p,Math.round((t.s+t.c*e)*1e4)/1e4+t.u,t)},b=function(e,t){return t.set(t.t,t.p,1===e?t.e:Math.round((t.s+t.c*e)*1e4)/1e4+t.u,t)},_=function(e,t){return t.set(t.t,t.p,e?Math.round((t.s+t.c*e)*1e4)/1e4+t.u:t.b,t)},w=function(e,t){var r=t.s+t.c*e;t.set(t.t,t.p,~~(r+(r<0?-.5:.5))+t.u,t)},O=function(e,t){return t.set(t.t,t.p,e?t.e:t.b,t)},M=function(e,t){return t.set(t.t,t.p,1!==e?t.b:t.e,t)},E=function(e,t,r){return e.style[t]=r},C=function(e,t,r){return e.style.setProperty(t,r)},P=function(e,t,r){return e._gsap[t]=r},T=function(e,t,r){return e._gsap.scaleX=e._gsap.scaleY=r},k=function(e,t,r,n,i){var o=e._gsap;o.scaleX=o.scaleY=r,o.renderTransform(i,o)},S=function(e,t,r,n,i){var o=e._gsap;o[t]=r,o.renderTransform(i,o)},A="transform",Y=A+"Origin",z=function e(t,r){var n=this,i=this.target,o=i.style,s=i._gsap;if(t in f&&o){if(this.tfm=this.tfm||{},"transform"===t)return y.transform.split(",").forEach(function(t){return e.call(n,t,r)});if(~(t=y[t]||t).indexOf(",")?t.split(",").forEach(function(e){return n.tfm[e]=$(i,e)}):this.tfm[t]=s.x?s[t]:$(i,t),t===Y&&(this.tfm.zOrigin=s.zOrigin),this.props.indexOf(A)>=0)return;s.svg&&(this.svgo=i.getAttribute("data-svg-origin"),this.props.push(Y,r,"")),t=A}(o||r)&&this.props.push(t,r,o[t])},R=function(e){e.translate&&(e.removeProperty("translate"),e.removeProperty("scale"),e.removeProperty("rotate"))},D=function(){var e,t,r=this.props,n=this.target,i=n.style,o=n._gsap;for(e=0;e<r.length;e+=3)r[e+1]?2===r[e+1]?n[r[e]](r[e+2]):n[r[e]]=r[e+2]:r[e+2]?i[r[e]]=r[e+2]:i.removeProperty("--"===r[e].substr(0,2)?r[e]:r[e].replace(g,"-$1").toLowerCase());if(this.tfm){for(t in this.tfm)o[t]=this.tfm[t];o.svg&&(o.renderTransform(),n.setAttribute("data-svg-origin",this.svgo||"")),(e=l())&&e.isStart||i[A]||(R(i),o.zOrigin&&i[Y]&&(i[Y]+=" "+o.zOrigin+"px",o.zOrigin=0,o.renderTransform()),o.uncache=1)}},F=function(e,t){var r={target:e,props:[],revert:D,save:z};return e._gsap||u.os.core.getCache(e),t&&e.style&&e.nodeType&&t.split(",").forEach(function(e){return r.save(e)}),r},X=function(e,t){var r=n.createElementNS?n.createElementNS((t||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),e):n.createElement(e);return r&&r.style?r:n.createElement(e)},B=function e(t,r,n){var i=getComputedStyle(t);return i[r]||i.getPropertyValue(r.replace(g,"-$1").toLowerCase())||i.getPropertyValue(r)||!n&&e(t,N(r)||r,1)||""},I="O,Moz,ms,Ms,Webkit".split(","),N=function(e,t,r){var n=(t||s).style,i=5;if(e in n&&!r)return e;for(e=e.charAt(0).toUpperCase()+e.substr(1);i--&&!(I[i]+e in n););return i<0?null:(3===i?"ms":i>=0?I[i]:"")+e},L=function(){"undefined"!=typeof window&&window.document&&(i=(n=window.document).documentElement,s=X("div")||{style:{}},X("div"),Y=(A=N(A))+"Origin",s.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",c=!!N("perspective"),l=u.os.core.reverting,o=1)},H=function(e){var t,r=e.ownerSVGElement,n=X("svg",r&&r.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),o=e.cloneNode(!0);o.style.display="block",n.appendChild(o),i.appendChild(n);try{t=o.getBBox()}catch(e){}return n.removeChild(o),i.removeChild(n),t},W=function(e,t){for(var r=t.length;r--;)if(e.hasAttribute(t[r]))return e.getAttribute(t[r])},q=function(e){var t,r;try{t=e.getBBox()}catch(n){t=H(e),r=1}return t&&(t.width||t.height)||r||(t=H(e)),!t||t.width||t.x||t.y?t:{x:+W(e,["x","cx","x1"])||0,y:+W(e,["y","cy","y1"])||0,width:0,height:0}},V=function(e){return!!(e.getCTM&&(!e.parentNode||e.ownerSVGElement)&&q(e))},U=function(e,t){if(t){var r,n=e.style;t in f&&t!==Y&&(t=A),n.removeProperty?(("ms"===(r=t.substr(0,2))||"webkit"===t.substr(0,6))&&(t="-"+t),n.removeProperty("--"===r?t:t.replace(g,"-$1").toLowerCase())):n.removeAttribute(t)}},j=function(e,t,r,n,i,o){var s=new u.J7(e._pt,t,r,0,1,o?M:O);return e._pt=s,s.b=n,s.e=i,e._props.push(r),s},G={deg:1,rad:1,turn:1},J={grid:1,flex:1},Z=function e(t,r,i,o){var a,l,c,p,d=parseFloat(i)||0,h=(i+"").trim().substr((d+"").length)||"px",g=s.style,m=v.test(r),y="svg"===t.tagName.toLowerCase(),x=(y?"client":"offset")+(m?"Width":"Height"),b="px"===o,_="%"===o;if(o===h||!d||G[o]||G[h])return d;if("px"===h||b||(d=e(t,r,i,"px")),p=t.getCTM&&V(t),(_||"%"===h)&&(f[r]||~r.indexOf("adius")))return a=p?t.getBBox()[m?"width":"height"]:t[x],(0,u.E_)(_?d/a*100:d/100*a);if(g[m?"width":"height"]=100+(b?h:o),l="rem"!==o&&~r.indexOf("adius")||"em"===o&&t.appendChild&&!y?t:t.parentNode,p&&(l=(t.ownerSVGElement||{}).parentNode),l&&l!==n&&l.appendChild||(l=n.body),(c=l._gsap)&&_&&c.width&&m&&c.time===u.au.time&&!c.uncache)return(0,u.E_)(d/c.width*100);if(_&&("height"===r||"width"===r)){var w=t.style[r];t.style[r]=100+o,a=t[x],w?t.style[r]=w:U(t,r)}else(_||"%"===h)&&!J[B(l,"display")]&&(g.position=B(t,"position")),l===t&&(g.position="static"),l.appendChild(s),a=s[x],l.removeChild(s),g.position="absolute";return m&&_&&((c=(0,u.a0)(l)).time=u.au.time,c.width=l[x]),(0,u.E_)(b?a*d/100:a&&d?100/a*d:0)},$=function(e,t,r,n){var i;return o||L(),t in y&&"transform"!==t&&~(t=y[t]).indexOf(",")&&(t=t.split(",")[0]),f[t]&&"transform"!==t?(i=ec(e,n),i="transformOrigin"!==t?i[t]:i.svg?i.origin:eu(B(e,Y))+" "+i.zOrigin+"px"):(!(i=e.style[t])||"auto"===i||n||~(i+"").indexOf("calc("))&&(i=er[t]&&er[t](e,t,r)||B(e,t)||(0,u.n)(e,t)||+("opacity"===t)),r&&!~(i+"").trim().indexOf(" ")?Z(e,t,i,r)+r:i},K=function(e,t,r,n){if(!r||"none"===r){var i=N(t,e,1),o=i&&B(e,i,1);o&&o!==r?(t=i,r=o):"borderColor"===t&&(r=B(e,"borderTopColor"))}var s,a,l,c,f,p,d,h,g,v,m,y=new u.J7(this._pt,e.style,t,0,1,u.l1),x=0,b=0;if(y.b=r,y.e=n,r+="","var(--"===(n+="").substring(0,6)&&(n=B(e,n.substring(4,n.indexOf(")")))),"auto"===n&&(p=e.style[t],e.style[t]=n,n=B(e,t)||n,p?e.style[t]=p:U(e,t)),s=[r,n],(0,u.Uc)(s),r=s[0],n=s[1],l=r.match(u.vM)||[],(n.match(u.vM)||[]).length){for(;a=u.vM.exec(n);)d=a[0],g=n.substring(x,a.index),f?f=(f+1)%5:("rgba("===g.substr(-5)||"hsla("===g.substr(-5))&&(f=1),d!==(p=l[b++]||"")&&(c=parseFloat(p)||0,m=p.substr((c+"").length),"="===d.charAt(1)&&(d=(0,u.B0)(c,d)+m),h=parseFloat(d),v=d.substr((h+"").length),x=u.vM.lastIndex-v.length,v||(v=v||u.Yz.units[t]||m,x===n.length&&(n+=v,y.e+=v)),m!==v&&(c=Z(e,t,p,v)||0),y._pt={_next:y._pt,p:g||1===b?g:",",s:c,c:h-c,m:f&&f<4||"zIndex"===t?Math.round:0});y.c=x<n.length?n.substring(x,n.length):""}else y.r="display"===t&&"none"===n?M:O;return u.Ks.test(n)&&(y.e=0),this._pt=y,y},Q={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},ee=function(e){var t=e.split(" "),r=t[0],n=t[1]||"50%";return("top"===r||"bottom"===r||"left"===n||"right"===n)&&(e=r,r=n,n=e),t[0]=Q[r]||r,t[1]=Q[n]||n,t.join(" ")},et=function(e,t){if(t.tween&&t.tween._time===t.tween._dur){var r,n,i,o=t.t,s=o.style,a=t.u,l=o._gsap;if("all"===a||!0===a)s.cssText="",n=1;else for(i=(a=a.split(",")).length;--i>-1;)f[r=a[i]]&&(n=1,r="transformOrigin"===r?Y:A),U(o,r);n&&(U(o,A),l&&(l.svg&&o.removeAttribute("transform"),s.scale=s.rotate=s.translate="none",ec(o,1),l.uncache=1,R(s)))}},er={clearProps:function(e,t,r,n,i){if("isFromStart"!==i.data){var o=e._pt=new u.J7(e._pt,t,r,0,0,et);return o.u=n,o.pr=-10,o.tween=i,e._props.push(r),1}}},en=[1,0,0,1,0,0],ei={},eo=function(e){return"matrix(1, 0, 0, 1, 0, 0)"===e||"none"===e||!e},es=function(e){var t=B(e,A);return eo(t)?en:t.substr(7).match(u.vX).map(u.E_)},ea=function(e,t){var r,n,o,s,a=e._gsap||(0,u.a0)(e),l=e.style,c=es(e);return a.svg&&e.getAttribute("transform")?"1,0,0,1,0,0"===(c=[(o=e.transform.baseVal.consolidate().matrix).a,o.b,o.c,o.d,o.e,o.f]).join(",")?en:c:(c!==en||e.offsetParent||e===i||a.svg||(o=l.display,l.display="block",(r=e.parentNode)&&(e.offsetParent||e.getBoundingClientRect().width)||(s=1,n=e.nextElementSibling,i.appendChild(e)),c=es(e),o?l.display=o:U(e,"display"),s&&(n?r.insertBefore(e,n):r?r.appendChild(e):i.removeChild(e))),t&&c.length>6?[c[0],c[1],c[4],c[5],c[12],c[13]]:c)},el=function(e,t,r,n,i,o){var s,a,l,c,u=e._gsap,f=i||ea(e,!0),p=u.xOrigin||0,d=u.yOrigin||0,h=u.xOffset||0,g=u.yOffset||0,v=f[0],m=f[1],y=f[2],x=f[3],b=f[4],_=f[5],w=t.split(" "),O=parseFloat(w[0])||0,M=parseFloat(w[1])||0;r?f!==en&&(a=v*x-m*y)&&(l=x/a*O+-y/a*M+(y*_-x*b)/a,c=-m/a*O+v/a*M-(v*_-m*b)/a,O=l,M=c):(O=(s=q(e)).x+(~w[0].indexOf("%")?O/100*s.width:O),M=s.y+(~(w[1]||w[0]).indexOf("%")?M/100*s.height:M)),n||!1!==n&&u.smooth?(u.xOffset=h+((b=O-p)*v+(_=M-d)*y)-b,u.yOffset=g+(b*m+_*x)-_):u.xOffset=u.yOffset=0,u.xOrigin=O,u.yOrigin=M,u.smooth=!!n,u.origin=t,u.originIsAbsolute=!!r,e.style[Y]="0px 0px",o&&(j(o,u,"xOrigin",p,O),j(o,u,"yOrigin",d,M),j(o,u,"xOffset",h,u.xOffset),j(o,u,"yOffset",g,u.yOffset)),e.setAttribute("data-svg-origin",O+" "+M)},ec=function(e,t){var r=e._gsap||new u.n6(e);if("x"in r&&!t&&!r.uncache)return r;var n,i,o,s,a,l,f,g,v,m,y,x,b,_,w,O,M,E,C,P,T,k,S,z,R,D,F,X,I,N,L,H,W=e.style,q=r.scaleX<0,U=getComputedStyle(e),j=B(e,Y)||"0";return n=i=o=l=f=g=v=m=y=0,s=a=1,r.svg=!!(e.getCTM&&V(e)),U.translate&&(("none"!==U.translate||"none"!==U.scale||"none"!==U.rotate)&&(W[A]=("none"!==U.translate?"translate3d("+(U.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+("none"!==U.rotate?"rotate("+U.rotate+") ":"")+("none"!==U.scale?"scale("+U.scale.split(" ").join(",")+") ":"")+("none"!==U[A]?U[A]:"")),W.scale=W.rotate=W.translate="none"),_=ea(e,r.svg),r.svg&&(r.uncache?(R=e.getBBox(),j=r.xOrigin-R.x+"px "+(r.yOrigin-R.y)+"px",z=""):z=!t&&e.getAttribute("data-svg-origin"),el(e,z||j,!!z||r.originIsAbsolute,!1!==r.smooth,_)),x=r.xOrigin||0,b=r.yOrigin||0,_!==en&&(E=_[0],C=_[1],P=_[2],T=_[3],n=k=_[4],i=S=_[5],6===_.length?(s=Math.sqrt(E*E+C*C),a=Math.sqrt(T*T+P*P),l=E||C?h(C,E)*p:0,(v=P||T?h(P,T)*p+l:0)&&(a*=Math.abs(Math.cos(v*d))),r.svg&&(n-=x-(x*E+b*P),i-=b-(x*C+b*T))):(H=_[6],N=_[7],F=_[8],X=_[9],I=_[10],L=_[11],n=_[12],i=_[13],o=_[14],f=(w=h(H,I))*p,w&&(z=k*(O=Math.cos(-w))+F*(M=Math.sin(-w)),R=S*O+X*M,D=H*O+I*M,F=-(k*M)+F*O,X=-(S*M)+X*O,I=-(H*M)+I*O,L=-(N*M)+L*O,k=z,S=R,H=D),g=(w=h(-P,I))*p,w&&(z=E*(O=Math.cos(-w))-F*(M=Math.sin(-w)),R=C*O-X*M,D=P*O-I*M,L=T*M+L*O,E=z,C=R,P=D),l=(w=h(C,E))*p,w&&(z=E*(O=Math.cos(w))+C*(M=Math.sin(w)),R=k*O+S*M,C=C*O-E*M,S=S*O-k*M,E=z,k=R),f&&Math.abs(f)+Math.abs(l)>359.9&&(f=l=0,g=180-g),s=(0,u.E_)(Math.sqrt(E*E+C*C+P*P)),a=(0,u.E_)(Math.sqrt(S*S+H*H)),v=Math.abs(w=h(k,S))>2e-4?w*p:0,y=L?1/(L<0?-L:L):0),r.svg&&(z=e.getAttribute("transform"),r.forceCSS=e.setAttribute("transform","")||!eo(B(e,A)),z&&e.setAttribute("transform",z))),Math.abs(v)>90&&270>Math.abs(v)&&(q?(s*=-1,v+=l<=0?180:-180,l+=l<=0?180:-180):(a*=-1,v+=v<=0?180:-180)),t=t||r.uncache,r.x=n-((r.xPercent=n&&(!t&&r.xPercent||(Math.round(e.offsetWidth/2)===Math.round(-n)?-50:0)))?e.offsetWidth*r.xPercent/100:0)+"px",r.y=i-((r.yPercent=i&&(!t&&r.yPercent||(Math.round(e.offsetHeight/2)===Math.round(-i)?-50:0)))?e.offsetHeight*r.yPercent/100:0)+"px",r.z=o+"px",r.scaleX=(0,u.E_)(s),r.scaleY=(0,u.E_)(a),r.rotation=(0,u.E_)(l)+"deg",r.rotationX=(0,u.E_)(f)+"deg",r.rotationY=(0,u.E_)(g)+"deg",r.skewX=v+"deg",r.skewY=m+"deg",r.transformPerspective=y+"px",(r.zOrigin=parseFloat(j.split(" ")[2])||!t&&r.zOrigin||0)&&(W[Y]=eu(j)),r.xOffset=r.yOffset=0,r.force3D=u.Yz.force3D,r.renderTransform=r.svg?eg:c?eh:ep,r.uncache=0,r},eu=function(e){return(e=e.split(" "))[0]+" "+e[1]},ef=function(e,t,r){var n=(0,u.l_)(t);return(0,u.E_)(parseFloat(t)+parseFloat(Z(e,"x",r+"px",n)))+n},ep=function(e,t){t.z="0px",t.rotationY=t.rotationX="0deg",t.force3D=0,eh(e,t)},ed="0deg",eh=function(e,t){var r=t||this,n=r.xPercent,i=r.yPercent,o=r.x,s=r.y,a=r.z,l=r.rotation,c=r.rotationY,u=r.rotationX,f=r.skewX,p=r.skewY,h=r.scaleX,g=r.scaleY,v=r.transformPerspective,m=r.force3D,y=r.target,x=r.zOrigin,b="",_="auto"===m&&e&&1!==e||!0===m;if(x&&(u!==ed||c!==ed)){var w,O=parseFloat(c)*d,M=Math.sin(O),E=Math.cos(O);o=ef(y,o,-(M*(w=Math.cos(O=parseFloat(u)*d))*x)),s=ef(y,s,-(-Math.sin(O)*x)),a=ef(y,a,-(E*w*x)+x)}"0px"!==v&&(b+="perspective("+v+") "),(n||i)&&(b+="translate("+n+"%, "+i+"%) "),(_||"0px"!==o||"0px"!==s||"0px"!==a)&&(b+="0px"!==a||_?"translate3d("+o+", "+s+", "+a+") ":"translate("+o+", "+s+") "),l!==ed&&(b+="rotate("+l+") "),c!==ed&&(b+="rotateY("+c+") "),u!==ed&&(b+="rotateX("+u+") "),(f!==ed||p!==ed)&&(b+="skew("+f+", "+p+") "),(1!==h||1!==g)&&(b+="scale("+h+", "+g+") "),y.style[A]=b||"translate(0, 0)"},eg=function(e,t){var r,n,i,o,s,a=t||this,l=a.xPercent,c=a.yPercent,f=a.x,p=a.y,h=a.rotation,g=a.skewX,v=a.skewY,m=a.scaleX,y=a.scaleY,x=a.target,b=a.xOrigin,_=a.yOrigin,w=a.xOffset,O=a.yOffset,M=a.forceCSS,E=parseFloat(f),C=parseFloat(p);h=parseFloat(h),g=parseFloat(g),(v=parseFloat(v))&&(g+=v=parseFloat(v),h+=v),h||g?(h*=d,g*=d,r=Math.cos(h)*m,n=Math.sin(h)*m,i=-(Math.sin(h-g)*y),o=Math.cos(h-g)*y,g&&(v*=d,i*=s=Math.sqrt(1+(s=Math.tan(g-v))*s),o*=s,v&&(r*=s=Math.sqrt(1+(s=Math.tan(v))*s),n*=s)),r=(0,u.E_)(r),n=(0,u.E_)(n),i=(0,u.E_)(i),o=(0,u.E_)(o)):(r=m,o=y,n=i=0),(E&&!~(f+"").indexOf("px")||C&&!~(p+"").indexOf("px"))&&(E=Z(x,"x",f,"px"),C=Z(x,"y",p,"px")),(b||_||w||O)&&(E=(0,u.E_)(E+b-(b*r+_*i)+w),C=(0,u.E_)(C+_-(b*n+_*o)+O)),(l||c)&&(s=x.getBBox(),E=(0,u.E_)(E+l/100*s.width),C=(0,u.E_)(C+c/100*s.height)),s="matrix("+r+","+n+","+i+","+o+","+E+","+C+")",x.setAttribute("transform",s),M&&(x.style[A]=s)},ev=function(e,t,r,n,i){var o,s,a=(0,u.vQ)(i),l=parseFloat(i)*(a&&~i.indexOf("rad")?p:1)-n,c=n+l+"deg";return a&&("short"===(o=i.split("_")[1])&&(l%=360)!=l%180&&(l+=l<0?360:-360),"cw"===o&&l<0?l=(l+36e9)%360-360*~~(l/360):"ccw"===o&&l>0&&(l=(l-36e9)%360-360*~~(l/360))),e._pt=s=new u.J7(e._pt,t,r,n,l,b),s.e=c,s.u="deg",e._props.push(r),s},em=function(e,t){for(var r in t)e[r]=t[r];return e},ey=function(e,t,r){var n,i,o,s,a,l,c,p=em({},r._gsap),d=r.style;for(i in p.svg?(o=r.getAttribute("transform"),r.setAttribute("transform",""),d[A]=t,n=ec(r,1),U(r,A),r.setAttribute("transform",o)):(o=getComputedStyle(r)[A],d[A]=t,n=ec(r,1),d[A]=o),f)(o=p[i])!==(s=n[i])&&0>"perspective,force3D,transformOrigin,svgOrigin".indexOf(i)&&(a=(0,u.l_)(o)!==(c=(0,u.l_)(s))?Z(r,i,o,c):parseFloat(o),l=parseFloat(s),e._pt=new u.J7(e._pt,n,i,a,l-a,x),e._pt.u=c||0,e._props.push(i));em(n,p)};(0,u.fA)("padding,margin,Width,Radius",function(e,t){var r="Right",n="Bottom",i="Left",o=(t<3?["Top",r,n,i]:["Top"+i,"Top"+r,n+r,n+i]).map(function(r){return t<2?e+r:"border"+r+e});er[t>1?"border"+e:e]=function(e,t,r,n,i){var s,a;if(arguments.length<4)return 5===(a=(s=o.map(function(t){return $(e,t,r)})).join(" ")).split(s[0]).length?s[0]:a;s=(n+"").split(" "),a={},o.forEach(function(e,t){return a[e]=s[t]=s[t]||s[(t-1)/2|0]}),e.init(t,a,i)}});var ex={name:"css",register:L,targetTest:function(e){return e.style&&e.nodeType},init:function(e,t,r,n,i){var s,a,l,c,p,d,h,g,v,b,O,M,E,C,P,T,k=this._props,S=e.style,z=r.vars.startAt;for(h in o||L(),this.styles=this.styles||F(e),T=this.styles.props,this.tween=r,t)if("autoRound"!==h&&(a=t[h],!(u.wU[h]&&(0,u.Zm)(h,t,r,n,e,i)))){if(p=typeof a,d=er[h],"function"===p&&(p=typeof(a=a.call(r,n,e,i))),"string"===p&&~a.indexOf("random(")&&(a=(0,u.Vy)(a)),d)d(this,e,h,a,r)&&(P=1);else if("--"===h.substr(0,2))s=(getComputedStyle(e).getPropertyValue(h)+"").trim(),a+="",u.qA.lastIndex=0,u.qA.test(s)||(g=(0,u.l_)(s),v=(0,u.l_)(a)),v?g!==v&&(s=Z(e,h,s,v)+v):g&&(a+=g),this.add(S,"setProperty",s,a,n,i,0,0,h),k.push(h),T.push(h,0,S[h]);else if("undefined"!==p){if(z&&h in z?(s="function"==typeof z[h]?z[h].call(r,n,e,i):z[h],(0,u.vQ)(s)&&~s.indexOf("random(")&&(s=(0,u.Vy)(s)),(0,u.l_)(s+"")||"auto"===s||(s+=u.Yz.units[h]||(0,u.l_)($(e,h))||""),"="===(s+"").charAt(1)&&(s=$(e,h))):s=$(e,h),c=parseFloat(s),(b="string"===p&&"="===a.charAt(1)&&a.substr(0,2))&&(a=a.substr(2)),l=parseFloat(a),h in y&&("autoAlpha"===h&&(1===c&&"hidden"===$(e,"visibility")&&l&&(c=0),T.push("visibility",0,S.visibility),j(this,S,"visibility",c?"inherit":"hidden",l?"inherit":"hidden",!l)),"scale"!==h&&"transform"!==h&&~(h=y[h]).indexOf(",")&&(h=h.split(",")[0])),O=h in f){if(this.styles.save(h),"string"===p&&"var(--"===a.substring(0,6)&&(l=parseFloat(a=B(e,a.substring(4,a.indexOf(")"))))),M||((E=e._gsap).renderTransform&&!t.parseTransform||ec(e,t.parseTransform),C=!1!==t.smoothOrigin&&E.smooth,(M=this._pt=new u.J7(this._pt,S,A,0,1,E.renderTransform,E,0,-1)).dep=1),"scale"===h)this._pt=new u.J7(this._pt,E,"scaleY",E.scaleY,(b?(0,u.B0)(E.scaleY,b+l):l)-E.scaleY||0,x),this._pt.u=0,k.push("scaleY",h),h+="X";else if("transformOrigin"===h){T.push(Y,0,S[Y]),a=ee(a),E.svg?el(e,a,0,C,0,this):((v=parseFloat(a.split(" ")[2])||0)!==E.zOrigin&&j(this,E,"zOrigin",E.zOrigin,v),j(this,S,h,eu(s),eu(a)));continue}else if("svgOrigin"===h){el(e,a,1,C,0,this);continue}else if(h in ei){ev(this,E,h,c,b?(0,u.B0)(c,b+a):a);continue}else if("smoothOrigin"===h){j(this,E,"smooth",E.smooth,a);continue}else if("force3D"===h){E[h]=a;continue}else if("transform"===h){ey(this,a,e);continue}}else h in S||(h=N(h)||h);if(O||(l||0===l)&&(c||0===c)&&!m.test(a)&&h in S)g=(s+"").substr((c+"").length),l||(l=0),v=(0,u.l_)(a)||(h in u.Yz.units?u.Yz.units[h]:g),g!==v&&(c=Z(e,h,s,v)),this._pt=new u.J7(this._pt,O?E:S,h,c,(b?(0,u.B0)(c,b+l):l)-c,!O&&("px"===v||"zIndex"===h)&&!1!==t.autoRound?w:x),this._pt.u=v||0,g!==v&&"%"!==v&&(this._pt.b=s,this._pt.r=_);else if(h in S)K.call(this,e,h,s,b?b+a:a);else if(h in e)this.add(e,h,s||e[h],b?b+a:a,n,i);else if("parseTransform"!==h){(0,u.dg)(h,a);continue}O||(h in S?T.push(h,0,S[h]):"function"==typeof e[h]?T.push(h,2,e[h]()):T.push(h,1,s||e[h])),k.push(h)}}P&&(0,u.St)(this)},render:function(e,t){if(t.tween._time||!l())for(var r=t._pt;r;)r.r(e,r.d),r=r._next;else t.styles.revert()},get:$,aliases:y,getSetter:function(e,t,r){var n=y[t];return n&&0>n.indexOf(",")&&(t=n),t in f&&t!==Y&&(e._gsap.x||$(e,"x"))?r&&a===r?"scale"===t?T:P:(a=r||{},"scale"===t?k:S):e.style&&!(0,u.OF)(e.style[t])?E:~t.indexOf("-")?C:(0,u.Dx)(e,t)},core:{_removeProperty:U,_getMatrix:ea}};u.os.utils.checkPrefix=N,u.os.core.getStyleSaver=F,function(e,t,r,n){var i=(0,u.fA)(e+","+t+","+r,function(e){f[e]=1});(0,u.fA)(t,function(e){u.Yz.units[e]="deg",ei[e]=1}),y[i[13]]=e+","+t,(0,u.fA)(n,function(e){var t=e.split(":");y[t[1]]=i[t[0]]})}("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY"),(0,u.fA)("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(e){u.Yz.units[e]="px"}),u.os.registerPlugin(ex);var eb=u.os.registerPlugin(ex)||u.os;eb.core.Tween},9088:(e,t,r)=>{r.d(t,{A:()=>t1});var n,i,o,s,a,l,c,u,f,p,d,h,g,v=function(){return n||"undefined"!=typeof window&&(n=window.gsap)&&n.registerPlugin&&n},m=1,y=[],x=[],b=[],_=Date.now,w=function(e,t){return t},O=function(){var e=f.core,t=e.bridge||{},r=e._scrollers,n=e._proxies;r.push.apply(r,x),n.push.apply(n,b),x=r,b=n,w=function(e,r){return t[e](r)}},M=function(e,t){return~b.indexOf(e)&&b[b.indexOf(e)+1][t]},E=function(e){return!!~p.indexOf(e)},C=function(e,t,r,n,i){return e.addEventListener(t,r,{passive:!1!==n,capture:!!i})},P=function(e,t,r,n){return e.removeEventListener(t,r,!!n)},T="scrollLeft",k="scrollTop",S=function(){return d&&d.isPressed||x.cache++},A=function(e,t){var r=function r(n){if(n||0===n){m&&(o.history.scrollRestoration="manual");var i=d&&d.isPressed;e(n=r.v=Math.round(n)||(d&&d.iOS?1:0)),r.cacheID=x.cache,i&&w("ss",n)}else(t||x.cache!==r.cacheID||w("ref"))&&(r.cacheID=x.cache,r.v=e());return r.v+r.offset};return r.offset=0,e&&r},Y={s:T,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:A(function(e){return arguments.length?o.scrollTo(e,z.sc()):o.pageXOffset||s[T]||a[T]||l[T]||0})},z={s:k,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:Y,sc:A(function(e){return arguments.length?o.scrollTo(Y.sc(),e):o.pageYOffset||s[k]||a[k]||l[k]||0})},R=function(e,t){return(t&&t._ctx&&t._ctx.selector||n.utils.toArray)(e)[0]||("string"==typeof e&&!1!==n.config().nullTargetWarn?console.warn("Element not found:",e):null)},D=function(e,t){for(var r=t.length;r--;)if(t[r]===e||t[r].contains(e))return!0;return!1},F=function(e,t){var r=t.s,i=t.sc;E(e)&&(e=s.scrollingElement||a);var o=x.indexOf(e),l=i===z.sc?1:2;~o||(o=x.push(e)-1),x[o+l]||C(e,"scroll",S);var c=x[o+l],u=c||(x[o+l]=A(M(e,r),!0)||(E(e)?i:A(function(t){return arguments.length?e[r]=t:e[r]})));return u.target=e,c||(u.smooth="smooth"===n.getProperty(e,"scrollBehavior")),u},X=function(e,t,r){var n=e,i=e,o=_(),s=o,a=t||50,l=Math.max(500,3*a),c=function(e,t){var l=_();t||l-o>a?(i=n,n=e,s=o,o=l):r?n+=e:n=i+(e-i)/(l-s)*(o-s)};return{update:c,reset:function(){i=n=r?0:n,s=o=0},getVelocity:function(e){var t=s,a=i,u=_();return(e||0===e)&&e!==n&&c(e),o===s||u-s>l?0:(n+(r?a:-a))/((r?u:o)-t)*1e3}}},B=function(e,t){return t&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e},I=function(e){var t=Math.max.apply(Math,e),r=Math.min.apply(Math,e);return Math.abs(t)>=Math.abs(r)?t:r},N=function(){(f=n.core.globals().ScrollTrigger)&&f.core&&O()},L=function(e){return n=e||v(),!i&&n&&"undefined"!=typeof document&&document.body&&(o=window,a=(s=document).documentElement,l=s.body,p=[o,s,a,l],n.utils.clamp,g=n.core.context||function(){},u="onpointerenter"in l?"pointer":"mouse",c=H.isTouch=o.matchMedia&&o.matchMedia("(hover: none), (pointer: coarse)").matches?1:2*("ontouchstart"in o||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0),h=H.eventTypes=("ontouchstart"in a?"touchstart,touchmove,touchcancel,touchend":!("onpointerdown"in a)?"mousedown,mousemove,mouseup,mouseup":"pointerdown,pointermove,pointercancel,pointerup").split(","),setTimeout(function(){return m=0},500),N(),i=1),i};Y.op=z,x.cache=0;var H=function(){var e;function t(e){this.init(e)}return t.prototype.init=function(e){i||L(n)||console.warn("Please gsap.registerPlugin(Observer)"),f||N();var t=e.tolerance,r=e.dragMinimum,p=e.type,v=e.target,m=e.lineHeight,x=e.debounce,b=e.preventDefault,w=e.onStop,O=e.onStopDelay,M=e.ignore,T=e.wheelSpeed,k=e.event,A=e.onDragStart,H=e.onDragEnd,W=e.onDrag,q=e.onPress,V=e.onRelease,U=e.onRight,j=e.onLeft,G=e.onUp,J=e.onDown,Z=e.onChangeX,$=e.onChangeY,K=e.onChange,Q=e.onToggleX,ee=e.onToggleY,et=e.onHover,er=e.onHoverEnd,en=e.onMove,ei=e.ignoreCheck,eo=e.isNormalizer,es=e.onGestureStart,ea=e.onGestureEnd,el=e.onWheel,ec=e.onEnable,eu=e.onDisable,ef=e.onClick,ep=e.scrollSpeed,ed=e.capture,eh=e.allowClicks,eg=e.lockAxis,ev=e.onLockAxis;this.target=v=R(v)||a,this.vars=e,M&&(M=n.utils.toArray(M)),t=t||1e-9,r=r||0,T=T||1,ep=ep||1,p=p||"wheel,touch,pointer",x=!1!==x,m||(m=parseFloat(o.getComputedStyle(l).lineHeight)||22);var em,ey,ex,eb,e_,ew,eO,eM=this,eE=0,eC=0,eP=e.passive||!b&&!1!==e.passive,eT=F(v,Y),ek=F(v,z),eS=eT(),eA=ek(),eY=~p.indexOf("touch")&&!~p.indexOf("pointer")&&"pointerdown"===h[0],ez=E(v),eR=v.ownerDocument||s,eD=[0,0,0],eF=[0,0,0],eX=0,eB=function(){return eX=_()},eI=function(e,t){return(eM.event=e)&&M&&D(e.target,M)||t&&eY&&"touch"!==e.pointerType||ei&&ei(e,t)},eN=function(){var e=eM.deltaX=I(eD),r=eM.deltaY=I(eF),n=Math.abs(e)>=t,i=Math.abs(r)>=t;K&&(n||i)&&K(eM,e,r,eD,eF),n&&(U&&eM.deltaX>0&&U(eM),j&&eM.deltaX<0&&j(eM),Z&&Z(eM),Q&&eM.deltaX<0!=eE<0&&Q(eM),eE=eM.deltaX,eD[0]=eD[1]=eD[2]=0),i&&(J&&eM.deltaY>0&&J(eM),G&&eM.deltaY<0&&G(eM),$&&$(eM),ee&&eM.deltaY<0!=eC<0&&ee(eM),eC=eM.deltaY,eF[0]=eF[1]=eF[2]=0),(eb||ex)&&(en&&en(eM),ex&&(A&&1===ex&&A(eM),W&&W(eM),ex=0),eb=!1),ew&&(ew=!1,1)&&ev&&ev(eM),e_&&(el(eM),e_=!1),em=0},eL=function(e,t,r){eD[r]+=e,eF[r]+=t,eM._vx.update(e),eM._vy.update(t),x?em||(em=requestAnimationFrame(eN)):eN()},eH=function(e,t){eg&&!eO&&(eM.axis=eO=Math.abs(e)>Math.abs(t)?"x":"y",ew=!0),"y"!==eO&&(eD[2]+=e,eM._vx.update(e,!0)),"x"!==eO&&(eF[2]+=t,eM._vy.update(t,!0)),x?em||(em=requestAnimationFrame(eN)):eN()},eW=function(e){if(!eI(e,1)){var t=(e=B(e,b)).clientX,n=e.clientY,i=t-eM.x,o=n-eM.y,s=eM.isDragging;eM.x=t,eM.y=n,(s||(i||o)&&(Math.abs(eM.startX-t)>=r||Math.abs(eM.startY-n)>=r))&&(ex=s?2:1,s||(eM.isDragging=!0),eH(i,o))}},eq=eM.onPress=function(e){eI(e,1)||e&&e.button||(eM.axis=eO=null,ey.pause(),eM.isPressed=!0,e=B(e),eE=eC=0,eM.startX=eM.x=e.clientX,eM.startY=eM.y=e.clientY,eM._vx.reset(),eM._vy.reset(),C(eo?v:eR,h[1],eW,eP,!0),eM.deltaX=eM.deltaY=0,q&&q(eM))},eV=eM.onRelease=function(e){if(!eI(e,1)){P(eo?v:eR,h[1],eW,!0);var t=!isNaN(eM.y-eM.startY),r=eM.isDragging,i=r&&(Math.abs(eM.x-eM.startX)>3||Math.abs(eM.y-eM.startY)>3),s=B(e);!i&&t&&(eM._vx.reset(),eM._vy.reset(),b&&eh&&n.delayedCall(.08,function(){if(_()-eX>300&&!e.defaultPrevented){if(e.target.click)e.target.click();else if(eR.createEvent){var t=eR.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,o,1,s.screenX,s.screenY,s.clientX,s.clientY,!1,!1,!1,!1,0,null),e.target.dispatchEvent(t)}}})),eM.isDragging=eM.isGesturing=eM.isPressed=!1,w&&r&&!eo&&ey.restart(!0),ex&&eN(),H&&r&&H(eM),V&&V(eM,i)}},eU=function(e){return e.touches&&e.touches.length>1&&(eM.isGesturing=!0)&&es(e,eM.isDragging)},ej=function(){return eM.isGesturing=!1,ea(eM)},eG=function(e){if(!eI(e)){var t=eT(),r=ek();eL((t-eS)*ep,(r-eA)*ep,1),eS=t,eA=r,w&&ey.restart(!0)}},eJ=function(e){if(!eI(e)){e=B(e,b),el&&(e_=!0);var t=(1===e.deltaMode?m:2===e.deltaMode?o.innerHeight:1)*T;eL(e.deltaX*t,e.deltaY*t,0),w&&!eo&&ey.restart(!0)}},eZ=function(e){if(!eI(e)){var t=e.clientX,r=e.clientY,n=t-eM.x,i=r-eM.y;eM.x=t,eM.y=r,eb=!0,w&&ey.restart(!0),(n||i)&&eH(n,i)}},e$=function(e){eM.event=e,et(eM)},eK=function(e){eM.event=e,er(eM)},eQ=function(e){return eI(e)||B(e,b)&&ef(eM)};ey=eM._dc=n.delayedCall(O||.25,function(){eM._vx.reset(),eM._vy.reset(),ey.pause(),w&&w(eM)}).pause(),eM.deltaX=eM.deltaY=0,eM._vx=X(0,50,!0),eM._vy=X(0,50,!0),eM.scrollX=eT,eM.scrollY=ek,eM.isDragging=eM.isGesturing=eM.isPressed=!1,g(this),eM.enable=function(e){return!eM.isEnabled&&(C(ez?eR:v,"scroll",S),p.indexOf("scroll")>=0&&C(ez?eR:v,"scroll",eG,eP,ed),p.indexOf("wheel")>=0&&C(v,"wheel",eJ,eP,ed),(p.indexOf("touch")>=0&&c||p.indexOf("pointer")>=0)&&(C(v,h[0],eq,eP,ed),C(eR,h[2],eV),C(eR,h[3],eV),eh&&C(v,"click",eB,!0,!0),ef&&C(v,"click",eQ),es&&C(eR,"gesturestart",eU),ea&&C(eR,"gestureend",ej),et&&C(v,u+"enter",e$),er&&C(v,u+"leave",eK),en&&C(v,u+"move",eZ)),eM.isEnabled=!0,eM.isDragging=eM.isGesturing=eM.isPressed=eb=ex=!1,eM._vx.reset(),eM._vy.reset(),eS=eT(),eA=ek(),e&&e.type&&eq(e),ec&&ec(eM)),eM},eM.disable=function(){eM.isEnabled&&(y.filter(function(e){return e!==eM&&E(e.target)}).length||P(ez?eR:v,"scroll",S),eM.isPressed&&(eM._vx.reset(),eM._vy.reset(),P(eo?v:eR,h[1],eW,!0)),P(ez?eR:v,"scroll",eG,ed),P(v,"wheel",eJ,ed),P(v,h[0],eq,ed),P(eR,h[2],eV),P(eR,h[3],eV),P(v,"click",eB,!0),P(v,"click",eQ),P(eR,"gesturestart",eU),P(eR,"gestureend",ej),P(v,u+"enter",e$),P(v,u+"leave",eK),P(v,u+"move",eZ),eM.isEnabled=eM.isPressed=eM.isDragging=!1,eu&&eu(eM))},eM.kill=eM.revert=function(){eM.disable();var e=y.indexOf(eM);e>=0&&y.splice(e,1),d===eM&&(d=0)},y.push(eM),eo&&E(v)&&(d=eM),eM.enable(k)},e=[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,e),t}();H.version="3.13.0",H.create=function(e){return new H(e)},H.register=L,H.getAll=function(){return y.slice()},H.getById=function(e){return y.filter(function(t){return t.vars.id===e})[0]},v()&&n.registerPlugin(H);var W,q,V,U,j,G,J,Z,$,K,Q,ee,et,er,en,ei,eo,es,ea,el,ec,eu,ef,ep,ed,eh,eg,ev,em,ey,ex,eb,e_,ew,eO,eM,eE,eC,eP=1,eT=Date.now,ek=eT(),eS=0,eA=0,eY=function(e,t,r){var n=eU(e)&&("clamp("===e.substr(0,6)||e.indexOf("max")>-1);return r["_"+t+"Clamp"]=n,n?e.substr(6,e.length-7):e},ez=function(e,t){return t&&(!eU(e)||"clamp("!==e.substr(0,6))?"clamp("+e+")":e},eR=function(){return er=1},eD=function(){return er=0},eF=function(e){return e},eX=function(e){return Math.round(1e5*e)/1e5||0},eB=function(){return"undefined"!=typeof window},eI=function(){return W||eB()&&(W=window.gsap)&&W.registerPlugin&&W},eN=function(e){return!!~J.indexOf(e)},eL=function(e){return("Height"===e?ex:V["inner"+e])||j["client"+e]||G["client"+e]},eH=function(e){return M(e,"getBoundingClientRect")||(eN(e)?function(){return tG.width=V.innerWidth,tG.height=ex,tG}:function(){return ti(e)})},eW=function(e,t,r){var n=r.d,i=r.d2,o=r.a;return(o=M(e,"getBoundingClientRect"))?function(){return o()[n]}:function(){return(t?eL(i):e["client"+i])||0}},eq=function(e,t){var r=t.s,n=t.d2,i=t.d,o=t.a;return Math.max(0,(o=M(e,r="scroll"+n))?o()-eH(e)()[i]:eN(e)?(j[r]||G[r])-eL(n):e[r]-e["offset"+n])},eV=function(e,t){for(var r=0;r<ea.length;r+=3)(!t||~t.indexOf(ea[r+1]))&&e(ea[r],ea[r+1],ea[r+2])},eU=function(e){return"string"==typeof e},ej=function(e){return"function"==typeof e},eG=function(e){return"number"==typeof e},eJ=function(e){return"object"==typeof e},eZ=function(e,t,r){return e&&e.progress(+!t)&&r&&e.pause()},e$=function(e,t){if(e.enabled){var r=e._ctx?e._ctx.add(function(){return t(e)}):t(e);r&&r.totalTime&&(e.callbackAnimation=r)}},eK=Math.abs,eQ="left",e0="right",e1="bottom",e2="width",e3="height",e5="Right",e6="Left",e4="Bottom",e8="padding",e9="margin",e7="Width",te="Height",tt=function(e){return V.getComputedStyle(e)},tr=function(e){var t=tt(e).position;e.style.position="absolute"===t||"fixed"===t?t:"relative"},tn=function(e,t){for(var r in t)r in e||(e[r]=t[r]);return e},ti=function(e,t){var r=t&&"matrix(1, 0, 0, 1, 0, 0)"!==tt(e)[en]&&W.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),n=e.getBoundingClientRect();return r&&r.progress(0).kill(),n},to=function(e,t){var r=t.d2;return e["offset"+r]||e["client"+r]||0},ts=function(e){var t,r=[],n=e.labels,i=e.duration();for(t in n)r.push(n[t]/i);return r},ta=function(e){var t=W.utils.snap(e),r=Array.isArray(e)&&e.slice(0).sort(function(e,t){return e-t});return r?function(e,n,i){var o;if(void 0===i&&(i=.001),!n)return t(e);if(n>0){for(e-=i,o=0;o<r.length;o++)if(r[o]>=e)return r[o];return r[o-1]}for(o=r.length,e+=i;o--;)if(r[o]<=e)return r[o];return r[0]}:function(r,n,i){void 0===i&&(i=.001);var o=t(r);return!n||Math.abs(o-r)<i||o-r<0==n<0?o:t(n<0?r-e:r+e)}},tl=function(e,t,r,n){return r.split(",").forEach(function(r){return e(t,r,n)})},tc=function(e,t,r,n,i){return e.addEventListener(t,r,{passive:!n,capture:!!i})},tu=function(e,t,r,n){return e.removeEventListener(t,r,!!n)},tf=function(e,t,r){(r=r&&r.wheelHandler)&&(e(t,"wheel",r),e(t,"touchmove",r))},tp={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},td={toggleActions:"play",anticipatePin:0},th={top:0,left:0,center:.5,bottom:1,right:1},tg=function(e,t){if(eU(e)){var r=e.indexOf("="),n=~r?(e.charAt(r-1)+1)*parseFloat(e.substr(r+1)):0;~r&&(e.indexOf("%")>r&&(n*=t/100),e=e.substr(0,r-1)),e=n+(e in th?th[e]*t:~e.indexOf("%")?parseFloat(e)*t/100:parseFloat(e)||0)}return e},tv=function(e,t,r,n,i,o,s,a){var l=i.startColor,c=i.endColor,u=i.fontSize,f=i.indent,p=i.fontWeight,d=U.createElement("div"),h=eN(r)||"fixed"===M(r,"pinType"),g=-1!==e.indexOf("scroller"),v=h?G:r,m=-1!==e.indexOf("start"),y=m?l:c,x="border-color:"+y+";font-size:"+u+";color:"+y+";font-weight:"+p+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return x+="position:"+((g||a)&&h?"fixed;":"absolute;"),(g||a||!h)&&(x+=(n===z?e0:e1)+":"+(o+parseFloat(f))+"px;"),s&&(x+="box-sizing:border-box;text-align:left;width:"+s.offsetWidth+"px;"),d._isStart=m,d.setAttribute("class","gsap-marker-"+e+(t?" marker-"+t:"")),d.style.cssText=x,d.innerText=t||0===t?e+"-"+t:e,v.children[0]?v.insertBefore(d,v.children[0]):v.appendChild(d),d._offset=d["offset"+n.op.d2],tm(d,0,n,m),d},tm=function(e,t,r,n){var i={display:"block"},o=r[n?"os2":"p2"],s=r[n?"p2":"os2"];e._isFlipped=n,i[r.a+"Percent"]=n?-100:0,i[r.a]=n?"1px":0,i["border"+o+e7]=1,i["border"+s+e7]=0,i[r.p]=t+"px",W.set(e,i)},ty=[],tx={},tb=function(){return eT()-eS>34&&(eO||(eO=requestAnimationFrame(tI)))},t_=function(){ef&&ef.isPressed&&!(ef.startX>G.clientWidth)||(x.cache++,ef?eO||(eO=requestAnimationFrame(tI)):tI(),eS||tP("scrollStart"),eS=eT())},tw=function(){eh=V.innerWidth,ed=V.innerHeight},tO=function(e){x.cache++,(!0===e||!et&&!eu&&!U.fullscreenElement&&!U.webkitFullscreenElement&&(!ep||eh!==V.innerWidth||Math.abs(V.innerHeight-ed)>.25*V.innerHeight))&&Z.restart(!0)},tM={},tE=[],tC=function e(){return tu(t1,"scrollEnd",e)||tF(!0)},tP=function(e){return tM[e]&&tM[e].map(function(e){return e()})||tE},tT=[],tk=function(e){for(var t=0;t<tT.length;t+=5)(!e||tT[t+4]&&tT[t+4].query===e)&&(tT[t].style.cssText=tT[t+1],tT[t].getBBox&&tT[t].setAttribute("transform",tT[t+2]||""),tT[t+3].uncache=1)},tS=function(e,t){var r;for(ei=0;ei<ty.length;ei++)(r=ty[ei])&&(!t||r._ctx===t)&&(e?r.kill(1):r.revert(!0,!0));eb=!0,t&&tk(t),t||tP("revert")},tA=function(e,t){x.cache++,(t||!eM)&&x.forEach(function(e){return ej(e)&&e.cacheID++&&(e.rec=0)}),eU(e)&&(V.history.scrollRestoration=em=e)},tY=0,tz=function(){if(eE!==tY){var e=eE=tY;requestAnimationFrame(function(){return e===tY&&tF(!0)})}},tR=function(){G.appendChild(ey),ex=!ef&&ey.offsetHeight||V.innerHeight,G.removeChild(ey)},tD=function(e){return $(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(t){return t.style.display=e?"none":"block"})},tF=function(e,t){if(j=U.documentElement,G=U.body,J=[V,U,j,G],eS&&!e&&!eb)return void tc(t1,"scrollEnd",tC);tR(),eM=t1.isRefreshing=!0,x.forEach(function(e){return ej(e)&&++e.cacheID&&(e.rec=e())});var r=tP("refreshInit");el&&t1.sort(),t||tS(),x.forEach(function(e){ej(e)&&(e.smooth&&(e.target.style.scrollBehavior="auto"),e(0))}),ty.slice(0).forEach(function(e){return e.refresh()}),eb=!1,ty.forEach(function(e){if(e._subPinOffset&&e.pin){var t=e.vars.horizontal?"offsetWidth":"offsetHeight",r=e.pin[t];e.revert(!0,1),e.adjustPinSpacing(e.pin[t]-r),e.refresh()}}),e_=1,tD(!0),ty.forEach(function(e){var t=eq(e.scroller,e._dir),r="max"===e.vars.end||e._endClamp&&e.end>t,n=e._startClamp&&e.start>=t;(r||n)&&e.setPositions(n?t-1:e.start,r?Math.max(n?t:e.start+1,t):e.end,!0)}),tD(!1),e_=0,r.forEach(function(e){return e&&e.render&&e.render(-1)}),x.forEach(function(e){ej(e)&&(e.smooth&&requestAnimationFrame(function(){return e.target.style.scrollBehavior="smooth"}),e.rec&&e(e.rec))}),tA(em,1),Z.pause(),tY++,eM=2,tI(2),ty.forEach(function(e){return ej(e.vars.onRefresh)&&e.vars.onRefresh(e)}),eM=t1.isRefreshing=!1,tP("refresh")},tX=0,tB=1,tI=function(e){if(2===e||!eM&&!eb){t1.isUpdating=!0,eC&&eC.update(0);var t=ty.length,r=eT(),n=r-ek>=50,i=t&&ty[0].scroll();if(tB=tX>i?-1:1,eM||(tX=i),n&&(eS&&!er&&r-eS>200&&(eS=0,tP("scrollEnd")),Q=ek,ek=r),tB<0){for(ei=t;ei-- >0;)ty[ei]&&ty[ei].update(0,n);tB=1}else for(ei=0;ei<t;ei++)ty[ei]&&ty[ei].update(0,n);t1.isUpdating=!1}eO=0},tN=[eQ,"top",e1,e0,e9+e4,e9+e5,e9+"Top",e9+e6,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],tL=tN.concat([e2,e3,"boxSizing","max"+e7,"max"+te,"position",e9,e8,e8+"Top",e8+e5,e8+e4,e8+e6]),tH=function(e,t,r){tV(r);var n=e._gsap;if(n.spacerIsNative)tV(n.spacerState);else if(e._gsap.swappedIn){var i=t.parentNode;i&&(i.insertBefore(e,t),i.removeChild(t))}e._gsap.swappedIn=!1},tW=function(e,t,r,n){if(!e._gsap.swappedIn){for(var i,o=tN.length,s=t.style,a=e.style;o--;)s[i=tN[o]]=r[i];s.position="absolute"===r.position?"absolute":"relative","inline"===r.display&&(s.display="inline-block"),a[e1]=a[e0]="auto",s.flexBasis=r.flexBasis||"auto",s.overflow="visible",s.boxSizing="border-box",s[e2]=to(e,Y)+"px",s[e3]=to(e,z)+"px",s[e8]=a[e9]=a.top=a[eQ]="0",tV(n),a[e2]=a["max"+e7]=r[e2],a[e3]=a["max"+te]=r[e3],a[e8]=r[e8],e.parentNode!==t&&(e.parentNode.insertBefore(t,e),t.appendChild(e)),e._gsap.swappedIn=!0}},tq=/([A-Z])/g,tV=function(e){if(e){var t,r,n=e.t.style,i=e.length,o=0;for((e.t._gsap||W.core.getCache(e.t)).uncache=1;o<i;o+=2)r=e[o+1],t=e[o],r?n[t]=r:n[t]&&n.removeProperty(t.replace(tq,"-$1").toLowerCase())}},tU=function(e){for(var t=tL.length,r=e.style,n=[],i=0;i<t;i++)n.push(tL[i],r[tL[i]]);return n.t=e,n},tj=function(e,t,r){for(var n,i=[],o=e.length,s=8*!!r;s<o;s+=2)n=e[s],i.push(n,n in t?t[n]:e[s+1]);return i.t=e.t,i},tG={left:0,top:0},tJ=function(e,t,r,n,i,o,s,a,l,c,u,f,p,d){ej(e)&&(e=e(a)),eU(e)&&"max"===e.substr(0,3)&&(e=f+("="===e.charAt(4)?tg("0"+e.substr(3),r):0));var h,g,v,m=p?p.time():0;if(p&&p.seek(0),isNaN(e)||(e*=1),eG(e))p&&(e=W.utils.mapRange(p.scrollTrigger.start,p.scrollTrigger.end,0,f,e)),s&&tm(s,r,n,!0);else{ej(t)&&(t=t(a));var y,x,b,_,w=(e||"0").split(" ");(y=ti(v=R(t,a)||G)||{}).left||y.top||"none"!==tt(v).display||(_=v.style.display,v.style.display="block",y=ti(v),_?v.style.display=_:v.style.removeProperty("display")),x=tg(w[0],y[n.d]),b=tg(w[1]||"0",r),e=y[n.p]-l[n.p]-c+x+i-b,s&&tm(s,b,n,r-b<20||s._isStart&&b>20),r-=r-b}if(d&&(a[d]=e||-.001,e<0&&(e=0)),o){var O=e+r,M=o._isStart;h="scroll"+n.d2,tm(o,O,n,M&&O>20||!M&&(u?Math.max(G[h],j[h]):o.parentNode[h])<=O+1),u&&(l=ti(s),u&&(o.style[n.op.p]=l[n.op.p]-n.op.m-o._offset+"px"))}return p&&v&&(h=ti(v),p.seek(f),g=ti(v),p._caScrollDist=h[n.p]-g[n.p],e=e/p._caScrollDist*f),p&&p.seek(m),p?e:Math.round(e)},tZ=/(webkit|moz|length|cssText|inset)/i,t$=function(e,t,r,n){if(e.parentNode!==t){var i,o,s=e.style;if(t===G){for(i in e._stOrig=s.cssText,o=tt(e))+i||tZ.test(i)||!o[i]||"string"!=typeof s[i]||"0"===i||(s[i]=o[i]);s.top=r,s.left=n}else s.cssText=e._stOrig;W.core.getCache(e).uncache=1,t.appendChild(e)}},tK=function(e,t,r){var n=t,i=n;return function(t){var o=Math.round(e());return o!==n&&o!==i&&Math.abs(o-n)>3&&Math.abs(o-i)>3&&(t=o,r&&r()),i=n,n=Math.round(t)}},tQ=function(e,t,r){var n={};n[t.p]="+="+r,W.set(e,n)},t0=function(e,t){var r=F(e,t),n="_scroll"+t.p2,i=function t(i,o,s,a,l){var c=t.tween,u=o.onComplete,f={};s=s||r();var p=tK(r,s,function(){c.kill(),t.tween=0});return l=a&&l||0,a=a||i-s,c&&c.kill(),o[n]=i,o.inherit=!1,o.modifiers=f,f[n]=function(){return p(s+a*c.ratio+l*c.ratio*c.ratio)},o.onUpdate=function(){x.cache++,t.tween&&tI()},o.onComplete=function(){t.tween=0,u&&u.call(c)},c=t.tween=W.to(e,o)};return e[n]=r,r.wheelHandler=function(){return i.tween&&i.tween.kill()&&(i.tween=0)},tc(e,"wheel",r.wheelHandler),t1.isTouch&&tc(e,"touchmove",r.wheelHandler),i},t1=function(){function e(t,r){q||e.register(W)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),ev(this),this.init(t,r)}return e.prototype.init=function(t,r){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),!eA){this.update=this.refresh=this.kill=eF;return}var n,i,o,s,a,l,c,u,f,p,d,h,g,v,m,y,_,w,O,E,C,P,T,k,S,A,D,X,B,I,N,L,H,q,J,Z,ee,en,eo,es,ea,eu=t=tn(eU(t)||eG(t)||t.nodeType?{trigger:t}:t,td),ef=eu.onUpdate,ep=eu.toggleClass,ed=eu.id,eh=eu.onToggle,eg=eu.onRefresh,ev=eu.scrub,em=eu.trigger,ey=eu.pin,ex=eu.pinSpacing,eb=eu.invalidateOnRefresh,eO=eu.anticipatePin,eE=eu.onScrubComplete,ek=eu.onSnapComplete,eR=eu.once,eD=eu.snap,eB=eu.pinReparent,eI=eu.pinSpacer,eL=eu.containerAnimation,eV=eu.fastScrollEnd,eQ=eu.preventOverlaps,e0=t.horizontal||t.containerAnimation&&!1!==t.horizontal?Y:z,e1=!ev&&0!==ev,tl=R(t.scroller||V),tf=W.core.getCache(tl),th=eN(tl),tm=("pinType"in t?t.pinType:M(tl,"pinType")||th&&"fixed")==="fixed",tb=[t.onEnter,t.onLeave,t.onEnterBack,t.onLeaveBack],tw=e1&&t.toggleActions.split(" "),tM="markers"in t?t.markers:td.markers,tE=th?0:parseFloat(tt(tl)["border"+e0.p2+e7])||0,tP=this,tT=t.onRefreshInit&&function(){return t.onRefreshInit(tP)},tk=eW(tl,th,e0),tS=!th||~b.indexOf(tl)?eH(tl):function(){return tG},tA=0,tY=0,tR=0,tD=F(tl,e0);if(tP._startClamp=tP._endClamp=!1,tP._dir=e0,eO*=45,tP.scroller=tl,tP.scroll=eL?eL.time.bind(eL):tD,l=tD(),tP.vars=t,r=r||t.animation,"refreshPriority"in t&&(el=1,-9999===t.refreshPriority&&(eC=tP)),tf.tweenScroll=tf.tweenScroll||{top:t0(tl,z),left:t0(tl,Y)},tP.tweenTo=o=tf.tweenScroll[e0.p],tP.scrubDuration=function(e){(J=eG(e)&&e)?q?q.duration(e):q=W.to(r,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:J,paused:!0,onComplete:function(){return eE&&eE(tP)}}):(q&&q.progress(1).kill(),q=0)},r&&(r.vars.lazy=!1,r._initted&&!tP.isReverted||!1!==r.vars.immediateRender&&!1!==t.immediateRender&&r.duration()&&r.render(0,!0,!0),tP.animation=r.pause(),r.scrollTrigger=tP,tP.scrubDuration(ev),L=0,ed||(ed=r.vars.id)),eD&&((!eJ(eD)||eD.push)&&(eD={snapTo:eD}),"scrollBehavior"in G.style&&W.set(th?[G,j]:tl,{scrollBehavior:"auto"}),x.forEach(function(e){return ej(e)&&e.target===(th?U.scrollingElement||j:tl)&&(e.smooth=!1)}),a=ej(eD.snapTo)?eD.snapTo:"labels"===eD.snapTo?(n=r,function(e){return W.utils.snap(ts(n),e)}):"labelsDirectional"===eD.snapTo?(i=r,function(e,t){return ta(ts(i))(e,t.direction)}):!1!==eD.directional?function(e,t){return ta(eD.snapTo)(e,eT()-tY<500?0:t.direction)}:W.utils.snap(eD.snapTo),Z=eJ(Z=eD.duration||{min:.1,max:2})?K(Z.min,Z.max):K(Z,Z),ee=W.delayedCall(eD.delay||J/2||.1,function(){var e=tD(),t=eT()-tY<500,n=o.tween;if((t||10>Math.abs(tP.getVelocity()))&&!n&&!er&&tA!==e){var i,s,l=(e-u)/y,c=r&&!e1?r.totalProgress():l,p=t?0:(c-H)/(eT()-Q)*1e3||0,d=W.utils.clamp(-l,1-l,eK(p/2)*p/.185),h=l+(!1===eD.inertia?0:d),g=eD,v=g.onStart,m=g.onInterrupt,x=g.onComplete;if(eG(i=a(h,tP))||(i=h),s=Math.max(0,Math.round(u+i*y)),e<=f&&e>=u&&s!==e){if(n&&!n._initted&&n.data<=eK(s-e))return;!1===eD.inertia&&(d=i-l),o(s,{duration:Z(eK(.185*Math.max(eK(h-c),eK(i-c))/p/.05||0)),ease:eD.ease||"power3",data:eK(s-e),onInterrupt:function(){return ee.restart(!0)&&m&&m(tP)},onComplete:function(){tP.update(),tA=tD(),r&&!e1&&(q?q.resetTo("totalProgress",i,r._tTime/r._tDur):r.progress(i)),L=H=r&&!e1?r.totalProgress():tP.progress,ek&&ek(tP),x&&x(tP)}},e,d*y,s-e-d*y),v&&v(tP,o.tween)}}else tP.isActive&&tA!==e&&ee.restart(!0)}).pause()),ed&&(tx[ed]=tP),(ea=(em=tP.trigger=R(em||!0!==ey&&ey))&&em._gsap&&em._gsap.stRevert)&&(ea=ea(tP)),ey=!0===ey?em:R(ey),eU(ep)&&(ep={targets:em,className:ep}),ey&&(!1===ex||ex===e9||(ex=(!!ex||!ey.parentNode||!ey.parentNode.style||"flex"!==tt(ey.parentNode).display)&&e8),tP.pin=ey,(s=W.core.getCache(ey)).spacer?_=s.pinState:(eI&&((eI=R(eI))&&!eI.nodeType&&(eI=eI.current||eI.nativeElement),s.spacerIsNative=!!eI,eI&&(s.spacerState=tU(eI))),s.spacer=E=eI||U.createElement("div"),E.classList.add("pin-spacer"),ed&&E.classList.add("pin-spacer-"+ed),s.pinState=_=tU(ey)),!1!==t.force3D&&W.set(ey,{force3D:!0}),tP.spacer=E=s.spacer,A=(N=tt(ey))[ex+e0.os2],P=W.getProperty(ey),T=W.quickSetter(ey,e0.a,"px"),tW(ey,E,N),O=tU(ey)),tM){v=eJ(tM)?tn(tM,tp):tp,h=tv("scroller-start",ed,tl,e0,v,0),g=tv("scroller-end",ed,tl,e0,v,0,h),C=h["offset"+e0.op.d2];var tF=R(M(tl,"content")||tl);p=this.markerStart=tv("start",ed,tF,e0,v,C,0,eL),d=this.markerEnd=tv("end",ed,tF,e0,v,C,0,eL),eL&&(es=W.quickSetter([p,d],e0.a,"px")),tm||b.length&&!0===M(tl,"fixedMarkers")||(tr(th?G:tl),W.set([h,g],{force3D:!0}),X=W.quickSetter(h,e0.a,"px"),I=W.quickSetter(g,e0.a,"px"))}if(eL){var tX=eL.vars.onUpdate,tI=eL.vars.onUpdateParams;eL.eventCallback("onUpdate",function(){tP.update(0,0,1),tX&&tX.apply(eL,tI||[])})}if(tP.previous=function(){return ty[ty.indexOf(tP)-1]},tP.next=function(){return ty[ty.indexOf(tP)+1]},tP.revert=function(e,t){if(!t)return tP.kill(!0);var n=!1!==e||!tP.enabled,i=et;n!==tP.isReverted&&(n&&(en=Math.max(tD(),tP.scroll.rec||0),tR=tP.progress,eo=r&&r.progress()),p&&[p,d,h,g].forEach(function(e){return e.style.display=n?"none":"block"}),n&&(et=tP,tP.update(n)),!ey||eB&&tP.isActive||(n?tH(ey,E,_):tW(ey,E,tt(ey),D)),n||tP.update(n),et=i,tP.isReverted=n)},tP.refresh=function(n,i,s,a){if(!et&&tP.enabled||i){if(ey&&n&&eS)return void tc(e,"scrollEnd",tC);!eM&&tT&&tT(tP),et=tP,o.tween&&!s&&(o.tween.kill(),o.tween=0),q&&q.pause(),eb&&r&&(r.revert({kill:!1}).invalidate(),r.getChildren&&r.getChildren(!0,!0,!1).forEach(function(e){return e.vars.immediateRender&&e.render(0,!0,!0)})),tP.isReverted||tP.revert(!0,!0),tP._subPinOffset=!1;var v,x,b,M,C,T,A,X,I,N,L,H,V,J=tk(),Z=tS(),$=eL?eL.duration():eq(tl,e0),K=y<=.01||!y,Q=0,er=a||0,ei=eJ(s)?s.end:t.end,es=t.endTrigger||em,ea=eJ(s)?s.start:t.start||(0!==t.start&&em?ey?"0 0":"0 100%":0),el=tP.pinnedContainer=t.pinnedContainer&&R(t.pinnedContainer,tP),eu=em&&Math.max(0,ty.indexOf(tP))||0,ef=eu;for(tM&&eJ(s)&&(H=W.getProperty(h,e0.p),V=W.getProperty(g,e0.p));ef-- >0;)(T=ty[ef]).end||T.refresh(0,1)||(et=tP),(A=T.pin)&&(A===em||A===ey||A===el)&&!T.isReverted&&(N||(N=[]),N.unshift(T),T.revert(!0,!0)),T!==ty[ef]&&(eu--,ef--);for(ej(ea)&&(ea=ea(tP)),u=tJ(ea=eY(ea,"start",tP),em,J,e0,tD(),p,h,tP,Z,tE,tm,$,eL,tP._startClamp&&"_startClamp")||(ey?-.001:0),ej(ei)&&(ei=ei(tP)),eU(ei)&&!ei.indexOf("+=")&&(~ei.indexOf(" ")?ei=(eU(ea)?ea.split(" ")[0]:"")+ei:(Q=tg(ei.substr(2),J),ei=eU(ea)?ea:(eL?W.utils.mapRange(0,eL.duration(),eL.scrollTrigger.start,eL.scrollTrigger.end,u):u)+Q,es=em)),ei=eY(ei,"end",tP),f=Math.max(u,tJ(ei||(es?"100% 0":$),es,J,e0,tD()+Q,d,g,tP,Z,tE,tm,$,eL,tP._endClamp&&"_endClamp"))||-.001,Q=0,ef=eu;ef--;)(A=(T=ty[ef]).pin)&&T.start-T._pinPush<=u&&!eL&&T.end>0&&(v=T.end-(tP._startClamp?Math.max(0,T.start):T.start),(A===em&&T.start-T._pinPush<u||A===el)&&isNaN(ea)&&(Q+=v*(1-T.progress)),A===ey&&(er+=v));if(u+=Q,f+=Q,tP._startClamp&&(tP._startClamp+=Q),tP._endClamp&&!eM&&(tP._endClamp=f||-.001,f=Math.min(f,eq(tl,e0))),y=f-u||(u-=.01)&&.001,K&&(tR=W.utils.clamp(0,1,W.utils.normalize(u,f,en))),tP._pinPush=er,p&&Q&&((v={})[e0.a]="+="+Q,el&&(v[e0.p]="-="+tD()),W.set([p,d],v)),ey&&!(e_&&tP.end>=eq(tl,e0)))v=tt(ey),M=e0===z,b=tD(),k=parseFloat(P(e0.a))+er,!$&&f>1&&(L={style:L=(th?U.scrollingElement||j:tl).style,value:L["overflow"+e0.a.toUpperCase()]},th&&"scroll"!==tt(G)["overflow"+e0.a.toUpperCase()]&&(L.style["overflow"+e0.a.toUpperCase()]="scroll")),tW(ey,E,v),O=tU(ey),x=ti(ey,!0),X=tm&&F(tl,M?Y:z)(),ex?((D=[ex+e0.os2,y+er+"px"]).t=E,(ef=ex===e8?to(ey,e0)+y+er:0)&&(D.push(e0.d,ef+"px"),"auto"!==E.style.flexBasis&&(E.style.flexBasis=ef+"px")),tV(D),el&&ty.forEach(function(e){e.pin===el&&!1!==e.vars.pinSpacing&&(e._subPinOffset=!0)}),tm&&tD(en)):(ef=to(ey,e0))&&"auto"!==E.style.flexBasis&&(E.style.flexBasis=ef+"px"),tm&&((C={top:x.top+(M?b-u:X)+"px",left:x.left+(M?X:b-u)+"px",boxSizing:"border-box",position:"fixed"})[e2]=C["max"+e7]=Math.ceil(x.width)+"px",C[e3]=C["max"+te]=Math.ceil(x.height)+"px",C[e9]=C[e9+"Top"]=C[e9+e5]=C[e9+e4]=C[e9+e6]="0",C[e8]=v[e8],C[e8+"Top"]=v[e8+"Top"],C[e8+e5]=v[e8+e5],C[e8+e4]=v[e8+e4],C[e8+e6]=v[e8+e6],w=tj(_,C,eB),eM&&tD(0)),r?(I=r._initted,ec(1),r.render(r.duration(),!0,!0),S=P(e0.a)-k+y+er,B=Math.abs(y-S)>1,tm&&B&&w.splice(w.length-2,2),r.render(0,!0,!0),I||r.invalidate(!0),r.parent||r.totalTime(r.totalTime()),ec(0)):S=y,L&&(L.value?L.style["overflow"+e0.a.toUpperCase()]=L.value:L.style.removeProperty("overflow-"+e0.a));else if(em&&tD()&&!eL)for(x=em.parentNode;x&&x!==G;)x._pinOffset&&(u-=x._pinOffset,f-=x._pinOffset),x=x.parentNode;N&&N.forEach(function(e){return e.revert(!1,!0)}),tP.start=u,tP.end=f,l=c=eM?en:tD(),eL||eM||(l<en&&tD(en),tP.scroll.rec=0),tP.revert(!1,!0),tY=eT(),ee&&(tA=-1,ee.restart(!0)),et=0,r&&e1&&(r._initted||eo)&&r.progress()!==eo&&r.progress(eo||0,!0).render(r.time(),!0,!0),(K||tR!==tP.progress||eL||eb||r&&!r._initted)&&(r&&!e1&&(r._initted||tR||!1!==r.vars.immediateRender)&&r.totalProgress(eL&&u<-.001&&!tR?W.utils.normalize(u,f,0):tR,!0),tP.progress=K||(l-u)/y===tR?0:tR),ey&&ex&&(E._pinOffset=Math.round(tP.progress*S)),q&&q.invalidate(),isNaN(H)||(H-=W.getProperty(h,e0.p),V-=W.getProperty(g,e0.p),tQ(h,e0,H),tQ(p,e0,H-(a||0)),tQ(g,e0,V),tQ(d,e0,V-(a||0))),K&&!eM&&tP.update(),!eg||eM||m||(m=!0,eg(tP),m=!1)}},tP.getVelocity=function(){return(tD()-c)/(eT()-Q)*1e3||0},tP.endAnimation=function(){eZ(tP.callbackAnimation),r&&(q?q.progress(1):r.paused()?e1||eZ(r,tP.direction<0,1):eZ(r,r.reversed()))},tP.labelToScroll=function(e){return r&&r.labels&&(u||tP.refresh()||u)+r.labels[e]/r.duration()*y||0},tP.getTrailing=function(e){var t=ty.indexOf(tP),r=tP.direction>0?ty.slice(0,t).reverse():ty.slice(t+1);return(eU(e)?r.filter(function(t){return t.vars.preventOverlaps===e}):r).filter(function(e){return tP.direction>0?e.end<=u:e.start>=f})},tP.update=function(e,t,n){if(!eL||n||e){var i,s,a,p,d,g,v,m=!0===eM?en:tP.scroll(),x=e?0:(m-u)/y,b=x<0?0:x>1?1:x||0,_=tP.progress;if(t&&(c=l,l=eL?tD():m,eD&&(H=L,L=r&&!e1?r.totalProgress():b)),eO&&ey&&!et&&!eP&&eS&&(!b&&u<m+(m-c)/(eT()-Q)*eO?b=1e-4:1===b&&f>m+(m-c)/(eT()-Q)*eO&&(b=.9999)),b!==_&&tP.enabled){if(p=(d=(i=tP.isActive=!!b&&b<1)!=(!!_&&_<1))||!!b!=!!_,tP.direction=b>_?1:-1,tP.progress=b,p&&!et&&(s=b&&!_?0:1===b?1:1===_?2:3,e1&&(a=!d&&"none"!==tw[s+1]&&tw[s+1]||tw[s],v=r&&("complete"===a||"reset"===a||a in r))),eQ&&(d||v)&&(v||ev||!r)&&(ej(eQ)?eQ(tP):tP.getTrailing(eQ).forEach(function(e){return e.endAnimation()})),!e1&&(!q||et||eP?r&&r.totalProgress(b,!!(et&&(tY||e))):(q._dp._time-q._start!==q._time&&q.render(q._dp._time-q._start),q.resetTo?q.resetTo("totalProgress",b,r._tTime/r._tDur):(q.vars.totalProgress=b,q.invalidate().restart()))),ey)if(e&&ex&&(E.style[ex+e0.os2]=A),tm){if(p){if(g=!e&&b>_&&f+1>m&&m+1>=eq(tl,e0),eB)if(!e&&(i||g)){var M=ti(ey,!0),C=m-u;t$(ey,G,M.top+(e0===z?C:0)+"px",M.left+(e0===z?0:C)+"px")}else t$(ey,E);tV(i||g?w:O),B&&b<1&&i||T(k+(1!==b||g?0:S))}}else T(eX(k+S*b));!eD||o.tween||et||eP||ee.restart(!0),ep&&(d||eR&&b&&(b<1||!ew))&&$(ep.targets).forEach(function(e){return e.classList[i||eR?"add":"remove"](ep.className)}),!ef||e1||e||ef(tP),p&&!et?(e1&&(v&&("complete"===a?r.pause().totalProgress(1):"reset"===a?r.restart(!0).pause():"restart"===a?r.restart(!0):r[a]()),ef&&ef(tP)),(d||!ew)&&(eh&&d&&e$(tP,eh),tb[s]&&e$(tP,tb[s]),eR&&(1===b?tP.kill(!1,1):tb[s]=0),!d&&tb[s=1===b?1:3]&&e$(tP,tb[s])),eV&&!i&&Math.abs(tP.getVelocity())>(eG(eV)?eV:2500)&&(eZ(tP.callbackAnimation),q?q.progress(1):eZ(r,"reverse"===a?1:!b,1))):e1&&ef&&!et&&ef(tP)}if(I){var P=eL?m/eL.duration()*(eL._caScrollDist||0):m;X(P+ +!!h._isFlipped),I(P)}es&&es(-m/eL.duration()*(eL._caScrollDist||0))}},tP.enable=function(t,r){tP.enabled||(tP.enabled=!0,tc(tl,"resize",tO),th||tc(tl,"scroll",t_),tT&&tc(e,"refreshInit",tT),!1!==t&&(tP.progress=tR=0,l=c=tA=tD()),!1!==r&&tP.refresh())},tP.getTween=function(e){return e&&o?o.tween:q},tP.setPositions=function(e,t,r,n){if(eL){var i=eL.scrollTrigger,o=eL.duration(),s=i.end-i.start;e=i.start+s*e/o,t=i.start+s*t/o}tP.refresh(!1,!1,{start:ez(e,r&&!!tP._startClamp),end:ez(t,r&&!!tP._endClamp)},n),tP.update()},tP.adjustPinSpacing=function(e){if(D&&e){var t=D.indexOf(e0.d)+1;D[t]=parseFloat(D[t])+e+"px",D[1]=parseFloat(D[1])+e+"px",tV(D)}},tP.disable=function(t,r){if(tP.enabled&&(!1!==t&&tP.revert(!0,!0),tP.enabled=tP.isActive=!1,r||q&&q.pause(),en=0,s&&(s.uncache=1),tT&&tu(e,"refreshInit",tT),ee&&(ee.pause(),o.tween&&o.tween.kill()&&(o.tween=0)),!th)){for(var n=ty.length;n--;)if(ty[n].scroller===tl&&ty[n]!==tP)return;tu(tl,"resize",tO),th||tu(tl,"scroll",t_)}},tP.kill=function(e,n){tP.disable(e,n),q&&!n&&q.kill(),ed&&delete tx[ed];var i=ty.indexOf(tP);i>=0&&ty.splice(i,1),i===ei&&tB>0&&ei--,i=0,ty.forEach(function(e){return e.scroller===tP.scroller&&(i=1)}),i||eM||(tP.scroll.rec=0),r&&(r.scrollTrigger=null,e&&r.revert({kill:!1}),n||r.kill()),p&&[p,d,h,g].forEach(function(e){return e.parentNode&&e.parentNode.removeChild(e)}),eC===tP&&(eC=0),ey&&(s&&(s.uncache=1),i=0,ty.forEach(function(e){return e.pin===ey&&i++}),i||(s.spacer=0)),t.onKill&&t.onKill(tP)},ty.push(tP),tP.enable(!1,!1),ea&&ea(tP),r&&r.add&&!y){var tN=tP.update;tP.update=function(){tP.update=tN,x.cache++,u||f||tP.refresh()},W.delayedCall(.01,tP.update),y=.01,u=f=0}else tP.refresh();ey&&tz()},e.register=function(t){return q||(W=t||eI(),eB()&&window.document&&e.enable(),q=eA),q},e.defaults=function(e){if(e)for(var t in e)td[t]=e[t];return td},e.disable=function(e,t){eA=0,ty.forEach(function(r){return r[t?"kill":"disable"](e)}),tu(V,"wheel",t_),tu(U,"scroll",t_),clearInterval(ee),tu(U,"touchcancel",eF),tu(G,"touchstart",eF),tl(tu,U,"pointerdown,touchstart,mousedown",eR),tl(tu,U,"pointerup,touchend,mouseup",eD),Z.kill(),eV(tu);for(var r=0;r<x.length;r+=3)tf(tu,x[r],x[r+1]),tf(tu,x[r],x[r+2])},e.enable=function(){if(V=window,j=(U=document).documentElement,G=U.body,W&&($=W.utils.toArray,K=W.utils.clamp,ev=W.core.context||eF,ec=W.core.suppressOverwrites||eF,em=V.history.scrollRestoration||"auto",tX=V.pageYOffset||0,W.core.globals("ScrollTrigger",e),G)){eA=1,(ey=document.createElement("div")).style.height="100vh",ey.style.position="absolute",tR(),function e(){return eA&&requestAnimationFrame(e)}(),H.register(W),e.isTouch=H.isTouch,eg=H.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),ep=1===H.isTouch,tc(V,"wheel",t_),J=[V,U,j,G],W.matchMedia?(e.matchMedia=function(e){var t,r=W.matchMedia();for(t in e)r.add(t,e[t]);return r},W.addEventListener("matchMediaInit",function(){return tS()}),W.addEventListener("matchMediaRevert",function(){return tk()}),W.addEventListener("matchMedia",function(){tF(0,1),tP("matchMedia")}),W.matchMedia().add("(orientation: portrait)",function(){return tw(),tw})):console.warn("Requires GSAP 3.11.0 or later"),tw(),tc(U,"scroll",t_);var t,r,n=G.hasAttribute("style"),i=G.style,o=i.borderTopStyle,s=W.core.Animation.prototype;for(s.revert||Object.defineProperty(s,"revert",{value:function(){return this.time(-.01,!0)}}),i.borderTopStyle="solid",z.m=Math.round((t=ti(G)).top+z.sc())||0,Y.m=Math.round(t.left+Y.sc())||0,o?i.borderTopStyle=o:i.removeProperty("border-top-style"),n||(G.setAttribute("style",""),G.removeAttribute("style")),ee=setInterval(tb,250),W.delayedCall(.5,function(){return eP=0}),tc(U,"touchcancel",eF),tc(G,"touchstart",eF),tl(tc,U,"pointerdown,touchstart,mousedown",eR),tl(tc,U,"pointerup,touchend,mouseup",eD),en=W.utils.checkPrefix("transform"),tL.push(en),q=eT(),Z=W.delayedCall(.2,tF).pause(),ea=[U,"visibilitychange",function(){var e=V.innerWidth,t=V.innerHeight;U.hidden?(eo=e,es=t):(eo!==e||es!==t)&&tO()},U,"DOMContentLoaded",tF,V,"load",tF,V,"resize",tO],eV(tc),ty.forEach(function(e){return e.enable(0,1)}),r=0;r<x.length;r+=3)tf(tu,x[r],x[r+1]),tf(tu,x[r],x[r+2])}},e.config=function(t){"limitCallbacks"in t&&(ew=!!t.limitCallbacks);var r=t.syncInterval;r&&clearInterval(ee)||(ee=r)&&setInterval(tb,r),"ignoreMobileResize"in t&&(ep=1===e.isTouch&&t.ignoreMobileResize),"autoRefreshEvents"in t&&(eV(tu)||eV(tc,t.autoRefreshEvents||"none"),eu=-1===(t.autoRefreshEvents+"").indexOf("resize"))},e.scrollerProxy=function(e,t){var r=R(e),n=x.indexOf(r),i=eN(r);~n&&x.splice(n,i?6:2),t&&(i?b.unshift(V,t,G,t,j,t):b.unshift(r,t))},e.clearMatchMedia=function(e){ty.forEach(function(t){return t._ctx&&t._ctx.query===e&&t._ctx.kill(!0,!0)})},e.isInViewport=function(e,t,r){var n=(eU(e)?R(e):e).getBoundingClientRect(),i=n[r?e2:e3]*t||0;return r?n.right-i>0&&n.left+i<V.innerWidth:n.bottom-i>0&&n.top+i<V.innerHeight},e.positionInViewport=function(e,t,r){eU(e)&&(e=R(e));var n=e.getBoundingClientRect(),i=n[r?e2:e3],o=null==t?i/2:t in th?th[t]*i:~t.indexOf("%")?parseFloat(t)*i/100:parseFloat(t)||0;return r?(n.left+o)/V.innerWidth:(n.top+o)/V.innerHeight},e.killAll=function(e){if(ty.slice(0).forEach(function(e){return"ScrollSmoother"!==e.vars.id&&e.kill()}),!0!==e){var t=tM.killAll||[];tM={},t.forEach(function(e){return e()})}},e}();t1.version="3.13.0",t1.saveStyles=function(e){return e?$(e).forEach(function(e){if(e&&e.style){var t=tT.indexOf(e);t>=0&&tT.splice(t,5),tT.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),W.core.getCache(e),ev())}}):tT},t1.revert=function(e,t){return tS(!e,t)},t1.create=function(e,t){return new t1(e,t)},t1.refresh=function(e){return e?tO(!0):(q||t1.register())&&tF(!0)},t1.update=function(e){return++x.cache&&tI(2*(!0===e))},t1.clearScrollMemory=tA,t1.maxScroll=function(e,t){return eq(e,t?Y:z)},t1.getScrollFunc=function(e,t){return F(R(e),t?Y:z)},t1.getById=function(e){return tx[e]},t1.getAll=function(){return ty.filter(function(e){return"ScrollSmoother"!==e.vars.id})},t1.isScrolling=function(){return!!eS},t1.snapDirectional=ta,t1.addEventListener=function(e,t){var r=tM[e]||(tM[e]=[]);~r.indexOf(t)||r.push(t)},t1.removeEventListener=function(e,t){var r=tM[e],n=r&&r.indexOf(t);n>=0&&r.splice(n,1)},t1.batch=function(e,t){var r,n=[],i={},o=t.interval||.016,s=t.batchMax||1e9,a=function(e,t){var r=[],n=[],i=W.delayedCall(o,function(){t(r,n),r=[],n=[]}).pause();return function(e){r.length||i.restart(!0),r.push(e.trigger),n.push(e),s<=r.length&&i.progress(1)}};for(r in t)i[r]="on"===r.substr(0,2)&&ej(t[r])&&"onRefreshInit"!==r?a(r,t[r]):t[r];return ej(s)&&(s=s(),tc(t1,"refresh",function(){return s=t.batchMax()})),$(e).forEach(function(e){var t={};for(r in i)t[r]=i[r];t.trigger=e,n.push(t1.create(t))}),n};var t2,t3=function(e,t,r,n){return t>n?e(n):t<0&&e(0),r>n?(n-t)/(r-t):r<0?t/(t-r):1},t5=function e(t,r){!0===r?t.style.removeProperty("touch-action"):t.style.touchAction=!0===r?"auto":r?"pan-"+r+(H.isTouch?" pinch-zoom":""):"none",t===j&&e(G,r)},t6={auto:1,scroll:1},t4=function(e){var t,r=e.event,n=e.target,i=e.axis,o=(r.changedTouches?r.changedTouches[0]:r).target,s=o._gsap||W.core.getCache(o),a=eT();if(!s._isScrollT||a-s._isScrollT>2e3){for(;o&&o!==G&&(o.scrollHeight<=o.clientHeight&&o.scrollWidth<=o.clientWidth||!(t6[(t=tt(o)).overflowY]||t6[t.overflowX]));)o=o.parentNode;s._isScroll=o&&o!==n&&!eN(o)&&(t6[(t=tt(o)).overflowY]||t6[t.overflowX]),s._isScrollT=a}(s._isScroll||"x"===i)&&(r.stopPropagation(),r._gsapAllow=!0)},t8=function(e,t,r,n){return H.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:t,onWheel:n=n&&t4,onPress:n,onDrag:n,onScroll:n,onEnable:function(){return r&&tc(U,H.eventTypes[0],t7,!1,!0)},onDisable:function(){return tu(U,H.eventTypes[0],t7,!0)}})},t9=/(input|label|select|textarea)/i,t7=function(e){var t=t9.test(e.target.tagName);(t||t2)&&(e._gsapAllow=!0,t2=t)},re=function(e){eJ(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var t,r,n,i,o,s,a,l,c=e,u=c.normalizeScrollX,f=c.momentum,p=c.allowNestedScroll,d=c.onRelease,h=R(e.target)||j,g=W.core.globals().ScrollSmoother,v=g&&g.get(),m=eg&&(e.content&&R(e.content)||v&&!1!==e.content&&!v.smooth()&&v.content()),y=F(h,z),b=F(h,Y),_=1,w=(H.isTouch&&V.visualViewport?V.visualViewport.scale*V.visualViewport.width:V.outerWidth)/V.innerWidth,O=0,M=ej(f)?function(){return f(t)}:function(){return f||2.8},E=t8(h,e.type,!0,p),C=function(){return i=!1},P=eF,T=eF,k=function(){r=eq(h,z),T=K(+!!eg,r),u&&(P=K(0,eq(h,Y))),n=tY},S=function(){m._gsap.y=eX(parseFloat(m._gsap.y)+y.offset)+"px",m.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(m._gsap.y)+", 0, 1)",y.offset=y.cacheID=0},A=function(){if(i){requestAnimationFrame(C);var e=eX(t.deltaY/2),r=T(y.v-e);if(m&&r!==y.v+y.offset){y.offset=r-y.v;var n=eX((parseFloat(m&&m._gsap.y)||0)-y.offset);m.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+n+", 0, 1)",m._gsap.y=n+"px",y.cacheID=x.cache,tI()}return!0}y.offset&&S(),i=!0},D=function(){k(),o.isActive()&&o.vars.scrollY>r&&(y()>r?o.progress(1)&&y(r):o.resetTo("scrollY",r))};return m&&W.set(m,{y:"+=0"}),e.ignoreCheck=function(e){return eg&&"touchmove"===e.type&&A(e)||_>1.05&&"touchstart"!==e.type||t.isGesturing||e.touches&&e.touches.length>1},e.onPress=function(){i=!1;var e=_;_=eX((V.visualViewport&&V.visualViewport.scale||1)/w),o.pause(),e!==_&&t5(h,_>1.01||!u&&"x"),s=b(),a=y(),k(),n=tY},e.onRelease=e.onGestureStart=function(e,t){if(y.offset&&S(),t){x.cache++;var n,i,s=M();u&&(i=(n=b())+-(.05*s*e.velocityX)/.227,s*=t3(b,n,i,eq(h,Y)),o.vars.scrollX=P(i)),i=(n=y())+-(.05*s*e.velocityY)/.227,s*=t3(y,n,i,eq(h,z)),o.vars.scrollY=T(i),o.invalidate().duration(s).play(.01),(eg&&o.vars.scrollY>=r||n>=r-1)&&W.to({},{onUpdate:D,duration:s})}else l.restart(!0);d&&d(e)},e.onWheel=function(){o._ts&&o.pause(),eT()-O>1e3&&(n=0,O=eT())},e.onChange=function(e,t,r,i,o){if(tY!==n&&k(),t&&u&&b(P(i[2]===t?s+(e.startX-e.x):b()+t-i[1])),r){y.offset&&S();var l=o[2]===r,c=l?a+e.startY-e.y:y()+r-o[1],f=T(c);l&&c!==f&&(a+=f-c),y(f)}(r||t)&&tI()},e.onEnable=function(){t5(h,!u&&"x"),t1.addEventListener("refresh",D),tc(V,"resize",D),y.smooth&&(y.target.style.scrollBehavior="auto",y.smooth=b.smooth=!1),E.enable()},e.onDisable=function(){t5(h,!0),tu(V,"resize",D),t1.removeEventListener("refresh",D),E.kill()},e.lockAxis=!1!==e.lockAxis,(t=new H(e)).iOS=eg,eg&&!y()&&y(1),eg&&W.ticker.add(eF),l=t._dc,o=W.to(t,{ease:"power4",paused:!0,inherit:!1,scrollX:u?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:tK(y,y(),function(){return o.pause()})},onUpdate:tI,onComplete:l.vars.onComplete}),t};t1.sort=function(e){if(ej(e))return ty.sort(e);var t=V.pageYOffset||0;return t1.getAll().forEach(function(e){return e._sortY=e.trigger?t+e.trigger.getBoundingClientRect().top:e.start+V.innerHeight}),ty.sort(e||function(e,t){return -1e6*(e.vars.refreshPriority||0)+(e.vars.containerAnimation?1e6:e._sortY)-((t.vars.containerAnimation?1e6:t._sortY)+-1e6*(t.vars.refreshPriority||0))})},t1.observe=function(e){return new H(e)},t1.normalizeScroll=function(e){if(void 0===e)return ef;if(!0===e&&ef)return ef.enable();if(!1===e){ef&&ef.kill(),ef=e;return}var t=e instanceof H?e:re(e);return ef&&ef.target===t.target&&ef.kill(),eN(t.target)&&(ef=t),t},t1.core={_getVelocityProp:X,_inputObserver:t8,_scrollers:x,_proxies:b,bridge:{ss:function(){eS||tP("scrollStart"),eS=eT()},ref:function(){return et}}},eI()&&W.registerPlugin(t1)},9676:(e,t,r)=>{r.d(t,{L:()=>u});var n=r(2115),i=r(802);let o="undefined"!=typeof document?n.useLayoutEffect:n.useEffect,s=e=>e&&!Array.isArray(e)&&"object"==typeof e,a=[],l={},c=i.Ay,u=(e,t=a)=>{let r=l;s(e)?(r=e,e=null,t="dependencies"in r?r.dependencies:a):s(t)&&(t="dependencies"in(r=t)?r.dependencies:a),e&&"function"!=typeof e&&console.warn("First parameter must be a function or config object");let{scope:i,revertOnUpdate:u}=r,f=(0,n.useRef)(!1),p=(0,n.useRef)(c.context(()=>{},i)),d=(0,n.useRef)(e=>p.current.add(null,e)),h=t&&t.length&&!u;return h&&o(()=>(f.current=!0,()=>p.current.revert()),a),o(()=>{if(e&&p.current.add(e,i),!h||!f.current)return()=>p.current.revert()},t),{context:p.current,contextSafe:d.current}};u.register=e=>{c=e},u.headless=!0}}]);