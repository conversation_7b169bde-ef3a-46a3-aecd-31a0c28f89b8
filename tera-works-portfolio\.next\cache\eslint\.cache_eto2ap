[{"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\layout.tsx": "1", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\page.tsx": "2", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Footer.tsx": "3", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Header.tsx": "4", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\About.tsx": "5", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Contact.tsx": "6", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Hero.tsx": "7", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Portfolio.tsx": "8", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Services.tsx": "9", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Testimonials.tsx": "10", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Button.tsx": "11", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Card.tsx": "12", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\index.ts": "13", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Input.tsx": "14", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Modal.tsx": "15", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\navigation.ts": "16", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\portfolio.ts": "17", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\services.ts": "18", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\testimonials.ts": "19", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\lib\\animations\\framer.ts": "20", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\lib\\animations\\gsap.ts": "21", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\lib\\utils.ts": "22", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\types\\index.ts": "23", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\about\\page.tsx": "24", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\api\\contact\\route.ts": "25", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\blog\\page.tsx": "26", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\contact\\page.tsx": "27", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\error.tsx": "28", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\loading.tsx": "29", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\not-found.tsx": "30", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\portfolio\\page.tsx": "31", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\robots.ts": "32", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\services\\page.tsx": "33", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\sitemap.ts": "34", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\hooks\\useIntersectionObserver.ts": "35", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\hooks\\useMediaQuery.ts": "36", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\hooks\\useScrollPosition.ts": "37"}, {"size": 2290, "mtime": 1750437298514, "results": "38", "hashOfConfig": "39"}, {"size": 502, "mtime": 1750437657569, "results": "40", "hashOfConfig": "39"}, {"size": 10674, "mtime": 1750437161917, "results": "41", "hashOfConfig": "39"}, {"size": 5231, "mtime": 1750438231111, "results": "42", "hashOfConfig": "39"}, {"size": 9394, "mtime": 1750438384454, "results": "43", "hashOfConfig": "39"}, {"size": 13378, "mtime": 1750440192832, "results": "44", "hashOfConfig": "39"}, {"size": 8964, "mtime": 1750438126698, "results": "45", "hashOfConfig": "39"}, {"size": 10464, "mtime": 1750438032563, "results": "46", "hashOfConfig": "39"}, {"size": 9977, "mtime": 1750437426034, "results": "47", "hashOfConfig": "39"}, {"size": 9237, "mtime": 1750438093069, "results": "48", "hashOfConfig": "39"}, {"size": 3104, "mtime": 1750437003080, "results": "49", "hashOfConfig": "39"}, {"size": 5620, "mtime": 1750437032669, "results": "50", "hashOfConfig": "39"}, {"size": 239, "mtime": 1750437089568, "results": "51", "hashOfConfig": "39"}, {"size": 6865, "mtime": 1750438957970, "results": "52", "hashOfConfig": "39"}, {"size": 2750, "mtime": 1750437080785, "results": "53", "hashOfConfig": "39"}, {"size": 3740, "mtime": 1750436983592, "results": "54", "hashOfConfig": "39"}, {"size": 8653, "mtime": 1750436903110, "results": "55", "hashOfConfig": "39"}, {"size": 7753, "mtime": 1750436935929, "results": "56", "hashOfConfig": "39"}, {"size": 4454, "mtime": 1750436961528, "results": "57", "hashOfConfig": "39"}, {"size": 4965, "mtime": 1750436805510, "results": "58", "hashOfConfig": "39"}, {"size": 5461, "mtime": 1750436776301, "results": "59", "hashOfConfig": "39"}, {"size": 6866, "mtime": 1750436836083, "results": "60", "hashOfConfig": "39"}, {"size": 5672, "mtime": 1750436864826, "results": "61", "hashOfConfig": "39"}, {"size": 2395, "mtime": 1750439885527, "results": "62", "hashOfConfig": "39"}, {"size": 1655, "mtime": 1750440174985, "results": "63", "hashOfConfig": "39"}, {"size": 4807, "mtime": 1750440032346, "results": "64", "hashOfConfig": "39"}, {"size": 3424, "mtime": 1750439946286, "results": "65", "hashOfConfig": "39"}, {"size": 1495, "mtime": 1750440116136, "results": "66", "hashOfConfig": "39"}, {"size": 539, "mtime": 1750440099692, "results": "67", "hashOfConfig": "39"}, {"size": 937, "mtime": 1750440127046, "results": "68", "hashOfConfig": "39"}, {"size": 1406, "mtime": 1750439901296, "results": "69", "hashOfConfig": "39"}, {"size": 262, "mtime": 1750440272173, "results": "70", "hashOfConfig": "39"}, {"size": 3064, "mtime": 1750439917922, "results": "71", "hashOfConfig": "39"}, {"size": 938, "mtime": 1750440255209, "results": "72", "hashOfConfig": "39"}, {"size": 959, "mtime": 1750440013538, "results": "73", "hashOfConfig": "39"}, {"size": 515, "mtime": 1750440022516, "results": "74", "hashOfConfig": "39"}, {"size": 439, "mtime": 1750440001826, "results": "75", "hashOfConfig": "39"}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "42mhxg", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\layout.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\page.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Footer.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Header.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\About.tsx", ["187", "188"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Contact.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Hero.tsx", ["189", "190", "191"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Portfolio.tsx", ["192", "193"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Services.tsx", ["194"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Testimonials.tsx", ["195", "196"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Button.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Card.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\index.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Input.tsx", ["197", "198", "199", "200"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Modal.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\navigation.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\portfolio.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\services.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\testimonials.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\lib\\animations\\framer.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\lib\\animations\\gsap.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\lib\\utils.ts", ["201", "202", "203", "204", "205"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\types\\index.ts", ["206", "207"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\about\\page.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\api\\contact\\route.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\blog\\page.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\contact\\page.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\error.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\loading.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\not-found.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\portfolio\\page.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\robots.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\services\\page.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\sitemap.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\hooks\\useIntersectionObserver.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\hooks\\useMediaQuery.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\hooks\\useScrollPosition.ts", [], [], {"ruleId": "208", "severity": 1, "message": "209", "line": 16, "column": 10, "nodeType": null, "messageId": "210", "endLine": 16, "endColumn": 18}, {"ruleId": "208", "severity": 1, "message": "211", "line": 221, "column": 36, "nodeType": null, "messageId": "210", "endLine": 221, "endColumn": 41}, {"ruleId": "208", "severity": 1, "message": "212", "line": 3, "column": 25, "nodeType": null, "messageId": "210", "endLine": 3, "endColumn": 34}, {"ruleId": "208", "severity": 1, "message": "213", "line": 4, "column": 8, "nodeType": null, "messageId": "210", "endLine": 4, "endColumn": 13}, {"ruleId": "208", "severity": 1, "message": "209", "line": 10, "column": 10, "nodeType": null, "messageId": "210", "endLine": 10, "endColumn": 18}, {"ruleId": "208", "severity": 1, "message": "214", "line": 7, "column": 10, "nodeType": null, "messageId": "210", "endLine": 7, "endColumn": 23}, {"ruleId": "208", "severity": 1, "message": "211", "line": 106, "column": 45, "nodeType": null, "messageId": "210", "endLine": 106, "endColumn": 50}, {"ruleId": "208", "severity": 1, "message": "211", "line": 68, "column": 43, "nodeType": null, "messageId": "210", "endLine": 68, "endColumn": 48}, {"ruleId": "208", "severity": 1, "message": "211", "line": 109, "column": 47, "nodeType": null, "messageId": "210", "endLine": 109, "endColumn": 52}, {"ruleId": "208", "severity": 1, "message": "211", "line": 203, "column": 55, "nodeType": null, "messageId": "210", "endLine": 203, "endColumn": 60}, {"ruleId": "215", "severity": 1, "message": "216", "line": 71, "column": 27, "nodeType": "217", "messageId": "218", "endLine": 71, "endColumn": 30, "suggestions": "219"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 113, "column": 25, "nodeType": "217", "messageId": "218", "endLine": 113, "endColumn": 28, "suggestions": "220"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 165, "column": 25, "nodeType": "217", "messageId": "218", "endLine": 165, "endColumn": 28, "suggestions": "221"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 218, "column": 25, "nodeType": "217", "messageId": "218", "endLine": 218, "endColumn": 28, "suggestions": "222"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 18, "column": 46, "nodeType": "217", "messageId": "218", "endLine": 18, "endColumn": 49, "suggestions": "223"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 18, "column": 56, "nodeType": "217", "messageId": "218", "endLine": 18, "endColumn": 59, "suggestions": "224"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 30, "column": 46, "nodeType": "217", "messageId": "218", "endLine": 30, "endColumn": 49, "suggestions": "225"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 30, "column": 56, "nodeType": "217", "messageId": "218", "endLine": 30, "endColumn": 59, "suggestions": "226"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 145, "column": 46, "nodeType": "217", "messageId": "218", "endLine": 145, "endColumn": 49, "suggestions": "227"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 130, "column": 34, "nodeType": "217", "messageId": "218", "endLine": 130, "endColumn": 37, "suggestions": "228"}, {"ruleId": "215", "severity": 1, "message": "216", "line": 282, "column": 13, "nodeType": "217", "messageId": "218", "endLine": 282, "endColumn": 16, "suggestions": "229"}, "@typescript-eslint/no-unused-vars", "'fadeInUp' is defined but never used.", "unusedVar", "'index' is defined but never used.", "'useEffect' is defined but never used.", "'Image' is defined but never used.", "'PortfolioCard' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["230", "231"], ["232", "233"], ["234", "235"], ["236", "237"], ["238", "239"], ["240", "241"], ["242", "243"], ["244", "245"], ["246", "247"], ["248", "249"], ["250", "251"], {"messageId": "252", "fix": "253", "desc": "254"}, {"messageId": "255", "fix": "256", "desc": "257"}, {"messageId": "252", "fix": "258", "desc": "254"}, {"messageId": "255", "fix": "259", "desc": "257"}, {"messageId": "252", "fix": "260", "desc": "254"}, {"messageId": "255", "fix": "261", "desc": "257"}, {"messageId": "252", "fix": "262", "desc": "254"}, {"messageId": "255", "fix": "263", "desc": "257"}, {"messageId": "252", "fix": "264", "desc": "254"}, {"messageId": "255", "fix": "265", "desc": "257"}, {"messageId": "252", "fix": "266", "desc": "254"}, {"messageId": "255", "fix": "267", "desc": "257"}, {"messageId": "252", "fix": "268", "desc": "254"}, {"messageId": "255", "fix": "269", "desc": "257"}, {"messageId": "252", "fix": "270", "desc": "254"}, {"messageId": "255", "fix": "271", "desc": "257"}, {"messageId": "252", "fix": "272", "desc": "254"}, {"messageId": "255", "fix": "273", "desc": "257"}, {"messageId": "252", "fix": "274", "desc": "254"}, {"messageId": "255", "fix": "275", "desc": "257"}, {"messageId": "252", "fix": "276", "desc": "254"}, {"messageId": "255", "fix": "277", "desc": "257"}, "suggestUnknown", {"range": "278", "text": "279"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "280", "text": "281"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "282", "text": "279"}, {"range": "283", "text": "281"}, {"range": "284", "text": "279"}, {"range": "285", "text": "281"}, {"range": "286", "text": "279"}, {"range": "287", "text": "281"}, {"range": "288", "text": "279"}, {"range": "289", "text": "281"}, {"range": "290", "text": "279"}, {"range": "291", "text": "281"}, {"range": "292", "text": "279"}, {"range": "293", "text": "281"}, {"range": "294", "text": "279"}, {"range": "295", "text": "281"}, {"range": "296", "text": "279"}, {"range": "297", "text": "281"}, {"range": "298", "text": "279"}, {"range": "299", "text": "281"}, {"range": "300", "text": "279"}, {"range": "301", "text": "281"}, [2102, 2105], "unknown", [2102, 2105], "never", [3277, 3280], [3277, 3280], [4815, 4818], [4815, 4818], [6370, 6373], [6370, 6373], [402, 405], [402, 405], [412, 415], [412, 415], [705, 708], [705, 708], [715, 718], [715, 718], [4167, 4170], [4167, 4170], [2413, 2416], [2413, 2416], [5275, 5278], [5275, 5278]]