[{"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\layout.tsx": "1", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\page.tsx": "2", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Footer.tsx": "3", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Header.tsx": "4", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\About.tsx": "5", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Contact.tsx": "6", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Hero.tsx": "7", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Portfolio.tsx": "8", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Services.tsx": "9", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Testimonials.tsx": "10", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Button.tsx": "11", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Card.tsx": "12", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\index.ts": "13", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Input.tsx": "14", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Modal.tsx": "15", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\navigation.ts": "16", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\portfolio.ts": "17", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\services.ts": "18", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\testimonials.ts": "19", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\lib\\animations\\framer.ts": "20", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\lib\\animations\\gsap.ts": "21", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\lib\\utils.ts": "22", "E:\\Augment Code Testing\\tera-works-portfolio\\src\\types\\index.ts": "23"}, {"size": 2290, "mtime": 1750437298514, "results": "24", "hashOfConfig": "25"}, {"size": 502, "mtime": 1750437657569, "results": "26", "hashOfConfig": "25"}, {"size": 10674, "mtime": 1750437161917, "results": "27", "hashOfConfig": "25"}, {"size": 5231, "mtime": 1750438231111, "results": "28", "hashOfConfig": "25"}, {"size": 9394, "mtime": 1750438384454, "results": "29", "hashOfConfig": "25"}, {"size": 13014, "mtime": 1750438405728, "results": "30", "hashOfConfig": "25"}, {"size": 8964, "mtime": 1750438126698, "results": "31", "hashOfConfig": "25"}, {"size": 10464, "mtime": 1750438032563, "results": "32", "hashOfConfig": "25"}, {"size": 9977, "mtime": 1750437426034, "results": "33", "hashOfConfig": "25"}, {"size": 9237, "mtime": 1750438093069, "results": "34", "hashOfConfig": "25"}, {"size": 3104, "mtime": 1750437003080, "results": "35", "hashOfConfig": "25"}, {"size": 5620, "mtime": 1750437032669, "results": "36", "hashOfConfig": "25"}, {"size": 239, "mtime": 1750437089568, "results": "37", "hashOfConfig": "25"}, {"size": 6829, "mtime": 1750437062871, "results": "38", "hashOfConfig": "25"}, {"size": 2750, "mtime": 1750437080785, "results": "39", "hashOfConfig": "25"}, {"size": 3740, "mtime": 1750436983592, "results": "40", "hashOfConfig": "25"}, {"size": 8653, "mtime": 1750436903110, "results": "41", "hashOfConfig": "25"}, {"size": 7753, "mtime": 1750436935929, "results": "42", "hashOfConfig": "25"}, {"size": 4454, "mtime": 1750436961528, "results": "43", "hashOfConfig": "25"}, {"size": 4965, "mtime": 1750436805510, "results": "44", "hashOfConfig": "25"}, {"size": 5461, "mtime": 1750436776301, "results": "45", "hashOfConfig": "25"}, {"size": 6866, "mtime": 1750436836083, "results": "46", "hashOfConfig": "25"}, {"size": 5672, "mtime": 1750436864826, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "42mhxg", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\layout.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\page.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Footer.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\layout\\Header.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\About.tsx", ["117", "118"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Contact.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Hero.tsx", ["119", "120", "121"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Portfolio.tsx", ["122", "123"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Services.tsx", ["124"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\sections\\Testimonials.tsx", ["125", "126"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Button.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Card.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\index.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Input.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\components\\ui\\Modal.tsx", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\navigation.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\portfolio.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\services.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\data\\testimonials.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\lib\\animations\\framer.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\lib\\animations\\gsap.ts", [], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\lib\\utils.ts", ["127", "128", "129", "130", "131"], [], "E:\\Augment Code Testing\\tera-works-portfolio\\src\\types\\index.ts", ["132", "133"], [], {"ruleId": "134", "severity": 1, "message": "135", "line": 16, "column": 10, "nodeType": null, "messageId": "136", "endLine": 16, "endColumn": 18}, {"ruleId": "134", "severity": 1, "message": "137", "line": 221, "column": 36, "nodeType": null, "messageId": "136", "endLine": 221, "endColumn": 41}, {"ruleId": "134", "severity": 1, "message": "138", "line": 3, "column": 25, "nodeType": null, "messageId": "136", "endLine": 3, "endColumn": 34}, {"ruleId": "134", "severity": 1, "message": "139", "line": 4, "column": 8, "nodeType": null, "messageId": "136", "endLine": 4, "endColumn": 13}, {"ruleId": "134", "severity": 1, "message": "135", "line": 10, "column": 10, "nodeType": null, "messageId": "136", "endLine": 10, "endColumn": 18}, {"ruleId": "134", "severity": 1, "message": "140", "line": 7, "column": 10, "nodeType": null, "messageId": "136", "endLine": 7, "endColumn": 23}, {"ruleId": "134", "severity": 1, "message": "137", "line": 106, "column": 45, "nodeType": null, "messageId": "136", "endLine": 106, "endColumn": 50}, {"ruleId": "134", "severity": 1, "message": "137", "line": 68, "column": 43, "nodeType": null, "messageId": "136", "endLine": 68, "endColumn": 48}, {"ruleId": "134", "severity": 1, "message": "137", "line": 109, "column": 47, "nodeType": null, "messageId": "136", "endLine": 109, "endColumn": 52}, {"ruleId": "134", "severity": 1, "message": "137", "line": 203, "column": 55, "nodeType": null, "messageId": "136", "endLine": 203, "endColumn": 60}, {"ruleId": "141", "severity": 1, "message": "142", "line": 18, "column": 46, "nodeType": "143", "messageId": "144", "endLine": 18, "endColumn": 49, "suggestions": "145"}, {"ruleId": "141", "severity": 1, "message": "142", "line": 18, "column": 56, "nodeType": "143", "messageId": "144", "endLine": 18, "endColumn": 59, "suggestions": "146"}, {"ruleId": "141", "severity": 1, "message": "142", "line": 30, "column": 46, "nodeType": "143", "messageId": "144", "endLine": 30, "endColumn": 49, "suggestions": "147"}, {"ruleId": "141", "severity": 1, "message": "142", "line": 30, "column": 56, "nodeType": "143", "messageId": "144", "endLine": 30, "endColumn": 59, "suggestions": "148"}, {"ruleId": "141", "severity": 1, "message": "142", "line": 145, "column": 46, "nodeType": "143", "messageId": "144", "endLine": 145, "endColumn": 49, "suggestions": "149"}, {"ruleId": "141", "severity": 1, "message": "142", "line": 130, "column": 34, "nodeType": "143", "messageId": "144", "endLine": 130, "endColumn": 37, "suggestions": "150"}, {"ruleId": "141", "severity": 1, "message": "142", "line": 282, "column": 13, "nodeType": "143", "messageId": "144", "endLine": 282, "endColumn": 16, "suggestions": "151"}, "@typescript-eslint/no-unused-vars", "'fadeInUp' is defined but never used.", "unusedVar", "'index' is defined but never used.", "'useEffect' is defined but never used.", "'Image' is defined but never used.", "'PortfolioCard' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["152", "153"], ["154", "155"], ["156", "157"], ["158", "159"], ["160", "161"], ["162", "163"], ["164", "165"], {"messageId": "166", "fix": "167", "desc": "168"}, {"messageId": "169", "fix": "170", "desc": "171"}, {"messageId": "166", "fix": "172", "desc": "168"}, {"messageId": "169", "fix": "173", "desc": "171"}, {"messageId": "166", "fix": "174", "desc": "168"}, {"messageId": "169", "fix": "175", "desc": "171"}, {"messageId": "166", "fix": "176", "desc": "168"}, {"messageId": "169", "fix": "177", "desc": "171"}, {"messageId": "166", "fix": "178", "desc": "168"}, {"messageId": "169", "fix": "179", "desc": "171"}, {"messageId": "166", "fix": "180", "desc": "168"}, {"messageId": "169", "fix": "181", "desc": "171"}, {"messageId": "166", "fix": "182", "desc": "168"}, {"messageId": "169", "fix": "183", "desc": "171"}, "suggestUnknown", {"range": "184", "text": "185"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "186", "text": "187"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "188", "text": "185"}, {"range": "189", "text": "187"}, {"range": "190", "text": "185"}, {"range": "191", "text": "187"}, {"range": "192", "text": "185"}, {"range": "193", "text": "187"}, {"range": "194", "text": "185"}, {"range": "195", "text": "187"}, {"range": "196", "text": "185"}, {"range": "197", "text": "187"}, {"range": "198", "text": "185"}, {"range": "199", "text": "187"}, [402, 405], "unknown", [402, 405], "never", [412, 415], [412, 415], [705, 708], [705, 708], [715, 718], [715, 718], [4167, 4170], [4167, 4170], [2413, 2416], [2413, 2416], [5275, 5278], [5275, 5278]]