// Portfolio types
export interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  longDescription?: string;
  category: 'web-development' | 'meta-advertising' | 'booking-system' | 'all';
  technologies: string[];
  images: {
    thumbnail: string;
    gallery: string[];
  };
  url?: string;
  githubUrl?: string;
  featured: boolean;
  completedAt: Date;
  client?: string;
  results?: {
    metric: string;
    value: string;
    description: string;
  }[];
}

// Service types
export interface Service {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  icon: string;
  features: string[];
  pricing?: {
    type: 'fixed' | 'hourly' | 'project';
    startingPrice?: number;
    currency: string;
  };
  deliverables: string[];
  timeline: string;
  category: 'web-development' | 'meta-advertising' | 'consulting';
}

// Testimonial types
export interface Testimonial {
  id: string;
  name: string;
  position: string;
  company: string;
  avatar?: string;
  content: string;
  rating: number;
  date: Date;
  projectType: string;
}

// Team member types
export interface TeamMember {
  id: string;
  name: string;
  position: string;
  bio: string;
  avatar: string;
  skills: string[];
  social: {
    linkedin?: string;
    twitter?: string;
    github?: string;
    email: string;
  };
}

// Blog post types
export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  publishedAt: Date;
  updatedAt?: Date;
  category: string;
  tags: string[];
  featuredImage: string;
  readingTime: number;
  slug: string;
  published: boolean;
}

// Contact form types
export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject: string;
  message: string;
  services: string[];
  budget?: string;
  timeline?: string;
  source?: string;
}

// Newsletter types
export interface NewsletterSubscription {
  email: string;
  name?: string;
  interests: string[];
  source: string;
}

// Navigation types
export interface NavigationItem {
  label: string;
  href: string;
  external?: boolean;
  children?: NavigationItem[];
}

// SEO types
export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  ogImage?: string;
  canonical?: string;
  noindex?: boolean;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Form validation types
export interface FormErrors {
  [key: string]: string | undefined;
}

export interface FormState {
  isSubmitting: boolean;
  isSubmitted: boolean;
  errors: FormErrors;
}

// Animation types
export interface AnimationConfig {
  duration?: number;
  delay?: number;
  ease?: string;
  stagger?: number;
}

// Theme types
export interface ThemeConfig {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
  fonts: {
    heading: string;
    body: string;
  };
  spacing: {
    [key: string]: string;
  };
}

// Component props types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  href?: string;
  external?: boolean;
}

export interface CardProps extends BaseComponentProps {
  title?: string;
  description?: string;
  image?: string;
  href?: string;
  hover?: boolean;
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
}

// Statistics types
export interface Statistic {
  id: string;
  label: string;
  value: number;
  suffix?: string;
  prefix?: string;
  description?: string;
  icon?: string;
}

// FAQ types
export interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
}

// Social media types
export interface SocialLink {
  platform: string;
  url: string;
  icon: string;
  label: string;
}

// Meta advertising specific types
export interface AdCampaign {
  id: string;
  name: string;
  client: string;
  platform: 'facebook' | 'instagram' | 'both';
  objective: string;
  budget: number;
  duration: {
    start: Date;
    end: Date;
  };
  results: {
    impressions: number;
    clicks: number;
    conversions: number;
    cost: number;
    roas: number;
  };
  status: 'active' | 'paused' | 'completed';
}

// Website project types
export interface WebsiteProject {
  id: string;
  name: string;
  client: string;
  type: 'business' | 'ecommerce' | 'portfolio' | 'booking' | 'custom';
  technologies: string[];
  features: string[];
  timeline: {
    start: Date;
    end: Date;
    phases: {
      name: string;
      duration: number;
      completed: boolean;
    }[];
  };
  budget: number;
  status: 'planning' | 'development' | 'testing' | 'deployed' | 'maintenance';
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// Loading states
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// Generic utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
