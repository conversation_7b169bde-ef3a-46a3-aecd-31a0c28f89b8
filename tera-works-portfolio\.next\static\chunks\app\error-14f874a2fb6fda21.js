(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[39],{671:(e,r,s)=>{"use strict";s.d(r,{$n:()=>n.default,TM:()=>t.Textarea,_f:()=>a.TestimonialCard,l6:()=>t.Select,mB:()=>a.ServiceCard,pd:()=>t.default});var n=s(3741),a=s(7703),t=s(3915);s(6440)},1901:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l});var n=s(5155),a=s(2115),t=s(671);function l(e){let{error:r,reset:s}=e;return(0,a.useEffect)(()=>{console.error(r)},[r]),(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,n.jsxs)("div",{className:"text-center max-w-md mx-auto px-4",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,n.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Something went wrong!"}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"We apologize for the inconvenience. An error occurred while loading this page."})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(t.$n,{onClick:s,variant:"primary",className:"w-full",children:"Try Again"}),(0,n.jsx)(t.$n,{href:"/",variant:"outline",className:"w-full",children:"Go Home"})]})]})})}},5114:(e,r,s)=>{Promise.resolve().then(s.bind(s,1901))}},e=>{var r=r=>e(e.s=r);e.O(0,[874,44,644,441,684,358],()=>r(5114)),_N_E=e.O()}]);