(()=>{var e={};e.id=746,e.ids=[746],e.modules={568:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>u});var o=r(6559),n=r(8088),a=r(7719),i=r(2190);async function u(e){try{let t=await e.json(),{name:r,email:s,message:o}=t;if(!r||!s||!o)return i.NextResponse.json({error:"Missing required fields"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s))return i.NextResponse.json({error:"Invalid email address"},{status:400});return console.log("Contact form submission:",{name:r,email:s,phone:t.phone,company:t.company,service:t.service,budget:t.budget,message:o,timestamp:new Date().toISOString()}),await new Promise(e=>setTimeout(e,1e3)),i.NextResponse.json({success:!0,message:"Thank you for your message. We'll get back to you within 24 hours!"},{status:200})}catch(e){return console.error("Contact form error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(){return i.NextResponse.json({message:"Contact API endpoint is working"},{status:200})}let c=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/contact/route",pathname:"/api/contact",filename:"route",bundlePath:"app/api/contact/route"},resolvedPagePath:"E:\\Augment Code Testing\\tera-works-portfolio\\src\\app\\api\\contact\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=c;function x(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(568));module.exports=s})();