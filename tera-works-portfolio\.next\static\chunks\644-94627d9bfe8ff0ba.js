"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[644],{3741:(e,a,t)=>{t.d(a,{default:()=>c});var s=t(5155);t(2115);var r=t(6874),l=t.n(r),i=t(6408),n=t(9434),o=t(4910);let c=e=>{let{children:a,className:t,variant:r="primary",size:c="md",disabled:d=!1,loading:m=!1,onClick:x,type:u="button",href:h,external:p=!1,...y}=e,f=(0,n.cn)("inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500",secondary:"bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500",outline:"border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500",ghost:"text-primary-600 hover:bg-primary-50 focus:ring-primary-500"}[r],{sm:"px-3 py-2 text-sm rounded-md",md:"px-4 py-2 text-base rounded-lg",lg:"px-6 py-3 text-lg rounded-lg"}[c],t),v=(0,s.jsxs)(s.Fragment,{children:[m&&(0,s.jsx)(()=>(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),{}),a]});return h?p?(0,s.jsx)(i.P.a,{href:h,target:"_blank",rel:"noopener noreferrer",className:f,variants:o.ru,initial:"idle",whileHover:"hover",whileTap:"tap",...y,children:v}):(0,s.jsx)(l(),{href:h,className:f,...y,children:(0,s.jsx)(i.P.span,{variants:o.ru,initial:"idle",whileHover:"hover",whileTap:"tap",className:"flex items-center justify-center w-full h-full",children:v})}):(0,s.jsx)(i.P.button,{type:u,className:f,disabled:d||m,onClick:x,variants:o.ru,initial:"idle",whileHover:d||m?"idle":"hover",whileTap:d||m?"idle":"tap",...y,children:v})}},3915:(e,a,t)=>{t.d(a,{Select:()=>d,Textarea:()=>c,default:()=>m});var s=t(5155),r=t(2115),l=t(6408),i=t(9434),n=t(4910);let o=(0,r.forwardRef)((e,a)=>{let{className:t,label:o,error:c,helperText:d,variant:m="default",type:x="text",id:u,...h}=e,[p,y]=(0,r.useState)(!1),[f,v]=(0,r.useState)(!1),g=u||"input-".concat(Math.random().toString(36).substr(2,9)),b=(0,i.cn)("w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed",c?"border-red-300 focus:ring-red-500":"border-gray-300 focus:border-primary-500","floating"===m&&"pt-6 pb-2",t),j=e=>{var a;y(!0),null==(a=h.onFocus)||a.call(h,e)},N=e=>{var a;y(!1),v(""!==e.target.value),null==(a=h.onBlur)||a.call(h,e)},w=e=>{var a;v(""!==e.target.value),null==(a=h.onChange)||a.call(h,e)};return"floating"===m?(0,s.jsxs)("div",{className:"relative mb-6",children:[(0,s.jsx)(l.P.input,{ref:a,type:x,id:g,className:b,onFocus:j,onBlur:N,onChange:w,variants:n.Xy,whileFocus:"focus",...h}),o&&(0,s.jsx)(l.P.label,{htmlFor:g,variants:n.pP,animate:p||f?"active":"default",className:"absolute left-3 top-2 pointer-events-none text-sm font-medium",children:o}),c&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:c}),d&&!c&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:d})]}):(0,s.jsxs)("div",{className:"mb-4",children:[o&&(0,s.jsx)("label",{htmlFor:g,className:"block text-sm font-medium text-gray-700 mb-1",children:o}),(0,s.jsx)(l.P.input,{ref:a,type:x,id:g,className:b,onFocus:j,onBlur:N,onChange:w,variants:n.Xy,whileFocus:"focus",...h}),c&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:c}),d&&!c&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:d})]})});o.displayName="Input";let c=(0,r.forwardRef)((e,a)=>{let{className:t,label:r,error:o,helperText:c,id:d,...m}=e,x=d||"textarea-".concat(Math.random().toString(36).substr(2,9)),u=(0,i.cn)("w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed resize-vertical min-h-[100px]",o?"border-red-300 focus:ring-red-500":"border-gray-300 focus:border-primary-500",t);return(0,s.jsxs)("div",{className:"mb-4",children:[r&&(0,s.jsx)("label",{htmlFor:x,className:"block text-sm font-medium text-gray-700 mb-1",children:r}),(0,s.jsx)(l.P.textarea,{ref:a,id:x,className:u,variants:n.Xy,whileFocus:"focus",...m}),o&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o}),c&&!o&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:c})]})});c.displayName="Textarea";let d=(0,r.forwardRef)((e,a)=>{let{className:t,label:r,error:o,helperText:c,options:d,id:m,...x}=e,u=m||"select-".concat(Math.random().toString(36).substr(2,9)),h=(0,i.cn)("w-full px-3 py-2 border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed bg-white",o?"border-red-300 focus:ring-red-500":"border-gray-300 focus:border-primary-500",t);return(0,s.jsxs)("div",{className:"mb-4",children:[r&&(0,s.jsx)("label",{htmlFor:u,className:"block text-sm font-medium text-gray-700 mb-1",children:r}),(0,s.jsx)(l.P.select,{ref:a,id:u,className:h,variants:n.Xy,whileFocus:"focus",...x,children:d.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))}),o&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:o}),c&&!o&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:c})]})});d.displayName="Select";let m=o},4910:(e,a,t)=>{t.d(a,{CV:()=>p,Rf:()=>l,Tz:()=>m,VM:()=>h,Xy:()=>x,Ym:()=>d,bK:()=>r,c$:()=>o,fP:()=>n,jE:()=>c,pP:()=>u,ru:()=>i,tE:()=>s});let s={initial:{opacity:0,y:60},animate:{opacity:1,y:0,transition:{duration:.6,ease:"easeOut"}},exit:{opacity:0,y:-60}},r={initial:{},animate:{transition:{staggerChildren:.1,delayChildren:.3}}},l={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.5,ease:"easeOut"}}},i={idle:{scale:1},hover:{scale:1.05,transition:{duration:.2,ease:"easeOut"}},tap:{scale:.95,transition:{duration:.1}}},n={idle:{y:0,scale:1,boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)"},hover:{y:-8,scale:1.02,boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1)",transition:{duration:.3,ease:"easeOut"}}},o={hidden:{opacity:0},visible:{opacity:1}},c={hidden:{opacity:0,scale:.8,y:50},visible:{opacity:1,scale:1,y:0,transition:{type:"spring",damping:25,stiffness:300}},exit:{opacity:0,scale:.8,y:50,transition:{duration:.2}}},d={closed:{opacity:0,height:0,transition:{duration:.3,ease:"easeInOut"}},open:{opacity:1,height:"auto",transition:{duration:.3,ease:"easeInOut",staggerChildren:.1,delayChildren:.1}}},m={closed:{opacity:0,x:-20},open:{opacity:1,x:0,transition:{duration:.3,ease:"easeOut"}}},x={focus:{scale:1.02,transition:{duration:.2,ease:"easeOut"}},blur:{scale:1,transition:{duration:.2,ease:"easeOut"}}},u={default:{y:0,scale:1,color:"#6B7280"},active:{y:-24,scale:.8,color:"#3B82F6",transition:{duration:.2,ease:"easeOut"}}},h={hidden:{opacity:0},show:{opacity:1,transition:{staggerChildren:.1}}},p={hidden:{opacity:0,scale:.8},show:{opacity:1,scale:1,transition:{duration:.5,ease:"easeOut"}}}},6440:(e,a,t)=>{t.d(a,{default:()=>d});var s=t(5155),r=t(2115),l=t(760),i=t(6408),n=t(4500),o=t(9434),c=t(4910);let d=e=>{let{isOpen:a,onClose:t,children:d,title:m,size:x="md",closeOnOverlayClick:u=!0,closeOnEscape:h=!0,className:p}=e;return(0,r.useEffect)(()=>{let e=e=>{h&&"Escape"===e.key&&t()};return a&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[a,h,t]),(0,s.jsx)(l.N,{children:a&&(0,s.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,s.jsx)(i.P.div,{variants:c.c$,initial:"hidden",animate:"visible",exit:"hidden",className:"fixed inset-0 bg-black bg-opacity-50",onClick:e=>{u&&e.target===e.currentTarget&&t()}}),(0,s.jsxs)(i.P.div,{variants:c.jE,initial:"hidden",animate:"visible",exit:"exit",className:(0,o.cn)("relative bg-white rounded-lg shadow-xl w-full",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[x],p),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[m&&(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:m}),(0,s.jsx)("button",{onClick:t,className:"p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-full hover:bg-gray-100","aria-label":"Close modal",children:(0,s.jsx)(n.A,{className:"w-5 h-5"})})]}),(0,s.jsx)("div",{className:"p-6",children:d})]})]})})}},7703:(e,a,t)=>{t.d(a,{PortfolioCard:()=>x,ServiceCard:()=>m,TestimonialCard:()=>u,default:()=>h});var s=t(5155);t(2115);var r=t(6874),l=t.n(r),i=t(6766),n=t(6408),o=t(9434),c=t(4910);let d=e=>{let{children:a,className:t,title:r,description:d,image:m,href:x,hover:u=!0,...h}=e,p=(0,o.cn)("bg-white rounded-lg shadow-md overflow-hidden transition-shadow duration-300",t),y=(0,s.jsxs)(n.P.div,{className:p,variants:u?c.fP:void 0,initial:"idle",whileHover:u?"hover":"idle",...h,children:[m&&(0,s.jsx)("div",{className:"relative h-48 w-full",children:(0,s.jsx)(i.default,{src:m,alt:r||"Card image",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),(0,s.jsxs)("div",{className:"p-6",children:[r&&(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:r}),d&&(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:d}),a]})]});return x?(0,s.jsx)(l(),{href:x,className:"block",children:y}):y},m=e=>{let{title:a,description:t,icon:r,features:l,href:i,className:n}=e;return(0,s.jsxs)(d,{title:a,description:t,href:i,className:(0,o.cn)("h-full",n),children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("span",{className:"text-4xl mr-3",children:r}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:a})]}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:t}),(0,s.jsx)("ul",{className:"space-y-2",children:l.slice(0,4).map((e,a)=>(0,s.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,s.jsx)("svg",{className:"w-4 h-4 text-green-500 mr-2 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),e]},a))})]})},x=e=>{let{title:a,description:t,image:r,technologies:l,href:i,className:n}=e;return(0,s.jsx)(d,{title:a,description:t,image:r,href:i,className:(0,o.cn)("h-full",n),children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mt-4",children:[l.slice(0,3).map((e,a)=>(0,s.jsx)("span",{className:"px-2 py-1 bg-primary-100 text-primary-800 text-xs rounded-full",children:e},a)),l.length>3&&(0,s.jsxs)("span",{className:"px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full",children:["+",l.length-3," more"]})]})})},u=e=>{let{name:a,position:t,company:r,content:l,avatar:n,rating:c,className:m}=e;return(0,s.jsxs)(d,{className:(0,o.cn)("h-full",m),hover:!1,children:[(0,s.jsx)("div",{className:"flex items-center mb-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,a)=>(0,s.jsx)("svg",{className:(0,o.cn)("w-5 h-5",a<c?"text-yellow-400":"text-gray-300"),fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},a))}),(0,s.jsxs)("blockquote",{className:"text-gray-600 mb-4 italic",children:['"',l,'"']}),(0,s.jsxs)("div",{className:"flex items-center",children:[n&&(0,s.jsx)("div",{className:"relative w-12 h-12 mr-4",children:(0,s.jsx)(i.default,{src:n,alt:a,fill:!0,className:"rounded-full object-cover"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-semibold text-gray-900",children:a}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[t," at ",r]})]})]})]})},h=d},9434:(e,a,t)=>{t.d(a,{cn:()=>r});var s=t(2596);function r(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,s.$)(a)}}}]);